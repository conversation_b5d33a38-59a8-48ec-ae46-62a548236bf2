# 自动重登机制测试说明

## 实现概述

基于video.vue的重登机制，我们创建了一个通用的autoRelogin mixin，并在所有主要页面中应用了这个机制。

## 已实现的功能

### 1. 通用重登mixin (`appWeb/src/mixins/autoRelogin.js`)
- 提供刷新后自动使用session重登的功能
- 防止http在websocket之前发送
- 包含重登状态管理
- 提供可重写的回调方法

### 2. 已应用重登机制的页面
- ✅ `appWeb/src/views/deviceMgt/index.vue` - 设备管理页面
- ✅ `appWeb/src/views/userMgt/index.vue` - 用户管理页面  
- ✅ `appWeb/src/views/systemSetting/index.vue` - 系统设置页面
- ✅ `appWeb/src/views/videoMgt/index.vue` - 视频管理页面
- ✅ `appWeb/src/views/operationLogs/index.vue` - 操作日志页面
- ✅ `appWeb/src/views/video.vue` - 视频页面（原有实现）

## 核心机制

### 1. 自动重登流程
```javascript
// 1. 页面创建时检查sessionStorage.home标志
if (sessionStorage.home && sessionStorage.home === '1') {
  // 刷新场景 - 执行自动重登
  performAutoRelogin()
} else {
  // 正常登录场景 - 检查websocket连接
  checkWebsocketConnection()
}

// 2. 自动重登过程
- 关闭现有websocket连接
- 使用refreshReconnection类型重新初始化websocket
- 监听cli_miru_login_rsp响应
- 更新token和store状态
- 调用页面特定的回调方法
```

### 2. 防止HTTP在WebSocket之前发送
```javascript
// 提供安全的HTTP请求方法
canSendHttpRequest() {
  return !this.isReloginInProgress && this.isReloginCompleted
}

safeHttpRequest(requestFn, ...args) {
  // 确保websocket连接建立后再发送HTTP请求
}
```

### 3. 页面特定回调
每个页面可以重写以下方法：
- `onReloginSuccess(response)` - 重登成功后执行
- `onWebsocketReady()` - websocket准备就绪后执行

## 测试步骤

### 1. 基本功能测试
1. 正常登录系统
2. 访问各个页面（deviceMgt, userMgt, systemSetting, videoMgt, operationLogs）
3. 在每个页面刷新浏览器
4. 验证页面能自动重登并正常加载数据

### 2. 重登机制测试
1. 打开浏览器开发者工具的Network和Console面板
2. 访问任意页面
3. 刷新页面
4. 观察Console输出，应该看到：
   - "刷新 - 开始自动重登"
   - "自动重登成功"
   - "[页面名称]页面重登成功"
   - "[页面名称]页面websocket准备就绪"

### 3. HTTP请求时序测试
1. 刷新页面
2. 观察Network面板
3. 确认websocket连接建立后才发送HTTP请求
4. 验证没有401错误或其他认证相关错误

## 注意事项

1. **sessionStorage.home标志**: 每个页面在mounted时都会设置`sessionStorage.home = '1'`，这是触发重登机制的关键
2. **websocket依赖**: 所有页面都依赖websocket连接，重登机制确保websocket优先建立
3. **状态管理**: mixin提供了`isReloginInProgress`和`isReloginCompleted`状态来控制请求时序
4. **回调机制**: 每个页面都实现了特定的回调方法来处理重登成功后的数据加载

## 与video.vue的一致性

我们的实现完全基于video.vue的重登机制：
- 使用相同的sessionStorage.home检查逻辑
- 使用相同的refreshReconnection类型
- 监听相同的cli_miru_login_rsp事件
- 执行相同的token更新操作

这确保了所有页面的重登行为与video.vue保持一致。
