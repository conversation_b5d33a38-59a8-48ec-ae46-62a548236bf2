import { setToken } from '@/utils/auth'
import tabManager from '@/utils/tabManager'

/**
 * 自动重登mixin
 * 基于video.vue的重登机制，提供刷新后自动使用session重登的功能
 * 防止http在websocket之前发送
 */
export default {
  data() {
    return {
      // 是否正在重登过程中
      isReloginInProgress: false,
      // 是否已经完成重登
      isReloginCompleted: false
    }
  },

  created() {
    this.handleAutoRelogin()
  },

  mounted() {
    // 设置sessionStorage.home标志，用于标识页面已经加载过
    // 这样刷新时就能触发自动重登机制
    sessionStorage.home = '1'
  },

  methods: {
    /**
     * 处理自动重登逻辑
     */
    handleAutoRelogin() {
      console.log('sessionStorage.home：' + sessionStorage.home)

      if (sessionStorage.home && sessionStorage.home === '1') {
        console.log('刷新 - 开始自动重登')
        this.performAutoRelogin()
      } else {
        console.log('登录进来 - 检查websocket连接状态')
        this.checkWebsocketConnection()
      }
    },

    /**
     * 执行自动重登
     */
    performAutoRelogin() {
      if (this.$websocket) {
        this.$websocket.closeWebsocket()
      }

      // 刷新界面websocket断开重连
      if (sessionStorage.username && sessionStorage.username !== '') {
        this.isReloginInProgress = true
        this.$websocket.initWebSocket('refreshReconnection')

        // 监听重登响应
        this.$bus.$once('cli_miru_login_rsp', response => {
          console.log('自动重登成功', response)

          // 重新设置所有必要的存储数据
          this.updateStorageAfterRelogin(response)

          this.isReloginInProgress = false
          this.isReloginCompleted = true

          // 重登成功后的回调
          this.onReloginSuccess(response)
        })
      }
    },

    /**
     * 检查websocket连接状态
     */
    checkWebsocketConnection() {
      if (!this.$websocket || !this.$websocket.socket) {
        console.log('websocket未初始化')
        return
      }

      // 0 - 表示连接尚未建立，1 - 表示连接已建立，可以进行通信，2 - 表示连接正在进行关闭，3 - 表示连接已经关闭或者连接不能打开
      const state = this.$websocket.socket.readyState
      console.log('websocket状态:', state)

      if (state === 0) {
        setTimeout(() => {
          this.checkWebsocketConnection()
        }, 300)
      } else if (state === 1) {
        this.isReloginCompleted = true
        this.onWebsocketReady()
      }
    },

    /**
     * 重登成功后更新存储数据
     * 确保sessionStorage和localStorage中的数据与重登响应一致
     */
    updateStorageAfterRelogin(response) {
      console.log('更新重登后的存储数据', response.body)

      // 更新store和sessionStorage中的token
      this.$store.commit('SET_TOKEN', response.body.sessid)
      setToken(response.body.sessid)

      // 重新设置sessionStorage中的用户信息
      if (response.body.account_type !== undefined) {
        sessionStorage.account_type = response.body.account_type
      }
      if (response.body.account) {
        sessionStorage.username = response.body.account
      }
      if (response.body.alias) {
        sessionStorage.account_alias = response.body.alias
      }
      if (response.body.srs_http) {
        sessionStorage.videoUrl = JSON.stringify(response.body.srs_http)
      }

      // 使用tabManager保存sessid和account到localStorage供其他Tab共享
      // 这是防止其他tab无法自动登录的关键步骤
      tabManager.setSessid(response.body.sessid, response.body.account)

      console.log('存储数据更新完成:', {
        sessid: response.body.sessid,
        account: response.body.account,
        account_type: response.body.account_type,
        alias: response.body.alias
      })
    },

    /**
     * 重登成功后的回调 - 子组件可以重写此方法
     */
    onReloginSuccess(response) {
      console.log('重登成功，子组件可以重写此方法进行后续操作')
      // 子组件可以重写此方法来执行特定的初始化逻辑
      this.onWebsocketReady()
    },

    /**
     * websocket准备就绪后的回调 - 子组件可以重写此方法
     */
    onWebsocketReady() {
      console.log('websocket准备就绪，子组件可以重写此方法进行数据加载')
      // 子组件可以重写此方法来执行数据加载等操作
    },

    /**
     * 检查是否可以发送HTTP请求
     * 确保websocket连接已建立且重登已完成
     */
    canSendHttpRequest() {
      if (this.isReloginInProgress) {
        console.log('重登进行中，暂缓HTTP请求')
        return false
      }

      if (!this.isReloginCompleted) {
        console.log('重登未完成，暂缓HTTP请求')
        return false
      }

      return true
    },

    /**
     * 安全的HTTP请求方法
     * 确保在websocket连接建立后再发送HTTP请求
     */
    safeHttpRequest(requestFn, ...args) {
      if (this.canSendHttpRequest()) {
        return requestFn.apply(this, args)
      } else {
        // 等待重登完成后再执行请求
        const checkAndExecute = () => {
          if (this.canSendHttpRequest()) {
            return requestFn.apply(this, args)
          } else {
            setTimeout(checkAndExecute, 100)
          }
        }
        setTimeout(checkAndExecute, 100)
      }
    }
  }
}
