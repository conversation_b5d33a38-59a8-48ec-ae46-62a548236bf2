# Video.vue重登机制优化建议

## 当前状态

video.vue中的重登逻辑（第683-687行）：
```javascript
this.$bus.$once('cli_miru_login_rsp', response => {
  this.$store.commit('SET_TOKEN', response.body.sessid)
  setToken(response.body.sessid)
  this.getDeviceList()
})
```

## 问题分析

1. **存储数据不完整**：只更新了token，没有更新其他用户信息
2. **缺少跨Tab支持**：没有调用tabManager.setSessid()
3. **与其他页面不一致**：其他页面使用了完整的存储更新逻辑

## 建议的解决方案

### 方案1：应用autoRelogin mixin（推荐）

```javascript
// 在video.vue中添加mixin
import autoReloginMixin from '@/mixins/autoRelogin'

export default {
  name: 'Video',
  mixins: [autoReloginMixin],
  // ... 其他配置
}
```

然后修改created钩子：
```javascript
created() {
  this.video_list = [].concat(Array(this.multi_screen).fill({}))
  console.log(window.location.protocol)
  
  // 使用mixin的自动重登逻辑
  this.handleAutoRelogin()
  
  // 设置账号类型
  this.isSuperAdmin = sessionStorage.account_type == '1'
  this.isNornalAdmin = sessionStorage.account_type == '3'
},

// 重写mixin的回调方法
onReloginSuccess(response) {
  console.log('video页面重登成功')
  this.getDeviceList()
},

onWebsocketReady() {
  console.log('video页面websocket准备就绪')
  this.getDeviceList()
}
```

### 方案2：手动更新重登逻辑

如果不想使用mixin，可以手动更新重登逻辑：
```javascript
this.$bus.$once('cli_miru_login_rsp', response => {
  console.log('video页面重登成功', response)
  
  // 更新store和sessionStorage中的token
  this.$store.commit('SET_TOKEN', response.body.sessid)
  setToken(response.body.sessid)
  
  // 重新设置sessionStorage中的用户信息
  if (response.body.account_type !== undefined) {
    sessionStorage.account_type = response.body.account_type
  }
  if (response.body.account) {
    sessionStorage.username = response.body.account
  }
  if (response.body.alias) {
    sessionStorage.account_alias = response.body.alias
  }
  if (response.body.srs_http) {
    sessionStorage.videoUrl = JSON.stringify(response.body.srs_http)
  }
  
  // 使用tabManager保存sessid和account到localStorage供其他Tab共享
  tabManager.setSessid(response.body.sessid, response.body.account)
  
  this.getDeviceList()
})
```

## 推荐方案1的原因

1. **代码一致性**：与其他页面保持一致的重登机制
2. **维护性**：统一的重登逻辑，便于维护和调试
3. **功能完整性**：自动包含所有存储更新和跨Tab支持
4. **扩展性**：如果将来需要添加新的重登逻辑，只需要修改mixin

## 注意事项

1. **测试重要性**：video.vue是核心页面，任何修改都需要充分测试
2. **向后兼容**：确保修改不会影响现有的视频播放功能
3. **性能考虑**：重登逻辑不应影响视频流的性能

## 实施步骤

1. 备份当前的video.vue文件
2. 应用推荐的修改
3. 测试以下场景：
   - 正常登录后访问视频页面
   - 刷新视频页面（触发重登）
   - 多Tab场景下的重登
   - 视频播放功能是否正常
4. 如果测试通过，提交修改

这样可以确保video.vue与其他页面具有一致的重登机制，同时解决跨Tab自动登录的问题。
