# 缓存登录功能说明

## 新增功能概述

实现了两个关键功能：
1. **优先使用缓存登录**：手动点击登录时，优先检查并使用缓存的sessid和account
2. **登录失败清除缓存**：任何登录失败操作都会清空sessid和account的缓存

## 功能1：优先使用缓存登录

### 实现逻辑

```javascript
handleLogin() {
  // 验证表单后...
  
  // 检查是否有缓存的sessid和account
  const cachedSessid = tabManager.getSessid()
  const cachedAccount = tabManager.getAccount()
  
  if (cachedSessid && cachedAccount) {
    console.log('发现缓存的sessid和account，尝试使用缓存登录')
    this.tryLoginWithCache(cachedSessid, cachedAccount)
  } else {
    console.log('没有缓存，使用账号密码登录')
    this.toLinkWsLogin(ws_server)
  }
}
```

### 缓存登录流程

1. **检查缓存**：从localStorage获取sessid和account
2. **构建请求**：使用缓存数据构建登录请求（passwd为空，sessid不为空）
3. **发送请求**：通过websocket发送缓存登录请求
4. **处理响应**：
   - 成功：直接登录成功
   - 失败：清除缓存，回退到账号密码登录

### 缓存登录的优势

- **用户体验**：即使用户手动输入账号密码，也会优先使用更快的缓存登录
- **减少服务器压力**：避免不必要的密码验证
- **无缝切换**：缓存失效时自动回退到密码登录

## 功能2：登录失败清除缓存

### 触发场景

1. **缓存登录失败**：使用缓存的sessid登录失败
2. **账号密码登录失败**：传统的账号密码登录失败
3. **自动重登失败**：页面刷新时的自动重登失败

### 清除范围

#### localStorage清除
```javascript
tabManager.clearSessid(false) // 清除共享的sessid和account
```

#### sessionStorage清除
```javascript
sessionStorage.removeItem('token')
sessionStorage.removeItem('username')
sessionStorage.removeItem('account_type')
sessionStorage.removeItem('account_alias')
sessionStorage.removeItem('videoUrl')
```

#### store清除
```javascript
this.$store.commit('SET_TOKEN', '')
```

### 实现位置

1. **登录页面**：`handleLoginFailure()`方法
2. **autoRelogin mixin**：`handleReloginFailure()`方法

## 完整的登录流程图

```
用户点击登录
    ↓
检查localStorage缓存
    ↓
有缓存？
├─ 是 → 使用缓存登录
│       ↓
│   缓存登录成功？
│   ├─ 是 → 登录成功
│   └─ 否 → 清除缓存 → 使用账号密码登录
│                      ↓
│                  密码登录成功？
│                  ├─ 是 → 登录成功
│                  └─ 否 → 清除缓存 → 显示错误
└─ 否 → 使用账号密码登录
        ↓
    密码登录成功？
    ├─ 是 → 登录成功
    └─ 否 → 清除缓存 → 显示错误
```

## 测试场景

### 1. 正常缓存登录测试
1. 正常登录系统
2. 退出到登录页（不关闭浏览器）
3. 重新输入账号密码点击登录
4. **预期**：Console显示"发现缓存的sessid和account，尝试使用缓存登录"
5. **预期**：快速登录成功，无需密码验证

### 2. 缓存失效回退测试
1. 正常登录系统
2. 手动清除localStorage中的sessid（模拟过期）
3. 退出到登录页
4. 重新输入账号密码点击登录
5. **预期**：Console显示"缓存登录失败，清除缓存并使用账号密码登录"
6. **预期**：自动回退到密码登录并成功

### 3. 登录失败清除缓存测试
1. 在登录页输入错误的账号密码
2. 点击登录
3. **预期**：登录失败后Console显示"登录失败，清除sessid和account缓存"
4. **预期**：localStorage和sessionStorage中的相关数据被清除

### 4. 多Tab缓存同步测试
1. 在Tab1正常登录
2. 在Tab2打开登录页
3. 在Tab2点击登录（即使输入不同的账号密码）
4. **预期**：Tab2使用Tab1的缓存快速登录

### 5. 自动重登失败测试
1. 正常登录后访问任意页面
2. 手动修改localStorage中的sessid为无效值
3. 刷新页面
4. **预期**：自动重登失败，缓存被清除，跳转到登录页

## 关键改进点

1. **智能登录策略**：优先使用缓存，失败时自动回退
2. **完整的失败处理**：任何登录失败都会清除所有相关缓存
3. **跨Tab一致性**：所有Tab共享相同的缓存状态
4. **用户体验优化**：用户无需关心缓存逻辑，系统自动选择最优登录方式

这些改进确保了登录系统的健壮性和用户体验，同时保持了缓存机制的可靠性。
