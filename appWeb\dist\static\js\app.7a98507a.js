(window.webpackJsonp=window.webpackJsonp||[]).push([["app"],{"+7Mh":function(e,t,i){},"+LJE":function(e,t,i){e.exports=i.p+"static/img/loading.f082413.gif"},"+SfS":function(e,t,i){"use strict";i.r(t);var n=i("4bNr"),a=i("cnwH");for(var o in a)"default"!==o&&function(e){i.d(t,e,function(){return a[e]})}(o);i("3H7Y");var s=i("KHd+"),r=Object(s.a)(a.default,n.a,n.b,!1,null,"b289dc08",null);r.options.__file="src\\views\\videoMgt\\index.vue",t.default=r.exports},"/KzS":function(e,t,i){"use strict";i.r(t);var n=i("1PML"),a=i.n(n);for(var o in n)"default"!==o&&function(e){i.d(t,e,function(){return n[e]})}(o);t.default=a.a},"/eX4":function(e,t,i){"use strict";i.r(t);var n=i("a14t"),a=i("ZJrb");for(var o in a)"default"!==o&&function(e){i.d(t,e,function(){return a[e]})}(o);i("evNe");var s=i("KHd+"),r=Object(s.a)(a.default,n.a,n.b,!1,null,"8fc51f94",null);r.options.__file="src\\views\\errorPage\\404.vue",t.default=r.exports},"0sKO":function(e,t,i){"use strict";var n=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"userMgtBox"},[n("div",{staticClass:"userMgtLayout"},[n("div",{staticClass:"operateUserBtnBox"},[n("span",{staticClass:"userPageTitle"},[e._v(e._s(e.$t("userManagement")))]),e._v(" "),n("div",[n("el-button",{staticClass:"tipSubmitBtn",on:{click:function(t){e.addUser()}}},[e._v(e._s(e.$t("addUser")))]),e._v(" "),n("el-button",{staticClass:"tipCancelBtn",on:{click:function(t){e.delBulkUser()}}},[e._v(e._s(e.$t("bulkDelete")))])],1)]),e._v(" "),n("div",{staticClass:"userListBox"},[n("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.list_loading,expression:"list_loading"}],attrs:{data:e.user_list,"highlight-current-row":!0,stripe:""},on:{"selection-change":e.handleSelectionChange}},[n("el-table-column",{attrs:{type:"selection",align:"center",width:"60"}}),e._v(" "),n("el-table-column",{attrs:{type:"index",align:"center",label:"#",width:"60","class-name":"infoText"}}),e._v(" "),n("el-table-column",{attrs:{label:e.$t("permissionLevel"),align:"center",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[3===t.row.account_type?n("span",[e._v(e._s(e.$t("manager")))]):n("span",[e._v(e._s(e.$t("guest")))])]}}])}),e._v(" "),e._l(e.listTitle,function(t,i){return n("el-table-column",{key:i,attrs:{prop:t.name,label:t.title,width:t.width,"column-key":t.name},scopedSlots:e._u([{key:"default",fn:function(t){return[n("section",{attrs:{title:t.row[t.column.columnKey]},domProps:{innerHTML:e._s(e.handleColumnShow(t.row[t.column.columnKey]))}})]}}])})}),e._v(" "),n("el-table-column",{attrs:{label:e.$t("operate")},scopedSlots:e._u([{key:"default",fn:function(t){return[n("section",[n("el-button",{staticClass:"operateRowBtn",attrs:{title:e.$t("edit"),type:"text"},on:{click:function(i){e.addEditUser(t.row)}}},[e._v(e._s(e.$t("edit")))]),e._v(" "),n("el-button",{staticClass:"operateRowBtn rowDeleteBtn",attrs:{title:e.$t("delete"),type:"text"},on:{click:function(i){e.delUser(t.row)}}},[e._v(e._s(e.$t("delete")))]),e._v(" "),n("el-button",{staticClass:"operateRowBtn rowResetPasswordBtn",attrs:{title:e.$t("resetPassword"),type:"text"},on:{click:function(i){e.resetPasswordUser(t.row)}}},[e._v(e._s(e.$t("resetPassword")))])],1)]}}])}),e._v(" "),n("template",{slot:"empty"},[n("el-empty",{attrs:{image:i("tFzm"),"image-size":65}})],1)],2),e._v(" "),n("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>10,expression:"total > 10"}],staticClass:"text-right mar_top_0",attrs:{total:e.total,page:e.listQuery.page,limit:e.listQuery.limit},on:{"update:page":function(t){e.$set(e.listQuery,"page",t)},"update:limit":function(t){e.$set(e.listQuery,"limit",t)},pagination:e.handlePaginationChange}})],1)]),e._v(" "),n("el-dialog",{attrs:{title:e.dialogTitle,visible:e.dialogVisible,width:e.dialogTitle===e.$t("resetPassword")||e.dialogTitle===e.$t("deleteUser")?"340px":"500px","before-close":e.hideDialog},on:{"update:visible":function(t){e.dialogVisible=t}}},[e.dialogTitle===e.$t("addUser")||e.dialogTitle===e.$t("editUser")?[n("el-form",{ref:"userForm",attrs:{rules:e.userForm.rules,model:e.userForm.data,"label-width":"23%","label-position":"left"}},[n("el-form-item",{attrs:{label:e.$t("permissionLevel"),prop:e.dialogTitle===e.$t("addUser")?"account_type":""}},[n("el-select",{attrs:{placeholder:e.$t("pleaseSelect")},model:{value:e.userForm.data.account_type,callback:function(t){e.$set(e.userForm.data,"account_type",t)},expression:"userForm.data.account_type"}},e._l(e.accountTypeRange,function(e){return n("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})}))],1),e._v(" "),n("el-form-item",{attrs:{label:e.$t("account"),prop:e.dialogTitle===e.$t("addUser")?"account":""}},[e.dialogTitle===e.$t("addUser")?n("el-input",{attrs:{placeholder:e.$t("inputLengthLimit",{limit:"6-20"}),maxlength:"20"},on:{input:e.onAccountInput},model:{value:e.userForm.data.account,callback:function(t){e.$set(e.userForm.data,"account",t)},expression:"userForm.data.account"}}):n("span",{staticStyle:{"font-weight":"400"}},[e._v(e._s(e.userForm.data.account))])],1),e._v(" "),e.dialogTitle===e.$t("addUser")?n("el-form-item",{attrs:{label:e.$t("password"),prop:"password"}},[n("el-input",{attrs:{placeholder:e.$t("inputLengthLimit",{limit:"6-18"}),maxlength:"18",type:"password","show-password":""},model:{value:e.userForm.data.password,callback:function(t){e.$set(e.userForm.data,"password",t)},expression:"userForm.data.password"}})],1):e._e(),e._v(" "),n("el-form-item",{attrs:{label:e.$t("nickName"),prop:"nickName"}},[n("el-input",{attrs:{placeholder:e.$t("inputLengthLimit",{limit:30}),maxlength:"30"},model:{value:e.userForm.data.nickName,callback:function(t){e.$set(e.userForm.data,"nickName",t)},expression:"userForm.data.nickName"}})],1),e._v(" "),n("el-form-item",{staticClass:"formItemTop",attrs:{label:e.$t("mark"),prop:"mark"}},[n("el-input",{attrs:{placeholder:e.$t("inputLengthLimit",{limit:100}),maxlength:"100",type:"textarea"},model:{value:e.userForm.data.mark,callback:function(t){e.$set(e.userForm.data,"mark",t)},expression:"userForm.data.mark"}})],1)],1)]:[n("div",{staticClass:"confirmDeleteText"},[e._v("\n        "+e._s(e.dialogTitle===e.$t("resetPassword")?e.$t("sureToReset"):e.$t("sureToDelete"))+"\n      ")])],e._v(" "),n("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[n("el-button",{staticClass:"tipCancelBtn",on:{click:function(t){e.hideDialog()}}},[e._v(e._s(e.$t("cancel")))]),e._v(" "),n("el-button",{staticClass:"tipSubmitBtn",attrs:{loading:e.isSubmitting},on:{click:function(t){e.submit()}}},[e._v(e._s(e.$t("sure")))])],1)],2)],1)},a=[];n._withStripped=!0,i.d(t,"a",function(){return n}),i.d(t,"b",function(){return a})},"1PML":function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default={name:"AppMain",computed:{key:function(){return this.$route.fullPath},isKeepAlive:function(){return["/user","/device","/system","/logs","/video_management"].includes(this.$route.fullPath)}}}},"1mG+":function(e,t,i){},"25re":function(e,t,i){"use strict";var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"pagination-container",class:{hidden:e.hidden}},[i("el-pagination",e._b({attrs:{"current-page":e.currentPage,"page-size":e.pageSize,layout:e.layout,"page-sizes":e.pageSizes,total:e.total},on:{"update:currentPage":function(t){e.currentPage=t},"update:pageSize":function(t){e.pageSize=t},"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}},"el-pagination",e.$attrs,!1))],1)},a=[];n._withStripped=!0,i.d(t,"a",function(){return n}),i.d(t,"b",function(){return a})},"27UU":function(e,t,i){"use strict";var n=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"navbar"},[n("div",{staticClass:"infoBox"},[n("div",{staticClass:"platInfo"},[n("img",{staticClass:"platLogo",attrs:{src:i("X8xi")}}),e._v(" "),n("div",{staticClass:"platNameBox"},[n("span",{staticClass:"platName"},[e._v(e._s(e.$t("mgtPlat")))]),e._v(" "),n("span",{staticClass:"platNameTip"},[e._v(e._s(e.$t("mgtPlatTip")))])])]),e._v(" "),n("div",{staticClass:"menuCard"},[n("div",{staticClass:"subMenu",class:{selectedSubMenu:"home"===e.selected_menu_flag},on:{click:function(t){e.toggleMenu("home")}}},[e._v("\n        "+e._s(e.$t("home"))+"\n      ")]),e._v(" "),e.isSuperAdmin||e.isNornalAdmin?n("div",{staticClass:"subMenu",class:{selectedSubMenu:"device"===e.selected_menu_flag},on:{click:function(t){e.toggleMenu("device")}}},[e._v("\n        "+e._s(e.$t("deviceManagement"))+"\n      ")]):e._e(),e._v(" "),e.isSuperAdmin?n("div",{staticClass:"subMenu",class:{selectedSubMenu:"user"===e.selected_menu_flag},on:{click:function(t){e.toggleMenu("user")}}},[e._v("\n        "+e._s(e.$t("userManagement"))+"\n      ")]):e._e(),e._v(" "),e.isSuperAdmin?n("div",{staticClass:"subMenu",class:{selectedSubMenu:"video_management"===e.selected_menu_flag},on:{click:function(t){e.toggleMenu("video_management")}}},[e._v("\n        "+e._s(e.$t("videomanagement"))+"\n      ")]):e._e(),e._v(" "),e.isSuperAdmin||e.isNornalAdmin?n("div",{staticClass:"subMenu",class:{selectedSubMenu:"system"===e.selected_menu_flag},on:{click:function(t){e.toggleMenu("system")}}},[e._v("\n        "+e._s(e.$t("systemSetting"))+"\n      ")]):e._e(),e._v(" "),e.isSuperAdmin||e.isNornalAdmin?n("div",{staticClass:"subMenu",class:{selectedSubMenu:"logs"===e.selected_menu_flag},on:{click:function(t){e.toggleMenu("logs")}}},[e._v("\n        "+e._s(e.$t("operationLogs"))+"\n      ")]):e._e()]),e._v(" "),n("div",{staticClass:"navbar__right"},[n("lang-select",{staticClass:"set-language",on:{handleSetLanguage:e.handleSetLanguage}}),e._v(" "),n("span",{staticClass:"userName"},[e._v(e._s(e.username))]),e._v(" "),n("div",{staticClass:"logOut pointer",on:{click:e.logoutDialog}},[e._v("\n        "+e._s(e.$t("quitTitle"))+"\n      ")])],1)]),e._v(" "),n("tip-dialog",{ref:"tipDialog",attrs:{title:e.dialogTitle,tip:e.dialogTip,request:e.tipRequest,params:e.tipParams},on:{handleTip:e.backTip}})],1)},a=[];n._withStripped=!0,i.d(t,"a",function(){return n}),i.d(t,"b",function(){return a})},"2WRI":function(e,t,i){e.exports=i.p+"static/img/playFail.38d651c.png"},"2c2J":function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=function(e){return e&&e.__esModule?e:{default:e}}(i("p46w"));(navigator.language||navigator.browserLanguage).toLowerCase();var a={state:{language:n.default.get("language")||"ky",size:n.default.get("size")||"medium"},mutations:{SET_LANGUAGE:function(e,t){e.language=t,n.default.set("language",t)},SET_SIZE:function(e,t){e.size=t,n.default.set("size",t)}},actions:{setLanguage:function(e,t){(0,e.commit)("SET_LANGUAGE",t)},setSize:function(e,t){(0,e.commit)("SET_SIZE",t)}}};t.default=a},"2c6e":function(e,t,i){"use strict";i.r(t);var n=i("9hzj"),a=i("O+jn");for(var o in a)"default"!==o&&function(e){i.d(t,e,function(){return a[e]})}(o);var s=i("KHd+"),r=Object(s.a)(a.default,n.a,n.b,!1,null,null,null);r.options.__file="src\\views\\layout\\Layout.vue",t.default=r.exports},"3H7Y":function(e,t,i){"use strict";var n=i("MsN4");i.n(n).a},"3KQq":function(e,t,i){"use strict";var n=i("YgiJ");i.n(n).a},"4AHp":function(e,t,i){"use strict";i.r(t);var n=i("UK+I"),a=i("fY3g");for(var o in a)"default"!==o&&function(e){i.d(t,e,function(){return a[e]})}(o);i("Jn5J");var s=i("KHd+"),r=Object(s.a)(a.default,n.a,n.b,!1,null,"2b770551",null);r.options.__file="src\\views\\video.vue",t.default=r.exports},"4E/B":function(e,t,i){"use strict";var n=i("1mG+");i.n(n).a},"4bNr":function(e,t,i){"use strict";var n=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"videoMgtBox"},[n("div",{staticClass:"videoMgtLayout"},[n("div",{staticClass:"filterBox"},[n("div",{staticClass:"operationType filterItem"},[n("span",[e._v(e._s(e.$t("group")))]),e._v(" "),n("el-select",{staticStyle:{width:"400px"},attrs:{placeholder:e.$t("pleaseSelect")},on:{change:e.onChangeSelect},model:{value:e.group,callback:function(t){e.group=t},expression:"group"}},e._l(e.groupList,function(e){return n("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})}))],1),e._v(" "),n("div",{staticClass:"operationType filterItem"},[n("span",[e._v(e._s(e.$t("device")))]),e._v(" "),n("el-input",{staticStyle:{width:"200px"},attrs:{placeholder:e.$t("deviceSearchTip"),maxlength:"100"},nativeOn:{keyup:function(t){if(!("button"in t)&&e._k(t.keyCode,"enter",13,t.key,"Enter"))return null;e.search()}},model:{value:e.searchText,callback:function(t){e.searchText=t},expression:"searchText"}})],1),e._v(" "),n("div",{staticClass:"operationType filterItem"},[n("span",[e._v(e._s(e.$t("time")))]),e._v(" "),n("el-date-picker",{attrs:{type:"daterange","range-separator":"~","start-placeholder":e.$t("startDate"),"end-placeholder":e.$t("endDate")},on:{change:e.onTimeChange},model:{value:e.operationTime,callback:function(t){e.operationTime=t},expression:"operationTime"}})],1),e._v(" "),n("el-button",{staticClass:"tipSubmitBtn",attrs:{type:"primary",icon:"el-icon-search"},on:{click:function(t){e.search()}}},[e._v("\n        "+e._s(e.$t("search"))+"\n      ")])],1),e._v(" "),n("div",{staticClass:"operateBox"},[n("el-checkbox",{staticClass:"selectAll",attrs:{indeterminate:e.isHasSomeChecked},on:{change:e.handleAllChecked},model:{value:e.checkedAll,callback:function(t){e.checkedAll=t},expression:"checkedAll"}},[e._v(e._s(e.$t("allselect")))]),e._v(" "),n("el-button",{staticClass:"tipSubmitBtn",on:{click:function(t){e.downloadVideos()}}},[e._v(e._s(e.$t("download")))]),e._v(" "),n("el-button",{staticClass:"tipDeleteBtn",on:{click:function(t){e.deleteRows()}}},[e._v(e._s(e.$t("delete")))])],1),e._v(" "),n("div",{staticClass:"rowListBox"},[e.isLoading?n("div",{staticClass:"loadingStateBox"},[n("div",{staticClass:"loadingContent"},[n("img",{staticClass:"loadingImage",attrs:{src:i("+LJE"),alt:"加载中..."}}),e._v(" "),n("p",{staticClass:"loadingText"},[e._v(e._s(e.$t("loading")))])])]):0===e.rowList.length?n("div",{staticClass:"emptyStateBox"},[n("el-empty",{attrs:{image:i("tFzm"),"image-size":65}})],1):n("div",{staticClass:"rowListBox"},e._l(e.rowList,function(t){return n("div",{key:t.videoId,staticClass:"rowItem"},[n("el-checkbox",{on:{change:e.handleRowChecked},model:{value:t.checked,callback:function(i){e.$set(t,"checked",i)},expression:"item.checked"}}),e._v(" "),n("div",{staticClass:"rowImageBox",on:{click:function(i){e.handlePlayVideo(t)}}},[t.imageUrl?n("img",{staticClass:"rowImage",attrs:{src:t.imageUrl}}):n("img",{staticClass:"rowIcon2",attrs:{src:i("o5Jc")}}),e._v(" "),n("img",{staticClass:"rowPlayIcon",attrs:{src:i("jhCA"),alt:""}})]),e._v(" "),n("div",{staticClass:"rowInfo"},[n("div",{staticClass:"rowDesc"},[n("span",{staticClass:"rowName"},[e._v(e._s(t.name))]),e._v(" "),n("span",{staticClass:"rowGroup"},[e._v(e._s(t.group))])]),e._v(" "),n("div",{staticClass:"rowTime"},[e._v(e._s(t.timeText))])]),e._v(" "),n("div",{staticStyle:{flex:"1"}}),e._v(" "),n("div",{staticClass:"rowOperate"},[n("el-button",{staticClass:"rowOperateBtn download",attrs:{type:"text"},on:{click:function(i){e.downloadVideos(t)}}},[e._v(e._s(e.$t("download")))]),n("span",{staticClass:"rowOperateSplit"},[e._v("|")]),n("el-button",{staticClass:"rowOperateBtn delete",attrs:{type:"text"},on:{click:function(i){e.deleteRows(t)}}},[e._v(e._s(e.$t("delete")))])],1)],1)}))]),e._v(" "),n("div",{staticClass:"paginationBox"},[n("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>10,expression:"total > 10"}],staticClass:"text-right mar_top_0",attrs:{total:e.total,page:e.listQuery.page,limit:e.listQuery.limit},on:{"update:page":function(t){e.$set(e.listQuery,"page",t)},"update:limit":function(t){e.$set(e.listQuery,"limit",t)},pagination:e.handlePaginationChange}})],1)]),e._v(" "),n("el-dialog",{attrs:{title:e.currentVideo.name,visible:e.showVideoDialog,width:"800px","before-close":e.closeVideoDialog},on:{"update:visible":function(t){e.showVideoDialog=t}}},[n("div",{staticClass:"video-container"},[n("video",{ref:"videoPlayer",attrs:{src:e.currentVideo.videoUrl,controls:"",preload:"metadata",autoplay:""},on:{loadstart:e.onVideoLoadStart,canplay:e.onVideoCanPlay,error:e.onVideoError,keydown:function(t){return"button"in t||!e._k(t.keyCode,"esc",27,t.key,"Escape")?e.closeVideoDialog(t):null}}},[e._v("\n        Your browser does not support the video tag.\n      ")])]),e._v(" "),n("div",{staticClass:"video-info"},[n("p",{staticClass:"video-group"},[e._v(e._s(e.currentVideo.group))]),e._v(" "),n("p",{staticClass:"video-time"},[e._v("\n        "+e._s(e.currentVideo.timeText)+"\n      ")])])]),e._v(" "),n("tip-dialog",{ref:"tipDialog",attrs:{title:e.dialogTitle,tip:e.dialogTip,request:e.tipRequest,params:e.tipParams,reqType:"http"}})],1)},a=[];n._withStripped=!0,i.d(t,"a",function(){return n}),i.d(t,"b",function(){return a})},"4fdH":function(e,t,i){"use strict";var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-dialog",{attrs:{title:e.title,visible:e.dialogVisible,"close-on-click-modal":!1,width:"700px"},on:{"update:visible":function(t){e.dialogVisible=t},close:e.hideDialog}},[i("section",[i("el-form",{ref:"dialogForm",attrs:{rules:e.dialogForm.rules,model:e.dialogForm.data,"label-width":"240px","label-position":"left"}},[i("el-form-item",{attrs:{label:e.$t("groupName"),prop:"group_name"}},[i("el-input",{model:{value:e.dialogForm.data.group_name,callback:function(t){e.$set(e.dialogForm.data,"group_name",t)},expression:"dialogForm.data.group_name"}})],1),e._v(" "),i("el-form-item",{attrs:{label:e.$t("groupNumber")}},[i("span",{staticStyle:{"font-weight":"400"}},[e._v(e._s(e.number))])]),e._v(" "),"1"==e.accountType?i("el-form-item",{staticClass:"formItemTop",attrs:{label:e.$t("authorizedUser")}},[i("div",[i("el-checkbox-group",{model:{value:e.authorizedUserList,callback:function(t){e.authorizedUserList=t},expression:"authorizedUserList"}},e._l(e.userList,function(e,t){return i("el-checkbox",{key:t,attrs:{label:e.name?e.name:e.account}})}))],1)]):e._e()],1)],1),e._v(" "),i("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{staticClass:"tipCancelBtn",attrs:{type:"info"},on:{click:function(t){e.hideDialog()}}},[e._v(e._s(e.$t("cancel")))]),e._v(" "),i("el-button",{staticClass:"tipSubmitBtn",attrs:{loading:e.loading,type:"primary"},on:{click:function(t){e.submit()}}},[e._v(e._s(e.$t("sure")))])],1)])},a=[];n._withStripped=!0,i.d(t,"a",function(){return n}),i.d(t,"b",function(){return a})},"4pdN":function(e,t,i){"use strict";var n=function(){var e=this.$createElement,t=this._self._c||e;return t("svg",{class:this.svgClass,attrs:{"aria-hidden":"true"}},[t("use",{attrs:{"xlink:href":this.iconName}})])},a=[];n._withStripped=!0,i.d(t,"a",function(){return n}),i.d(t,"b",function(){return a})},"5Hul":function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getVideoList=function(e){return(0,n.default)({url:"v1/miru/video/list",method:"post",data:e})},t.getDeviceVideo=function(e){return(0,n.default)({url:"v1/miru/video/get",method:"post",data:e})},t.deleteVideos=function(e){return(0,n.default)({url:"v1/miru/video/delete",method:"post",data:e})};var n=function(e){return e&&e.__esModule?e:{default:e}}(i("t3Un"))},"65Ft":function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=s(i("gDS+")),a=s(i("iCc5")),o=s(i("V7oC"));function s(e){return e&&e.__esModule?e:{default:e}}var r="miru_shared_sessid",l="miru_shared_account",d=new(function(){function e(){(0,a.default)(this,e),this.tabId=this.generateTabId(),this.isInitialized=!1,this.logoutCallbacks=[],this.loginCallbacks=[],this.init()}return(0,o.default)(e,[{key:"generateTabId",value:function(){return"tab_"+Date.now()+"_"+Math.random().toString(36).substr(2,9)}},{key:"init",value:function(){this.isInitialized||(sessionStorage.setItem("miru_tab_id",this.tabId),this.incrementTabCount(),this.setupBeforeUnloadListener(),this.setupStorageListener(),this.isInitialized=!0,console.log("TabManager initialized, Tab ID:",this.tabId))}},{key:"incrementTabCount",value:function(){var e=parseInt(localStorage.getItem("miru_tab_count")||"0");localStorage.setItem("miru_tab_count",(e+1).toString()),console.log("Tab count incremented to:",e+1)}},{key:"decrementTabCount",value:function(){var e=parseInt(localStorage.getItem("miru_tab_count")||"0"),t=Math.max(0,e-1);return localStorage.setItem("miru_tab_count",t.toString()),console.log("Tab count decremented to:",t),t}},{key:"getTabCount",value:function(){return parseInt(localStorage.getItem("miru_tab_count")||"0")}},{key:"setSessid",value:function(e,t){e&&(localStorage.getItem(r)!==e&&(localStorage.setItem(r,e),console.log("Sessid saved to localStorage:",e)));t&&(localStorage.getItem(l)!==t&&(localStorage.setItem(l,t),console.log("Account saved to localStorage:",t)))}},{key:"getSessid",value:function(){return localStorage.getItem(r)}},{key:"getAccount",value:function(){return localStorage.getItem(l)}},{key:"clearSessid",value:function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];localStorage.removeItem(r),localStorage.removeItem(l),console.log("Sessid and account cleared from localStorage"),e&&this.triggerLogoutEvent()}},{key:"hasValidSessid",value:function(){var e=this.getSessid(),t=this.getAccount();return e&&""!==e.trim()&&t&&""!==t.trim()}},{key:"setupBeforeUnloadListener",value:function(){var e=this;window.addEventListener("beforeunload",function(){0===e.decrementTabCount()&&(e.clearSessid(!1),console.log("Last tab closing, sessid cleared"))})}},{key:"setupStorageListener",value:function(){var e=this;window.addEventListener("storage",function(t){t.key===r&&(console.log("Sessid changed in another tab:",t.newValue),t.newValue?t.oldValue!==t.newValue&&(console.log("New sessid detected in another tab, triggering login sync"),e.handleLoginEvent(t.newValue)):(console.log("Sessid cleared in another tab, triggering logout"),e.handleLogoutEvent())),"miru_logout_event"===t.key&&t.newValue&&(console.log("Logout event received from another tab"),e.handleLogoutEvent())})}},{key:"onSessidChanged",value:function(e){console.log("Sessid changed:",e)}},{key:"triggerLogoutEvent",value:function(){var e={tabId:this.tabId,timestamp:Date.now()};localStorage.setItem("miru_logout_event",(0,n.default)(e)),console.log("Logout event triggered by tab:",this.tabId),setTimeout(function(){localStorage.removeItem("miru_logout_event")},100)}},{key:"handleLogoutEvent",value:function(){this.logoutCallbacks.forEach(function(e){try{e()}catch(e){console.error("Error executing logout callback:",e)}})}},{key:"handleLoginEvent",value:function(e){this.loginCallbacks.forEach(function(t){try{t(e)}catch(e){console.error("Error executing login callback:",e)}})}},{key:"onLogout",value:function(e){"function"==typeof e&&this.logoutCallbacks.push(e)}},{key:"offLogout",value:function(e){var t=this.logoutCallbacks.indexOf(e);t>-1&&this.logoutCallbacks.splice(t,1)}},{key:"onLogin",value:function(e){"function"==typeof e&&this.loginCallbacks.push(e)}},{key:"offLogin",value:function(e){var t=this.loginCallbacks.indexOf(e);t>-1&&this.loginCallbacks.splice(t,1)}},{key:"getTabId",value:function(){return this.tabId}},{key:"isFirstTab",value:function(){return 1===this.getTabCount()}},{key:"destroy",value:function(){0===this.decrementTabCount()&&this.clearSessid(!1)}}]),e}());t.default=d},"67KD":function(e,t,i){},"6g3Z":function(e,t,i){"use strict";i.r(t);var n=i("M3qR"),a=i.n(n);for(var o in n)"default"!==o&&function(e){i.d(t,e,function(){return n[e]})}(o);t.default=a.a},"78Ql":function(e,t,i){"use strict";var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-dialog",{attrs:{title:e.title,visible:e.dialogVisible,"close-on-click-modal":!1},on:{"update:visible":function(t){e.dialogVisible=t},close:e.hideDialog}},[i("section",[i("el-form",{ref:"dialogForm",attrs:{rules:e.dialogForm.rules,model:e.dialogForm.data,"label-width":"23%","label-position":"left"}},[e.is_edit?e._e():i("el-form-item",{attrs:{label:e.$t("sn"),prop:"sn"}},[i("el-input",{attrs:{placeholder:e.$t("snEnter"),disabled:e.is_edit,maxlength:"16"},model:{value:e.dialogForm.data.sn,callback:function(t){e.$set(e.dialogForm.data,"sn",t)},expression:"dialogForm.data.sn"}})],1),e._v(" "),e.is_edit?e._e():i("el-form-item",{attrs:{label:e.$t("deviceVerificationCode"),prop:"code"}},[i("el-input",{attrs:{placeholder:e.$t("deviceCodeEnter"),maxlength:"6"},model:{value:e.dialogForm.data.code,callback:function(t){e.$set(e.dialogForm.data,"code",t)},expression:"dialogForm.data.code"}})],1),e._v(" "),i("el-form-item",{attrs:{label:e.$t("deviceNickname"),prop:"name"}},[i("el-input",{attrs:{placeholder:e.$t("deviceNameEnter"),maxlength:"20"},model:{value:e.dialogForm.data.name,callback:function(t){e.$set(e.dialogForm.data,"name",t)},expression:"dialogForm.data.name"}})],1)],1)],1),e._v(" "),i("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{staticClass:"tipCancelBtn",attrs:{type:"info"},on:{click:function(t){e.hideDialog()}}},[e._v(e._s(e.$t("cancel")))]),e._v(" "),i("el-button",{staticClass:"tipSubmitBtn",attrs:{loading:e.loading,type:"primary"},on:{click:function(t){e.submit()}}},[e._v(e._s(e.$t("sure")))])],1)])},a=[];n._withStripped=!0,i.d(t,"a",function(){return n}),i.d(t,"b",function(){return a})},"7Qib":function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.handleColumnShow=function(e){return"[object Array]"===Object.prototype.toString.call(e)?0!==e.length?"<p>"+e.join("</p><p>")+"</p>":n.default.t("nodata"):"number"==typeof e?e:e||"--"},t.stringToByte=function(e){var t=e.split(","),i=atob(t[1]),n=i.length,a=new Array(n);for(;n--;)a[n]=255&i.charCodeAt(n);return a},t.dataURLtoBlob=function(e){var t=e.split(","),i=t[0].match(/:(.*?);/)[1],n=atob(t[1]),a=n.length,o=new Uint8Array(a);for(;a--;)o[a]=n.charCodeAt(a);return new Blob([o],{type:i})};var n=function(e){return e&&e.__esModule?e:{default:e}}(i("mSNy"))},8:function(e,t){},"84/4":function(e,t,i){"use strict";i.r(t);var n=i("h3eN"),a=i("c5/p");for(var o in a)"default"!==o&&function(e){i.d(t,e,function(){return a[e]})}(o);i("HRyT");var s=i("KHd+"),r=Object(s.a)(a.default,n.a,n.b,!1,null,"63966a7d",null);r.options.__file="src\\components\\Captcha\\index.vue",t.default=r.exports},"8DEM":function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=s(i("14Xm")),a=s(i("4d7F")),o=s(i("D3Ub"));i("X4fA");function s(e){return e&&e.__esModule?e:{default:e}}t.default={name:"TipDialog",components:{},props:{request:{type:String,default:""},params:{type:Object,default:function(){return{}}},title:{type:String,default:""},tip:{type:String,default:""},reqType:{type:String,default:""}},data:function(){return{dialogVisible:!1,name:"",loading:!1,isEnd:null}},methods:{show:function(e){var t=this;return(0,o.default)(n.default.mark(function i(){return n.default.wrap(function(i){for(;;)switch(i.prev=i.next){case 0:return t.dialogVisible=!0,t.name=e,console.log(t.name),i.abrupt("return",new a.default(function(e){t.isEnd=new Proxy({status:null},{set:function(t,i,n){return"status"===i&&!0===n?e(!0):"status"===i&&!1===n&&e(!1),!0}})}));case 4:case"end":return i.stop()}},i,t)}))()},hideDialog:function(){this.dialogVisible=!1,this.isEnd.status=!1,this.loading=!1},submit:function(){var e=this;if(this.loading=!0,this.isEnd.status=!0,"logout"===this.name)this.loading=!1,this.$store.dispatch("FedLogOut"),location.reload();else if("http"===this.reqType);else{this.$websocket.webSocketSend(this.request,this.params);var t=this.request+"_rsp";this.$bus.$once(t,function(t){e.$emit("handleTip",e.name,t),e.hideDialog()}),this.loading=!1}},handleCancelIcon:function(e){this.$emit("handleCancel"),e()}}}},"8eVs":function(e,t,i){},"8hTN":function(e,t,i){"use strict";var n=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("el-dropdown",{staticClass:"international",attrs:{trigger:"click"},on:{command:e.handleSetLanguage,"visible-change":e.langListVisibleChange}},[n("span",{staticClass:"pointer"},[e._v("\n    "+e._s(e.language_text)),e.lang_dropdown_visible?n("img",{staticClass:"topBottomArrowIcon",attrs:{src:i("kl8X")}}):n("img",{staticClass:"topBottomArrowIcon",attrs:{src:i("ILs8")}})]),e._v(" "),n("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[n("el-dropdown-item",{attrs:{disabled:"ky"===e.language,command:"ky"}},[e._v("Кыргызча")]),e._v(" "),n("el-dropdown-item",{attrs:{disabled:"en"===e.language,command:"en"}},[e._v("English")]),e._v(" "),n("el-dropdown-item",{attrs:{disabled:"ru"===e.language,command:"ru"}},[e._v("Русский")]),e._v(" "),n("el-dropdown-item",{attrs:{disabled:"zh"===e.language,command:"zh"}},[e._v("中文")])],1)],1)},a=[];n._withStripped=!0,i.d(t,"a",function(){return n}),i.d(t,"b",function(){return a})},"97gW":function(e,t,i){"use strict";i.r(t);var n=i("OneV"),a=i.n(n);for(var o in n)"default"!==o&&function(e){i.d(t,e,function(){return n[e]})}(o);t.default=a.a},"984b":function(e,t,i){e.exports=i.p+"static/img/arrow_prev_click.dfda118.png"},"9TU5":function(e,t,i){"use strict";i.r(t);var n=i("Dhux"),a=i.n(n);for(var o in n)"default"!==o&&function(e){i.d(t,e,function(){return n[e]})}(o);t.default=a.a},"9hzj":function(e,t,i){"use strict";var n=function(){var e=this.$createElement,t=this._self._c||e;return t("div",{staticClass:"app-wrapper"},[t("navbar"),this._v(" "),t("div",{staticClass:"main-container"},[t("app-main")],1)],1)},a=[];n._withStripped=!0,i.d(t,"a",function(){return n}),i.d(t,"b",function(){return a})},AMyg:function(e,t,i){"use strict";var n=i("TE6u");i.n(n).a},B4CH:function(e,t,i){"use strict";var n=i("MYBh");i.n(n).a},BnOw:function(e,t,i){"use strict";var n=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"operationLogsBox"},[n("div",{staticClass:"operationLogsLayout"},[n("div",{staticClass:"contentBox"},[n("div",{staticClass:"filterBox"},[n("div",{staticClass:"timeFilter filterItem"},[n("span",[e._v(e._s(e.$t("operationTime")))]),e._v(" "),n("el-date-picker",{attrs:{type:"daterange","range-separator":"~","start-placeholder":e.$t("startDate"),"end-placeholder":e.$t("endDate")},model:{value:e.operationTime,callback:function(t){e.operationTime=t},expression:"operationTime"}})],1),e._v(" "),n("div",{staticClass:"operationType filterItem"},[n("span",[e._v(e._s(e.$t("operationType")))]),e._v(" "),n("el-select",{staticStyle:{width:"400px"},attrs:{placeholder:e.$t("pleaseSelect")},on:{change:e.onChangeSelect},model:{value:e.operationType,callback:function(t){e.operationType=t},expression:"operationType"}},e._l(e.operationTypeRange,function(e){return n("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})}))],1),e._v(" "),n("el-button",{staticClass:"tipSubmitBtn",attrs:{type:"primary",icon:"el-icon-search"},on:{click:function(t){e.search()}}},[e._v("\n          "+e._s(e.$t("search"))+"\n        ")])],1),e._v(" "),n("div",{staticClass:"logsBox"},[n("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.list_loading,expression:"list_loading"}],attrs:{data:e.logsList,"highlight-current-row":!0,stripe:""}},[n("el-table-column",{attrs:{type:"index",align:"center",label:"#",width:"60"}}),e._v(" "),e._l(e.listTitle,function(t,i){return n("el-table-column",{key:i,attrs:{prop:t.name,label:t.title,width:t.width,"column-key":t.name},scopedSlots:e._u([{key:"default",fn:function(t){return[n("section",{attrs:{title:t.row[t.column.columnKey]},domProps:{innerHTML:e._s(e.handleColumnShow(t.row[t.column.columnKey]))}})]}}])})}),e._v(" "),n("template",{slot:"empty"},[n("el-empty",{attrs:{image:i("tFzm"),"image-size":65}})],1)],2),e._v(" "),n("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>20,expression:"total > 20"}],staticClass:"text-right mar_top_0",attrs:{total:e.total,page:e.listQuery.page,limit:e.listQuery.limit},on:{"update:page":function(t){e.$set(e.listQuery,"page",t)},"update:limit":function(t){e.$set(e.listQuery,"limit",t)},pagination:e.handlePaginationChange}})],1)])])])},a=[];n._withStripped=!0,i.d(t,"a",function(){return n}),i.d(t,"b",function(){return a})},CMXa:function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=s(i("gDS+")),a=i("X4fA"),o=s(i("65Ft"));function s(e){return e&&e.__esModule?e:{default:e}}t.default={data:function(){return{isReloginInProgress:!1,isReloginCompleted:!1}},created:function(){this.handleAutoRelogin()},mounted:function(){sessionStorage.home="1"},methods:{handleAutoRelogin:function(){console.log("sessionStorage.home："+sessionStorage.home),sessionStorage.home&&"1"===sessionStorage.home?(console.log("刷新 - 开始自动重登"),this.performAutoRelogin()):(console.log("登录进来 - 检查websocket连接状态"),this.checkWebsocketConnection())},performAutoRelogin:function(){var e=this;this.$websocket&&this.$websocket.closeWebsocket(),sessionStorage.username&&""!==sessionStorage.username&&(this.isReloginInProgress=!0,this.$websocket.initWebSocket("refreshReconnection"),this.$bus.$once("cli_miru_login_rsp",function(t){if(console.log("自动重登响应",t),"number"==typeof t)return console.log("自动重登失败，清除缓存"),e.handleReloginFailure(),e.isReloginInProgress=!1,e.isReloginCompleted=!1,void e.onReloginFailure(t);console.log("自动重登成功"),e.updateStorageAfterRelogin(t),e.isReloginInProgress=!1,e.isReloginCompleted=!0,e.onReloginSuccess(t)}))},checkWebsocketConnection:function(){var e=this;if(this.$websocket&&this.$websocket.socket){var t=this.$websocket.socket.readyState;console.log("websocket状态:",t),0===t?setTimeout(function(){e.checkWebsocketConnection()},300):1===t&&(this.isReloginCompleted=!0,this.onWebsocketReady())}else console.log("websocket未初始化")},updateStorageAfterRelogin:function(e){console.log("更新重登后的存储数据",e.body),this.$store.commit("SET_TOKEN",e.body.sessid),(0,a.setToken)(e.body.sessid),void 0!==e.body.account_type&&(sessionStorage.account_type=e.body.account_type),e.body.account&&(sessionStorage.username=e.body.account),e.body.alias&&(sessionStorage.account_alias=e.body.alias),e.body.srs_http&&(sessionStorage.videoUrl=(0,n.default)(e.body.srs_http)),o.default.setSessid(e.body.sessid,e.body.account),console.log("存储数据更新完成:",{sessid:e.body.sessid,account:e.body.account,account_type:e.body.account_type,alias:e.body.alias})},handleReloginFailure:function(){console.log("重登失败，清除sessid和account缓存"),o.default.clearSessid(!1),sessionStorage.removeItem("token"),sessionStorage.removeItem("username"),sessionStorage.removeItem("account_type"),sessionStorage.removeItem("account_alias"),sessionStorage.removeItem("videoUrl"),this.$store.commit("SET_TOKEN",""),console.log("重登失败缓存清除完成")},onReloginSuccess:function(e){console.log("重登成功，子组件可以重写此方法进行后续操作"),this.onWebsocketReady()},onReloginFailure:function(e){console.log("重登失败，子组件可以重写此方法处理失败情况",e),this.$router.push({path:"/login"})},onWebsocketReady:function(){console.log("websocket准备就绪，子组件可以重写此方法进行数据加载")},canSendHttpRequest:function(){return this.isReloginInProgress?(console.log("重登进行中，暂缓HTTP请求"),!1):!!this.isReloginCompleted||(console.log("重登未完成，暂缓HTTP请求"),!1)},safeHttpRequest:function(e){for(var t=this,i=arguments.length,n=Array(i>1?i-1:0),a=1;a<i;a++)n[a-1]=arguments[a];if(this.canSendHttpRequest())return e.apply(this,n);setTimeout(function i(){if(t.canSendHttpRequest())return e.apply(t,n);setTimeout(i,100)},100)}}}},CTbN:function(e,t,i){"use strict";i.r(t);var n=i("8DEM"),a=i.n(n);for(var o in n)"default"!==o&&function(e){i.d(t,e,function(){return n[e]})}(o);t.default=a.a},D5rb:function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=l(i("4d7F")),a=i("fe1z"),o=i("X4fA"),s=l(i("65Ft")),r=l(i("sutq"));function l(e){return e&&e.__esModule?e:{default:e}}var d={state:{user:"",token:(0,o.getToken)(),role:"",roles:[]},mutations:{SET_TOKEN:function(e,t){e.token=t},SET_ROLE:function(e,t){e.role=t},SET_ROLES:function(e,t){e.roles=t}},actions:{GetMenuList:function(e){e.commit;var t=e.state;return new n.default(function(e,i){var n={time:Date.parse(new Date)/1e3,account:sessionStorage.username,auth_token:t.token};(0,a.menuList)(n).then(function(t){e(t)}).catch(function(e){i(e)})})},LogOut:function(e,t){var i=e.commit;e.state;return new n.default(function(e,n){(0,a.logout)(t).then(function(){i("SET_TOKEN",""),i("SET_ROLES",[]),i("SET_ROLE",""),sessionStorage.clear(),(0,o.removeToken)(),(0,o.removeRole)(),e()}).catch(function(e){n(e)})})},FedLogOut:function(e){var t=e.commit,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return new n.default(function(e){try{r.default.closeWebsocket(),console.log("WebSocket connection closed during logout")}catch(e){console.warn("Error closing WebSocket during logout:",e)}t("SET_TOKEN",""),t("SET_ROLES",[]),t("SET_ROLE",""),sessionStorage.clear(),(0,o.removeToken)(),(0,o.removeRole)();var n=!1!==i.triggerEvent;s.default.clearSessid(n),e()})}}};t.default=d},Dhux:function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default={data:function(){return{lang_dropdown_visible:!1}},computed:{language:function(){return this.$store.getters.language},language_text:function(){var e=this.$store.getters.language,t="Кыргызча";return"zh"===e?t="中文":"en"===e?t="English":"ru"===e?t="Русский":"ky"===e&&(t="Кыргызча"),t}},methods:{handleSetLanguage:function(e){var t=this;this.$i18n.locale=e,this.$store.dispatch("setLanguage",e).then(function(){t.$emit("handleSetLanguage",e)}),setTimeout(function(){location.reload()},100)},langListVisibleChange:function(e){this.lang_dropdown_visible=e}}}},E2t9:function(e,t,i){"use strict";var n=i("VeXQ");i.n(n).a},EKno:function(e,t,i){"use strict";i.r(t);var n=i("ywvi"),a=i("CTbN");for(var o in a)"default"!==o&&function(e){i.d(t,e,function(){return a[e]})}(o);i("E2t9");var s=i("KHd+"),r=Object(s.a)(a.default,n.a,n.b,!1,null,"18ee1ef4",null);r.options.__file="src\\components\\TipDialog\\index.vue",t.default=r.exports},ETGp:function(e,t,i){"use strict";i.r(t);var n=i("8hTN"),a=i("9TU5");for(var o in a)"default"!==o&&function(e){i.d(t,e,function(){return a[e]})}(o);i("Zt36");var s=i("KHd+"),r=Object(s.a)(a.default,n.a,n.b,!1,null,"f142be86",null);r.options.__file="src\\components\\LangSelect\\index.vue",t.default=r.exports},F35N:function(e,t,i){"use strict";i.r(t);var n=i("c8V3"),a=i("/KzS");for(var o in a)"default"!==o&&function(e){i.d(t,e,function(){return a[e]})}(o);i("WoFz");var s=i("KHd+"),r=Object(s.a)(a.default,n.a,n.b,!1,null,"6beed8bc",null);r.options.__file="src\\views\\layout\\components\\AppMain.vue",t.default=r.exports},Ff7L:function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=u(i("P2sY")),a=(i("X4fA"),i("OU4T")),o=i("7Qib"),s=u(i("Mz3J")),r=u(i("I48J")),l=u(i("kQ8I")),d=u(i("EKno")),c=u(i("CMXa"));function u(e){return e&&e.__esModule?e:{default:e}}t.default={name:"DeviceMgt",mixins:[c.default],components:{Pagination:s.default,GroupAddDialog:r.default,DeviceAddDialog:l.default,TipDialog:d.default},data:function(){return{os_attr:[],group_list:[],selected_group:-10,group_loading:!1,device_list:null,filter_device_list:[],listTitle:[{title:this.$t("cid"),name:"sn",width:"180px"},{title:this.$t("status"),name:"net_status",width:"110px"},{title:this.$t("versionNumber"),name:"version",width:"150px"},{title:this.$t("deviceNickname"),name:"alias",width:"211px"},{title:this.$t("group"),name:"group_name"}],total:0,list_loading:!0,listQuery:{page:1,limit:20},dialogTitle:"",dialogTip:"",tipRequest:"",tipParams:{},multipleSelection:[],editGroupNumber:"0",account_type:sessionStorage.account_type,searchText:"",isSuperAdmin:!1}},created:function(){this.handleAutoRelogin(),this.isSuperAdmin="1"==sessionStorage.account_type},methods:{onReloginSuccess:function(e){console.log("deviceMgt页面重登成功"),this.getGroup()},onWebsocketReady:function(){console.log("deviceMgt页面websocket准备就绪"),this.getGroup()},getDeviceOs:function(){var e=this,t={version:1,language_type:this.$store.getters.language};this.$websocket.webSocketSend("cli_os_attr_web_get",t),this.$bus.$once("cli_os_attr_web_get_rsp",function(t){"number"!=typeof t&&(e.os_attr=t.body)})},getGroup:function(){var e=this;this.group_loading=!0,this.list_loading=!0,this.group_list=[{value:-1,label:this.$t("allGroup"),selected_group_flag:!0}],(0,a.getDeviceGroupList)().then(function(t){200===t.code&&(console.log(t.datas),t.datas&&t.datas.map(function(t,i){e.group_list.push({value:t.group_id,label:t.name})}))}).catch(function(e){}).finally(function(){e.group_loading=!1,e.getDeviceList(-1)})},getDeviceList:function(e,t){var i=this,n=this.group_list;n.length>0&&n.map(function(e){i.$set(e,"selected_group_flag",!1)}),this.selected_group=e,this.group_list.map(function(t){t.value===e&&i.$set(t,"selected_group_flag",!0)}),this.list_loading=!0;var o={offset:(this.listQuery.page-1)*this.listQuery.limit,limit:this.listQuery.limit,text:this.searchText};-1!==e&&(o.group_id=e),(0,a.getDeviceList)(o).then(function(e){if(200===e.code){i.total=e.count;var t=e.datas.map(function(e){return{sn:e.cid,net:e.net,net_status:e.net<=0?i.$t("offline"):i.$t("online"),version:e.version,alias:e.name,group_name:e.group_name}});i.device_list=t}}).catch(function(e){}).finally(function(){i.list_loading=!1})},handelDeviceList:function(e,t){var i=this,n=[];-1===e?(n=t.list,this.total=t.total):t.list.map(function(t){t.group_id===e&&((n=t.sn_list).map(function(t){t.group_id=e}),i.total=t.total)});this.device_list=[],n.map(function(e,t){e.net=0,i.group_list.map(function(t){e.group_id===t.value&&(e.group_name=t.label)})}),n.length>0?this.getDeviceNet(n):(this.list_loading=!1,this.device_list=[])},handlePaginationChange:function(e){console.log(e),this.listQuery={page:e.page,limit:e.limit},this.getDeviceList(this.selected_group),this.$refs.deviceMgtLayout.scrollTo({top:0,behavior:"smooth"})},getDeviceNet:function(e){var t=this,i={};e.map(function(e){i[e.sn]=[{id:201},{id:207}]});var a={limit:e.length,asc:!0,equal:!0,req_map:i};this.$websocket.webSocketSend("pub_dp_get_muti",a),this.$bus.$once("pub_dp_get_muti_rsp",function(i){if("number"==typeof i)return t.list_loading=!1,void(t.device_list=e);var a=i.body;e.map(function(e){a[e.sn][201].length>0?e.net=JSON.parse(a[e.sn][201][0].value).net:e.net=0,e.net<=0?e.net_status=t.$t("offline"):e.net_status=t.$t("online"),a[e.sn][207].length>0?e.version=JSON.parse(a[e.sn][207][0].value).str_val:e.version="",t.os_attr.length>0&&t.os_attr.map(function(t){e.os===parseInt(t.os)&&(0,n.default)(e,t)})}),t.device_list=e,console.log(t.device_list),t.list_loading=!1})},handleColumnShow:o.handleColumnShow,addGroup:function(e){e?(this.dialogTitle=this.$t("editGroup"),this.editGroupNumber=e.value):this.dialogTitle=this.$t("addGroup"),this.$refs.groupAddDialog.show(e)},delGroup:function(e){var t=this;this.dialogTitle=this.$t("delGroup"),this.dialogTip=this.$t("sureToDelete"),this.tipRequest="cli_dev_group_del",this.tipParams={group_id:e.value},this.$refs.tipDialog.show("delGroup").then(function(i){if(i){var n={group_id:e.value};(0,a.deleteDeviceGroup)(n).then(function(e){200===e.code&&(t.$refs.tipDialog.hideDialog(),t.getGroup(),t.$message.success(t.$t("deleteSuccess")))})}})},backTip:function(e){this.$message.success(this.$t("deleteSuccess")),"delGroup"===e?this.getGroup():"delDevice"!==e&&"bulkDelDevice"!==e||this.getDeviceList(this.selected_group,"refresh")},addEditDevice:function(e){console.log(e),this.dialogTitle=e?this.$t("editDevice"):this.$t("addDevice"),this.$refs.deviceAddDialog.show(this.selected_group,e)},delDevice:function(e){var t=this;this.dialogTitle=this.$t("deleteDevice"),this.dialogTip=this.$t("sureToDelete"),this.tipRequest="cli_dev_del",this.tipParams={sn:e.sn},this.$refs.tipDialog.show("delDevice").then(function(i){if(i){var n={cid:e.sn};(0,a.deleteDevice)(n).then(function(e){200===e.code&&(t.$refs.tipDialog.hideDialog(),t.getDeviceList(t.selected_group),t.$message.success(t.$t("deleteSuccess")))})}})},handleSelectionChange:function(e){this.multipleSelection=e},delBulkDevice:function(){if(console.log(this.multipleSelection),this.multipleSelection.length<=0)this.$message.error(this.$t("bulkDelDeviceTip"));else{var e=[];this.multipleSelection.map(function(t){e.push(t.sn)}),this.dialogTitle=this.$t("bulkDelete"),this.dialogTip=this.$t("sureToDelete"),this.tipRequest="cli_dev_del_muti",this.tipParams={sn:e},this.$refs.tipDialog.show("bulkDelDevice")}},showStrategy:function(e){console.log(e),console.log(this.selected_group)},search:function(){this.getDeviceList(this.selected_group)},changeGroup:function(e){e.value!==this.selected_group&&(this.selected_group=e.value,this.searchText="",this.getDeviceList(this.selected_group))}}}},FkO0:function(e,t,i){e.exports=i.p+"static/img/main_logo.c922e5b.png"},FyMl:function(e,t,i){"use strict";i.r(t);var n=i("exBM"),a=i.n(n);for(var o in n)"default"!==o&&function(e){i.d(t,e,function(){return n[e]})}(o);t.default=a.a},"G/jE":function(e,t,i){"use strict";i.r(t);var n=i("0sKO"),a=i("f1Gg");for(var o in a)"default"!==o&&function(e){i.d(t,e,function(){return a[e]})}(o);i("ZDNR");var s=i("KHd+"),r=Object(s.a)(a.default,n.a,n.b,!1,null,"8cf2df30",null);r.options.__file="src\\views\\userMgt\\index.vue",t.default=r.exports},HRyT:function(e,t,i){"use strict";var n=i("uByC");i.n(n).a},I48J:function(e,t,i){"use strict";i.r(t);var n=i("4fdH"),a=i("svwP");for(var o in a)"default"!==o&&function(e){i.d(t,e,function(){return a[e]})}(o);i("4E/B");var s=i("KHd+"),r=Object(s.a)(a.default,n.a,n.b,!1,null,"3747eaaf",null);r.options.__file="src\\views\\deviceMgt\\groupAddDialog.vue",t.default=r.exports},I77X:function(e,t,i){"use strict";i.r(t);var n=i("Q18x"),a=i.n(n);for(var o in n)"default"!==o&&function(e){i.d(t,e,function(){return n[e]})}(o);t.default=a.a},IHaW:function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.startViewer=void 0;var n=o(i("14Xm")),a=o(i("D3Ub"));t.startViewer=function(){var e=(0,a.default)(n.default.mark(function e(t,i,o,r,l,d,c,u,h){var _,f,p,m,g,v,y,b,S,w,C,E=this;return n.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return console.log("[VIEWER] start viewer connection"),s[r]={},s[r].localView=t,s[r].remoteView=i,_=new AWS.KinesisVideo({region:o.region,accessKeyId:o.accessKeyId,secretAccessKey:o.secretAccessKey,sessionToken:o.sessionToken,endpoint:o.endpoint,correctClockSkew:!0}),e.next=7,_.describeSignalingChannel({ChannelName:o.channelName}).promise();case 7:return f=e.sent,p=f.ChannelInfo.ChannelARN,e.next=11,_.getSignalingChannelEndpoint({ChannelARN:p,SingleMasterChannelEndpointConfiguration:{Protocols:["WSS","HTTPS"],Role:KVSWebRTC.Role.VIEWER}}).promise();case 11:return m=e.sent,g=m.ResourceEndpointList.reduce(function(e,t){return e[t.Protocol]=t.ResourceEndpoint,e},{}),v=new AWS.KinesisVideoSignalingChannels({region:o.region,accessKeyId:o.accessKeyId,secretAccessKey:o.secretAccessKey,sessionToken:o.sessionToken,endpoint:g.HTTPS,correctClockSkew:!0}),e.next=16,v.getIceServerConfig({ChannelARN:p}).promise();case 16:return y=e.sent,b=[],o.natTraversalDisabled||o.forceTURN||b.push({urls:"stun:stun.kinesisvideo."+o.region+".amazonaws.com:443"}),o.natTraversalDisabled||y.IceServerList.forEach(function(e){return b.push({urls:e.Uris,username:e.Username,credential:e.Password})}),s[r].signalingClient=new KVSWebRTC.SignalingClient({channelARN:p,channelEndpoint:g.WSS,clientId:o.clientId,role:KVSWebRTC.Role.VIEWER,region:o.region,credentials:{accessKeyId:o.accessKeyId,secretAccessKey:o.secretAccessKey,sessionToken:o.sessionToken},systemClockOffset:_.config.systemClockOffset}),S=o.widescreen?{width:{ideal:1280},height:{ideal:720}}:{width:{ideal:640},height:{ideal:480}},w={video:!!o.sendVideo&&S,audio:o.sendAudio},C={iceServers:b,iceTransportPolicy:o.forceTURN?"relay":"all"},s[r].peerConnection=new RTCPeerConnection(C),o.openDataChannel&&(s[r].dataChannel=s[r].peerConnection.createDataChannel("kvsDataChannel"),s[r].peerConnection.ondatachannel=function(e){e.channel.onmessage=h}),s[r].peerConnectionStateInterval=setInterval(function(){u(r,s[r].peerConnection.connectionState)},1e4),s[r].signalingClient.on("open",(0,a.default)(n.default.mark(function e(){return n.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,navigator.mediaDevices.getUserMedia(w);case 3:s[r].localStream=e.sent,s[r].localStream.getTracks().forEach(function(e){s[r].peerConnection.addTrack(e,s[r].localStream),"audio"===e.kind&&(e.enabled=!1)}),t.srcObject=s[r].localStream,d(!0),e.next=13;break;case 9:e.prev=9,e.t0=e.catch(0),console.error("[VIEWER] Could not find webcam"),d(!1);case 13:return e.t1=s[r].peerConnection,e.next=16,s[r].peerConnection.createOffer({offerToReceiveAudio:!0,offerToReceiveVideo:!0});case 16:return e.t2=e.sent,e.next=19,e.t1.setLocalDescription.call(e.t1,e.t2);case 19:o.useTrickleICE&&s[r].signalingClient.sendSdpOffer(s[r].peerConnection.localDescription);case 20:case"end":return e.stop()}},e,E,[[0,9]])}))),s[r].signalingClient.on("sdpAnswer",function(){var e=(0,a.default)(n.default.mark(function e(t){return n.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return console.log("[VIEWER] Received SDP answer"),e.next=3,s[r].peerConnection.setRemoteDescription(t);case 3:case"end":return e.stop()}},e,E)}));return function(t){return e.apply(this,arguments)}}()),s[r].signalingClient.on("iceCandidate",function(e){console.log("[VIEWER] Received ICE candidate"),s[r].peerConnection.addIceCandidate(e)}),s[r].signalingClient.on("close",function(){console.log("Disconnected from signaling channel"),console.log(s)}),s[r].signalingClient.on("error",function(e){console.error("Signaling client error: ",e)}),s[r].peerConnection.addEventListener("icecandidate",function(e){var t=e.candidate;t?o.useTrickleICE&&s[r].signalingClient.sendIceCandidate(t):(console.log("[VIEWER] All ICE candidates have been generated"),o.useTrickleICE||s[r].signalingClient.sendSdpOffer(s[r].peerConnection.localDescription))}),s[r].peerConnection.addEventListener("track",function(e){console.log("[VIEWER] Received remote track"),i.srcObject||(s[r].remoteStream=e.streams[0],i.srcObject=s[r].remoteStream,c(r,!0))}),console.log("[VIEWER] Starting viewer connection"),s[r].signalingClient.open(),e.abrupt("return",s);case 37:case"end":return e.stop()}},e,this)}));return function(t,i,n,a,o,s,r,l,d){return e.apply(this,arguments)}}();function o(e){return e&&e.__esModule?e:{default:e}}t.stopViewer=function(e){if(console.log("[VIEWER] Stopping viewer connection"),!s[e])return;s[e].signalingClient&&(s[e].signalingClient.close(),s[e].signalingClient=null);s[e].peerConnection&&(s[e].peerConnection.close(),s[e].peerConnection=null);s[e].localStream&&(s[e].localStream.getTracks().forEach(function(e){return e.stop()}),s[e].localStream=null);s[e].remoteStream&&(s[e].remoteStream.getTracks().forEach(function(e){return e.stop()}),s[e].remoteStream=null);s[e].peerConnectionStateInterval&&(clearInterval(s[e].peerConnectionStateInterval),s[e].peerConnectionStateInterval=null);s[e].peerConnectionStatsInterval&&(clearInterval(s[e].peerConnectionStatsInterval),s[e].peerConnectionStatsInterval=null);s[e].localView&&(s[e].localView.srcObject=null);s[e].remoteView&&(s[e].remoteView.srcObject=null);s[e].dataChannel&&(s[e].dataChannel=null);return console.log(s),e};var s=[]},IJUe:function(e,t,i){"use strict";function n(e){return e<10?"0"+e:e}function a(e){var t=e.length;t<32&&(e="00000000000000000000000000000000".slice(0,32-t)+e);return e}Object.defineProperty(t,"__esModule",{value:!0}),t.formatTime=function(e,t){var i=new Date(1e3*e),a=i.getFullYear(),o=n(i.getMonth()+1),s=n(i.getDate()),r=n(i.getHours()),l=n(i.getMinutes()),d=n(i.getSeconds()),c="";c=1===t?a+"-"+o+"-"+s+" "+r+":"+l+":"+d:2===t?o+"-"+s+" "+r+":"+l+":"+d:4===t?a+"/"+o+"/"+s:5===t?r+":"+l+":"+d:6===t?a+"-"+o+"-"+s:a+"/"+o+"/"+s+" "+r+":"+l+":"+d;return c},t.addZero=n,t.handleMins=function(e){var t=[];e.map(function(e){t.push(a(e.toString(2)))});var i=t.join(""),n=[];for(var o in i)"1"===i[o]&&n.push(parseInt(o));return n},t.binaryAddZero=a},ILs8:function(e,t,i){e.exports=i.p+"static/img/bottomArrow.dc760a3.png"},"IT+0":function(e,t,i){e.exports=i.p+"static/img/arrow_next_click.6f704fb.png"},IZcc:function(e,t,i){},Ij2q:function(e,t,i){"use strict";var n=r(i("QbLZ")),a=r(i("oYx3")),o=r(i("Q2AE")),s=i("X4fA");function r(e){return e&&e.__esModule?e:{default:e}}var l=["/login"];a.default.beforeEach(function(e,t,i){if((0,s.getToken)()&&o.default.getters.token)if("/login"===e.path||"/"===e.path)o.default.commit("SET_TOKEN",""),o.default.commit("SET_ROLES",[]),o.default.commit("SET_ROLE",""),i({path:"/"}),(0,s.removeToken)(),(0,s.removeRole)();else if(0===o.default.getters.addRouters.length)if(sessionStorage.username&&""!==sessionStorage.username){var r=o.default.getters.role;o.default.dispatch("GenerateRoutes",{token:r}).then(function(){a.default.addRoutes(o.default.getters.addRouters),i((0,n.default)({},e,{replace:!0}))})}else o.default.commit("SET_TOKEN",""),o.default.commit("SET_ROLES",[]),o.default.commit("SET_ROLE",""),i({path:"/"}),(0,s.removeToken)(),(0,s.removeRole)();else i();else console.log("没有token"),-1!==l.indexOf(e.path)?i():i("/login")}),a.default.afterEach(function(){})},IjTU:function(e,t,i){"use strict";var n=function(){var e=this.$createElement,t=this._self._c||e;return t("div",{attrs:{id:"app"}},[this.isRouterAlive?t("router-view"):this._e()],1)},a=[];n._withStripped=!0,i.d(t,"a",function(){return n}),i.d(t,"b",function(){return a})},Jn5J:function(e,t,i){"use strict";var n=i("8eVs");i.n(n).a},Jvyq:function(e,t,i){e.exports=i.p+"static/img/404_cloud.0f4bc32.png"},Ktri:function(e,t,i){},L9G8:function(e,t,i){"use strict";i.r(t);var n=i("Ff7L"),a=i.n(n);for(var o in n)"default"!==o&&function(e){i.d(t,e,function(){return n[e]})}(o);t.default=a.a},LIc2:function(e,t,i){"use strict";i.r(t);var n=i("4BeY"),a=i.n(n),o=i("IaFt"),s=i.n(o),r=new a.a({id:"icon-device",use:"icon-device-usage",viewBox:"0 0 36 36",content:'<symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 36 36" id="icon-device"><defs><style>#icon-device .a{fill:#fff;stroke:#707070;opacity:0;}#icon-device .b{fill:#8a9097;stroke:#8a9097;stroke-width:0.1px;}#icon-device .c{stroke:none;}#icon-device .d{fill:none;}</style></defs><g transform="translate(-25 -259)"><g class="a" transform="translate(25 259)"><rect class="c" width="36" height="36" /><rect class="d" x="0.5" y="0.5" width="35" height="35" /></g><g transform="translate(-66.1 85.673)"><path class="b" d="M101.089,190.97h15.169a1.058,1.058,0,0,0,.977-1.122v-5.227a1.058,1.058,0,0,0-.977-1.122H101.089a1.058,1.058,0,0,0-.977,1.122v5.229A1.056,1.056,0,0,0,101.089,190.97Zm.478-5.8H115.78V189.3H101.567Zm15.018,8.279H100.827a.843.843,0,0,0,0,1.671h15.757a.843.843,0,0,0,0-1.671Zm0,4.15H100.827a.843.843,0,0,0,0,1.671h15.757a.843.843,0,0,0,0-1.671Z" /><path class="b" d="M717.835,305.835m-.835,0a.835.835,0,1,0,.835-.835A.835.835,0,0,0,717,305.835Z" transform="translate(-604.258 -118.6)" /></g></g></symbol>'});s.a.add(r);t.default=r},LWV3:function(e,t,i){},LhkO:function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getUserList=function(e){return(0,n.default)({url:"v1/miru/account/list",method:"post",data:e})},t.addUser=function(e){return(0,n.default)({url:"v1/miru/account/add",method:"post",data:e})},t.editUser=function(e){return(0,n.default)({url:"v1/miru/account/edit",method:"post",data:e})},t.deleteUser=function(e){return(0,n.default)({url:"v1/miru/account/delete",method:"post",data:e})},t.resetPassword=function(e){return(0,n.default)({url:"v1/miru/account/password/reset",method:"post",data:e})};var n=function(e){return e&&e.__esModule?e:{default:e}}(i("t3Un"))},M3qR:function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=i("Y5bG");t.default={name:"Pagination",props:{total:{required:!0,type:Number},page:{type:Number,default:1},limit:{type:Number,default:20},pageSizes:{type:Array,default:function(){return[10,20,30,50]}},layout:{type:String,default:"total, sizes, prev, pager, next, jumper"},autoScroll:{type:Boolean,default:!0},hidden:{type:Boolean,default:!1}},computed:{currentPage:{get:function(){return this.page},set:function(e){this.$emit("update:page",e)}},pageSize:{get:function(){return this.limit},set:function(e){this.$emit("update:limit",e)}}},methods:{handleSizeChange:function(e){this.$emit("pagination",{page:this.currentPage,limit:e}),this.autoScroll&&(0,n.scrollTo)(0,800)},handleCurrentChange:function(e){this.$emit("pagination",{page:e,limit:this.pageSize}),this.autoScroll&&(0,n.scrollTo)(0,800)}}}},MKk9:function(e,t,i){"use strict";i.r(t);var n=i("4BeY"),a=i.n(n),o=i("IaFt"),s=i.n(o),r=new a.a({id:"icon-system",use:"icon-system-usage",viewBox:"0 0 36 36",content:'<symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 36 36" id="icon-system"><defs><style>#icon-system .a{fill:#fff;stroke:#707070;opacity:0;}#icon-system .b{fill:#8a9097;}#icon-system .c{stroke:none;}#icon-system .d{fill:none;}</style></defs><g transform="translate(-25 -665)"><g class="a" transform="translate(25 665)"><rect class="c" width="36" height="36" /><rect class="d" x="0.5" y="0.5" width="35" height="35" /></g><g transform="translate(-84.886 593.602)"><path class="b" d="M126.476,79.707l-5.985,3.457a3.208,3.208,0,0,0-1.6,2.778v6.912a3.208,3.208,0,0,0,1.6,2.778l5.987,3.457a3.209,3.209,0,0,0,3.207,0l5.987-3.457a3.208,3.208,0,0,0,1.6-2.778V85.942a3.208,3.208,0,0,0-1.6-2.778l-5.987-3.457a3.208,3.208,0,0,0-3.207,0Zm2.311,1.557,5.986,3.456a1.413,1.413,0,0,1,.707,1.221v6.913a1.414,1.414,0,0,1-.707,1.223l-5.987,3.457a1.413,1.413,0,0,1-1.411,0l-5.987-3.457a1.413,1.413,0,0,1-.707-1.221V85.941a1.414,1.414,0,0,1,.707-1.223l5.987-3.457a1.413,1.413,0,0,1,1.411,0Z" transform="translate(0)" /><path class="b" d="M374.493,371.2a3.293,3.293,0,1,0,3.293,3.293A3.293,3.293,0,0,0,374.493,371.2Zm0,1.8a1.5,1.5,0,1,1-1.5,1.5A1.5,1.5,0,0,1,374.493,373Z" transform="translate(-246.413 -285.095)" /></g></g></symbol>'});s.a.add(r);t.default=r},MYBh:function(e,t,i){},McJg:function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=s(i("4d7F")),a=s(i("QbLZ")),o=i("oYx3");function s(e){return e&&e.__esModule?e:{default:e}}var r={state:{routers:o.constantRouterMap,addRouters:[]},mutations:{SET_ROUTERS:function(e,t){e.addRouters=t,e.routers=o.constantRouterMap.concat(t)}},actions:{GenerateRoutes:function(e,t){var i=e.commit;return new n.default(function(e){var n=t.token,s=function e(t,i){var n=[];return t.forEach(function(t){var o=(0,a.default)({},t);o.children&&(o.children=e(o.children,i)),n.push(o)}),n}(o.asyncRouterMap,n);i("SET_ROUTERS",s),e()})}}};t.default=r},MsN4:function(e,t,i){},Mz3J:function(e,t,i){"use strict";i.r(t);var n=i("25re"),a=i("6g3Z");for(var o in a)"default"!==o&&function(e){i.d(t,e,function(){return a[e]})}(o);i("VhpY");var s=i("KHd+"),r=Object(s.a)(a.default,n.a,n.b,!1,null,"72233bcd",null);r.options.__file="src\\components\\Pagination\\index.vue",t.default=r.exports},NhWU:function(e,t,i){e.exports=i.p+"static/img/arrow_prev.f84073d.png"},"O+jn":function(e,t,i){"use strict";i.r(t);var n=i("jJqW"),a=i.n(n);for(var o in n)"default"!==o&&function(e){i.d(t,e,function(){return n[e]})}(o);t.default=a.a},O8dt:function(e,t,i){},OOIL:function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=i("oQr/");Object.defineProperty(t,"Navbar",{enumerable:!0,get:function(){return o(n).default}});var a=i("F35N");function o(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(t,"AppMain",{enumerable:!0,get:function(){return o(a).default}})},OU4T:function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.editDeviceGroup=function(e){return(0,n.default)({url:"v1/miru/camera/group/edit",method:"post",data:e})},t.getDeviceGroupList=function(e){return(0,n.default)({url:"v1/miru/camera/group/list",method:"post",data:e})},t.getDeviceGroupDetail=function(e){return(0,n.default)({url:"v1/miru/camera/group/info",method:"post",data:e})},t.getDeviceList=function(e){return(0,n.default)({url:"v1/miru/camera/list",method:"post",data:e})},t.editDeviceName=function(e){return(0,n.default)({url:"v1/miru/camera/edit",method:"post",data:e})},t.deleteDevice=function(e){return(0,n.default)({url:"v1/miru/camera/delete",method:"post",data:e})},t.deleteDeviceGroup=function(e){return(0,n.default)({url:"v1/miru/camera/group/delete",method:"post",data:e})};var n=function(e){return e&&e.__esModule?e:{default:e}}(i("t3Un"))},"ObO/":function(e,t,i){"use strict";var n=i("67KD");i.n(n).a},OneV:function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=i("Yfch"),a=i("OU4T");t.default={name:"DeviceAddDialog",props:{title:{type:String,default:""}},data:function(){var e=this;return{dialogVisible:!1,dialogForm:{rules:{sn:[{required:!0,validator:function(t,i,a){16!==i.trim().length?a(new Error(e.$t("snEnterTip"))):(0,n.validSN)(i.trim())?a():a(new Error(e.$t("snEnterTip")))},trigger:"blur"}],code:[{required:!0,message:this.$t("cannotEmpty"),trigger:"blur"}],name:[{required:!0,message:this.$t("cannotEmpty"),trigger:"blur"}]},data:{sn:"",code:"",name:""}},group_id:-10,is_edit:!1,loading:!1}},methods:{show:function(e,t){console.log(e),this.dialogVisible=!0,t?(this.is_edit=!0,this.dialogForm.data.sn=t.sn,this.dialogForm.data.name=t.alias):this.is_edit=!1,this.group_id=e,this.$refs&&this.$refs.dialogForm&&this.$refs.dialogForm.clearValidate()},hideDialog:function(){this.dialogVisible=!1,this.dialogForm.data.sn="",this.dialogForm.data.code="",this.dialogForm.data.name=""},submit:function(){var e=this;e.$refs.dialogForm.validate(function(t){if(!t)return console.log("Error!"),!1;var i={cid:e.dialogForm.data.sn,name:e.dialogForm.data.name};(0,a.editDeviceName)(i).then(function(t){console.log(t),200===t.code&&(e.$message.success(e.$t("editSuccess")),e.hideDialog(),e.$parent.getDeviceList(e.group_id,"refresh"))}).catch(function(e){console.log(e)}).finally(function(){e.loading=!1})})}}}},P6u4:function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default={mgtPlat:"MIRU",mgtPlatTip:"Real-time Video Surveillance System",scanCodeToLogIn:"Scan code to log in",scanCodeTip:"Use the APP bound with the device to scan the code to log in",qrcodeExpired:"QR code is invalid",username:"Username",password:"Password",passwordTip1:"Please enter your password",rememberPwd:"Remember your password",pleaseEnterAccount:"Please enter your account number",pleaseEnterCorrectPwd:"Password must be 6 to 128 characters!",logIn:"LogIn",account:"Account",accountTip:"Enter 6-20 digits or letters.",pleaseEnterCorrectPwdLen:"Password length must be greater than or equal to 6 digits",pleaseEnterCorrectPwd18:"Password must be 6 to 18 characters!",verificationCode:"Verification Code",verificationCodeTip:"Please enter the verification code.",verificationCodeError:"Verification code error",home:"Home",deviceManagement:"Device Management",userManagement:"User Management",systemSetting:"System Setting",operationLogs:"Operation Logs",quitTitle:"Sign Out",quitTip:"Are you sure you want to quit?",submit:"Submit",confirm:"Confirm",sure:"Confirm",cancel:"Cancel",search:"Search",inquire:"Search",nodata:"No Data",cannotEmpty:"Can not be empty",operate:"Operation",modify:"Edit",export:"Export",edit:"Edit",delete:"Delete",sureToDelete:"Are you sure you want to delete?",deleteSuccess:"Successfully deleted",addSuccess:"Added successfully",addFail:"Add failed",remove:"Remove",sureToRemove:"Are you sure you want to remove?",removeSuccess:"Removed successfully",editSuccess:"Edited successfully",tip:"Tip",noMore:"No more",getLogSuccess:"Get the log successfully",all:"All",modifySuccess:"Successfully modified",noData:"Do data",refresh:"Refresh",save:"Save",to:"To",startDate:"Start Date",endDate:"End Date",pleaseSelect:"Please select",errCode120:"Unknown error",errCode121:"Database error",errCode122:"Session timeout",errCode123:"Message format error",errCode124:"The message rate exceeds the limit, please control the reasonable flow rate (100 messages per second)",errCode125:"Wrong parameters",errCode126:"An error occurred when calling the robot API",errCode127:"vid vkey error",errCode128:"Calling robot API returns data error",errCode129:"Not signed in, need to sign in to perform this action",errCode161:"Account or password incorrect",errCode165:"The account is already signed in to another device.",errCode200:"CID does not exist",errCode202:"The device alias already exists",errCode203:"The device is not bound",errCode204:"The device is already bound to another account",errCode205:"Device verification codes do not match",errCode206:"The device is bound by itself",errCode214:"The device has already been bound by another APP",errCode215:"The device is not online",errCode290:"Random code does not exist",errCode291:"The random code has expired",errCode292:"Status is incorrect",errCode401:"Please log in again",errCode500:"Server error",errCode1000:"Request error",errCode1001:"Request error",errCode1002:"Permission denied!",errCode1004:"Туташуу катасы",errCode1100:"Duplicate Name",errCode1101:"Account already exists",errCode1102:"The current password is incorrect",errCode2008:"During SD card formatting",errCode2011:"SD card reading failed",errCode2030:"SD card historical video has been read",errCode2031:"Failed to read historical video from SD card",errCode2032:"Failed to read SD card historical video card",timeout:"Network error",networkError:"Network error",allGroup:"All Group",noDevice:"No Device",online:"Online",offline:"Offline",deviceLoading:"Loading device list···",videoLoading:"Loading video···",failPlay:"Play failed",deviceOffline:"Device Offline",noHistoricalVideo:"The device has no historical video",noOssVideo:"The device has no cloud storage",retry:"Retry",incall:"In call···",screen1:"1-screen",screens4:"4-screen",screens9:"9-screen",screens16:"16-screen",screens25:"25-screen",liveVideo:"Live Video",cloudStorage:"Cloud Storage",playback:"Play Back",todayVideoOver:"Video of today is over",movementDetected:"Motion detected",soundDetected:"Sound detected",humanDetected:"Humanoid detected",noAudioEnabled:"Microphone permission is not turned on",alarmMessages:"Alarm messages",noMessages:"No news",chooseDevice:"Please select a device",exceedMax:"The current maximum number of viewers has been reached",previousPage:"Previous page",nextPage:"Next page",carousel:"Carousel",refreshList:"Refresh List",refreshListTipCon:"Are you sure you want to refresh the list?",refreshListTipCon1:"Note: After refreshing the list, the first page of device screens will be played.",refreshSuccess:"Refreshed successfully",rebootDevice:"Restart Device",rebootDeviceTipCon:"Are you sure you want to restart the device?",scrolledToEnd:"Already scrolled to the bottom",clickSingleDeviceTip:"Carousel status cannot click device.",addGroup:"Add group",deviceGroupLoading:"Loading device group list···",addDevice:"Add device",strategicAttributes:"Strategic Attributes",bulkDelete:"Bulk delete",status:"Status",cid:"Device CID",versionNumber:"Version number",deviceNickname:"Device nickname",group:"Group",strategy:"Strategy",editGroup:"Edit group",groupName:"Group name",groupNumber:"Group number",authorizedUser:"Authorized user",groupNameLimit:"Length limit 20 characters",delGroup:"Delete a group",editDevice:"Edit the device",sn:"Device SN",snEnter:"Please enter SN",snEnterTip:"Please enter a valid 12-digit SN code",deviceVerificationCode:"Device verification code",deviceCodeEnter:"Please enter the verification code",deviceNameEnter:"Please enter a device nickname",deleteDevice:"Delete the device",bulkDelDeviceTip:"Please tick the device you want to delete",device:"Device",deviceSearchTip:"Device CID/Device nickname",delGroupHasDeviceTip:"There are devices in the group, so it cannot be deleted",nickName:"Nick Name",mark:"Mark",addUser:"Add User",resetPassword:"Reset Password",inputLengthLimit:"Length limit {limit} characters",editUser:"Edit User",deleteUser:"Delete User",sureToReset:'Are you sure you want to reset the password to "123456"?',bulkDelUserTip:"Please tick the user you want to delete",userLimitTip:"Limit 5 users",modifyPassword:"Modify Password",passwordNotMatching:"Password does not match",oldPassword:"Current Password",newPassword:"New Password",checkPassword:"Confirm Password",passwordSame:"The new password is the same as the old password",operationTime:"Operation Time",operationType:"Operation Type",editDeviceGroup:"Edit Device Group",operationContent:"Operation Content",saveVideo:"Save video",time:"Time",startTime:"Start time",endTime:"End time",empty:"Clear",timerangeexceeds:"The selection range exceeds the limit. Please delete videos in Video Management or make a new selection!",novideo:"No videos found in the current time range, please select again!",videomanagement:"Video Management",selectgroup:"Please select a group",allselect:"Select All",download:"Download",downloading:"Downloading...",downloadSuccess:"Download successful",downloadFailed:"Download failed",saveVideoSuccess:"Save video successfully",timeLimit:"Up to 100 minutes of video can be saved",Uploading:"File Uploading",warnings:"Warning!",ok:"OK",cancelVideoSave:"Cancel saving the video",cancelVideoSaveTip:"Are you sure you want to cancel saving the video?",loading:"Loading...",minute:"min",permissionLevel:"Permission Level",manager:"Manager",guest:"Guest"}},PWg7:function(e,t,i){"use strict";i.r(t);var n=i("om6w"),a=i("L9G8");for(var o in a)"default"!==o&&function(e){i.d(t,e,function(){return a[e]})}(o);i("B4CH");var s=i("KHd+"),r=Object(s.a)(a.default,n.a,n.b,!1,null,"563cbf86",null);r.options.__file="src\\views\\deviceMgt\\index.vue",t.default=r.exports},PYSj:function(e,t,i){"use strict";var n=i("+7Mh");i.n(n).a},Pf3K:function(e,t,i){"use strict";i.r(t);var n=i("IjTU"),a=i("I77X");for(var o in a)"default"!==o&&function(e){i.d(t,e,function(){return a[e]})}(o);i("XAuw");var s=i("KHd+"),r=Object(s.a)(a.default,n.a,n.b,!1,null,null,null);r.options.__file="src\\App.vue",t.default=r.exports},Ptsr:function(e,t,i){"use strict";i.r(t);var n=i("iqJH"),a=i("vOrs");for(var o in a)"default"!==o&&function(e){i.d(t,e,function(){return a[e]})}(o);i("ObO/");var s=i("KHd+"),r=Object(s.a)(a.default,n.a,n.b,!1,null,"6691723a",null);r.options.__file="src\\views\\systemSetting\\index.vue",t.default=r.exports},Q18x:function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=o(i("Kw5r")),a=o(i("65Ft"));function o(e){return e&&e.__esModule?e:{default:e}}n.default.prototype.$bus=new n.default,t.default={name:"App",provide:function(){return{reload:this.reload}},data:function(){return{isRouterAlive:!0}},created:function(){this.setupLogoutListener()},methods:{reload:function(){this.isRouterAlive=!1,this.$nextTick(function(){this.isRouterAlive=!0})},setupLogoutListener:function(){var e=this;a.default.onLogout(function(){console.log("Received logout event from another tab"),e.$route&&"/login"!==e.$route.path&&(console.log("Redirecting to login page due to logout in another tab"),e.$store.dispatch("FedLogOut",{triggerEvent:!1}).then(function(){e.$router.replace("/login")}).catch(function(t){console.error("Error during logout:",t),e.$router.replace("/login")}))}),a.default.onLogin(function(t){console.log("Received login event from another tab, sessid:",t),e.$route&&"/login"===e.$route.path&&(console.log("Auto login triggered by another tab login"),e.$bus.$emit("autoLoginFromAnotherTab",t))})}}}},Q2AE:function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=d(i("Kw5r")),a=d(i("L2JU")),o=d(i("2c2J")),s=d(i("McJg")),r=d(i("D5rb")),l=d(i("lNWa"));function d(e){return e&&e.__esModule?e:{default:e}}n.default.use(a.default);var c=new a.default.Store({modules:{app:o.default,permission:s.default,user:r.default},getters:l.default});t.default=c},"Q7z+":function(e,t,i){"use strict";i.r(t);var n=i("4BeY"),a=i.n(n),o=i("IaFt"),s=i.n(o),r=new a.a({id:"icon-app",use:"icon-app-usage",viewBox:"0 0 36 36",content:'<symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 36 36" id="icon-app"><defs><style>#icon-app .a{fill:#fff;stroke:#707070;opacity:0;}#icon-app .b{fill:#8a9097;stroke:#8a9097;stroke-width:0.8px;}#icon-app .c{stroke:none;}#icon-app .d{fill:none;}</style></defs><g transform="translate(-25 -455)"><g class="a" transform="translate(25 455)"><rect class="c" width="36" height="36" /><rect class="d" x="0.5" y="0.5" width="35" height="35" /></g><path class="b" d="M74.828,65.828a9,9,0,1,0,9,9,9,9,0,0,0-9-9Zm.031,17.23a8.2,8.2,0,1,1,8.2-8.2,8.2,8.2,0,0,1-8.2,8.2ZM69.113,77l1.6-4.171h.595L73.016,77h-.629L71.9,75.741H70.157L69.7,77Zm1.2-1.713H71.73L71.3,74.136q-.2-.526-.3-.865a5.3,5.3,0,0,1-.225.8ZM73.457,77V72.833h1.573a3.783,3.783,0,0,1,.635.04,1.252,1.252,0,0,1,.515.195,1.023,1.023,0,0,1,.335.4,1.357,1.357,0,0,1-.212,1.47,1.624,1.624,0,0,1-1.223.368h-1.07V77h-.552Zm.552-2.188h1.078a1.145,1.145,0,0,0,.76-.2.71.71,0,0,0,.225-.561.754.754,0,0,0-.132-.448.618.618,0,0,0-.349-.246,2.354,2.354,0,0,0-.515-.037H74.009v1.491h0ZM77.344,77V72.833h1.573a3.782,3.782,0,0,1,.634.04,1.255,1.255,0,0,1,.515.195,1.024,1.024,0,0,1,.334.4,1.359,1.359,0,0,1-.212,1.47,1.624,1.624,0,0,1-1.223.368H77.9V77h-.552Zm.552-2.188h1.078a1.144,1.144,0,0,0,.759-.2.709.709,0,0,0,.225-.561.754.754,0,0,0-.132-.448.618.618,0,0,0-.349-.246,2.351,2.351,0,0,0-.515-.037H77.9v1.491Z" transform="translate(-31.328 397.837)" /></g></symbol>'});s.a.add(r);t.default=r},R7BX:function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default={name:"SvgIcon",props:{iconClass:{type:String,required:!0},className:{type:String,default:""}},computed:{iconName:function(){return"#icon-"+this.iconClass},svgClass:function(){return this.className?"svg-icon "+this.className:"svg-icon"}}}},RoGb:function(e,t,i){e.exports=i.p+"static/img/editIcon.bd8dd3a.png"},TE6u:function(e,t,i){},"UK+I":function(e,t,i){"use strict";var n=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"webRTCVideo"},[n("div",{staticClass:"deviceBox"},[e.group_list.length>0?n("div",{staticClass:"deviceGroupBox"},[n("el-select",{ref:"groupSelector",attrs:{filterable:""},on:{change:e.filterDeviceList,"visible-change":e.handleGroupFocus},model:{value:e.selected_group,callback:function(t){e.selected_group=t},expression:"selected_group"}},e._l(e.group_list,function(t){return n("el-option",{key:t.group_id,attrs:{label:t.alias,disabled:e.selected_group===t.group_id,value:t.group_id}})})),e._v(" "),n("div",{staticClass:"loadImg pointer",on:{click:function(t){e.showDialog("refresh")}}})],1):e._e(),e._v(" "),n("div",{staticClass:"deviceListCon"},[e.device_loading?n("div",{staticClass:"deviceLoadingBox"},[n("img",{staticClass:"loadingIcon",attrs:{src:i("sQSC")}}),e._v(" "),n("div",[e._v(e._s(e.$t("deviceLoading")))])]):0===e.filter_device_list.length?n("div",{staticClass:"noDeviceBox"},[n("img",{staticClass:"noDeviceIcon",attrs:{src:i("o5Jc")}}),e._v(" "),n("span",[e._v(e._s(e.$t("noDevice")))])]):e._l(e.filter_device_list,function(t,a){return n("div",{key:a,staticClass:"deviceItem pointer",class:{selectedDeviceVideo:t.selected_device_flag,selectedSingleDeviceVideo:t.selected_single_device_flag},on:{click:function(i){i.stopPropagation(),e.selectSingleDevice(t)}}},[n("div",{staticClass:"deviceItemLeft"},[n("img",{staticClass:"deviceIcon",attrs:{src:i("dZcW")}}),e._v(" "),n("span",{staticClass:"lineClamp2",attrs:{title:t.alias?t.alias:t.sn},domProps:{textContent:e._s(t.alias?t.alias:t.sn)}})]),e._v(" "),n("span",{staticClass:"dot",class:t.net>0?"onDot":"offDot"})])})],2)]),e._v(" "),n("div",{staticClass:"playVideoBox"},[n("div",{staticClass:"liveModeBox"},[n("div",{ref:"videoBoxOutter",staticClass:"videoBoxOutter",class:e.isVideoOutBoxFulling?"full":"",on:{click:e.onClickVideoBoxOutter}},[n("div",{staticClass:"videoBox ossVideoBox",class:e.fulling?"fullingBox":""},e._l(e.video_list,function(t,a){return n("div",{key:a,staticClass:"videoItem",class:{videoItem1:1===e.multi_screen,videoItem4:4===e.multi_screen,videoItem9:9===e.multi_screen,videoItem16:16===e.multi_screen,videoItem25:25===e.multi_screen,selectedSingleDeviceVideoBox:t.selected_single_device_flag},on:{mouseover:function(i){e.onMouseEnterVideoItem(t)},mouseleave:function(i){e.ouMouseLeaveVideoItem(t)},click:function(i){i.stopPropagation(),e.handleSelectedSingleDevice(t)}}},[n("div",{ref:t.video_con_ref,refInFor:!0,staticClass:"video-container",class:e.fullingSn==t.sn&&e.fulling_single_video?"full":""},[t.remote_stream_loading?n("div",{staticClass:"videoLoadingBox"},[n("img",{staticClass:"loadingIcon",attrs:{src:i("sQSC")}}),e._v(" "),n("div",[e._v(e._s(e.$t("videoLoading")))])]):2===t.play_state?n("div",{staticClass:"videoFailBox"},[n("img",{staticClass:"playFailIcon",attrs:{src:i("2WRI")}}),e._v(" "),n("div",[n("div",[e._v(e._s(e.$t("failPlay")))]),e._v(" "),n("div",{staticClass:"retryBtn pointer",on:{click:function(i){i.stopPropagation(),e.toPlayFlvVideo(a,t.device_index)}}},[e._v("\n                    "+e._s(e.$t("retry"))+"\n                  ")])])]):3===t.play_state?n("div",{staticClass:"videoFailBox"},[n("img",{staticClass:"playFailIcon",attrs:{src:i("zoDt")}}),e._v(" "),n("div",[e._v(e._s(e.$t("deviceOffline")))])]):4===t.play_state?n("div",{staticClass:"videoFailBox"},[n("img",{staticClass:"playFailIcon",attrs:{src:i("YHMm")}}),e._v(" "),n("div",[e._v(e._s(e.$t("Uploading")))])]):e._e(),e._v(" "),n("video",{directives:[{name:"show",rawName:"v-show",value:t.flvPlayer_flag,expression:"item.flvPlayer_flag"}],ref:t.video_ref,refInFor:!0,staticClass:"remote-view",attrs:{autoplay:"",disablePictureInPicture:""},domProps:{muted:t.muted}}),e._v(" "),n("div",{directives:[{name:"show",rawName:"v-show",value:t.sn&&t.show_headwrap_operation,expression:"item.sn && item.show_headwrap_operation"}],staticClass:"videoWrapHead"},[n("div",{staticClass:"videoWrapHeadLeft text-nowrap"},[n("span",{staticClass:"deviceName text-nowrap",domProps:{textContent:e._s(t.alias?t.alias:t.sn)}}),e._v(" "),t.net>0?n("span",{staticClass:"deviceNetStatus deviceOnline"},[e._v(e._s(e.$t("online")))]):n("span",{staticClass:"deviceNetStatus deviceOffline"},[e._v(e._s(e.$t("offline")))])]),e._v(" "),n("div",{staticClass:"videoWrapHeadRight"},[t.savingVideo?n("el-tooltip",{staticClass:"item",attrs:{content:e.$t("cancelVideoSave"),effect:"dark",placement:"top"}},[n("div",{staticClass:"saveVideoIcon cancelSaveVideoIcon pointer",on:{click:function(i){i.stopPropagation(),e.showCancelSaveVideoPop(t)}}})]):n("el-tooltip",{staticClass:"item",attrs:{content:e.$t("saveVideo"),effect:"dark",placement:"top"}},[e.isSuperAdmin?n("div",{staticClass:"saveVideoIcon pointer",on:{click:function(i){i.stopPropagation(),e.showSaveVideoPop(t)}}}):e._e()]),e._v(" "),e.isSuperAdmin||e.isNornalAdmin?n("div",{staticClass:"stopVideoImg pointer",on:{click:function(i){i.stopPropagation(),e.showDialog("reboot",t)}}}):e._e()],1)]),e._v(" "),t.show_PTZ_status&&t.show_headwrap_operation&&(e.isSuperAdmin||e.isNornalAdmin)?n("div",{staticClass:"showPtzBox",on:{click:function(e){e.stopPropagation()}}},[n("div",{staticClass:"videoWrapDirection"},[n("div",{staticClass:"directionBtnBox"},[n("div",{staticClass:"directionBtn directionLeftBtn",on:{mousedown:function(i){e.direction(3,t.channel_name)},mouseup:function(i){e.stopDirection(t.channel_name)}}}),e._v(" "),n("div",{staticClass:"directionBtn directionUpBtn",on:{mousedown:function(i){e.direction(1,t.channel_name)},mouseup:function(i){e.stopDirection(t.channel_name)}}}),e._v(" "),n("div",{staticClass:"directionBtn directionDownBtn",on:{mousedown:function(i){e.direction(2,t.channel_name)},mouseup:function(i){e.stopDirection(t.channel_name)}}}),e._v(" "),n("div",{staticClass:"directionBtn directionRightBtn",on:{mousedown:function(i){e.direction(4,t.channel_name)},mouseup:function(i){e.stopDirection(t.channel_name)}}})])])]):e._e(),e._v(" "),n("div",{directives:[{name:"show",rawName:"v-show",value:t.flvPlayer_flag&&t.show_headwrap_operation,expression:"item.flvPlayer_flag && item.show_headwrap_operation"}],staticClass:"cameraControl",on:{click:function(e){e.stopPropagation()}}},[t.show_PTZ_status&&(e.isSuperAdmin||e.isNornalAdmin)?n("img",{staticClass:"ptzIcon pointer",attrs:{src:i("XLtf")},on:{click:function(i){i.stopPropagation(),e.showPTZ(t,!1)}}}):e._e(),e._v(" "),t.show_PTZ_status||!e.isSuperAdmin&&!e.isNornalAdmin?e._e():n("div",{staticClass:"ptzIcon showPtzIcon pointer",on:{click:function(i){i.stopPropagation(),e.showPTZ(t,!0)}}}),e._v(" "),e.fulling?e._e():n("div",{staticClass:"fullingSingleVideoBox",on:{click:function(i){i.stopPropagation(),e.fulling_single_video?e.exitFullscreen():e.fullSingleScreen(t)}}},[e.fulling_single_video?n("div",{staticClass:"exitFullSingleScreenIcon pointer"}):n("div",{staticClass:"fullScreenSingleIcon pointer"})])])])])})),e._v(" "),n("div",{directives:[{name:"show",rawName:"v-show",value:e.isShowModeFooter,expression:"isShowModeFooter"}],staticClass:"viderModeFooter",class:e.fulling?"fullingBox":"",on:{mouseover:e.onMouseEnterFooterHolder,mouseleave:e.ouMouseLiveFooter}},[n("div",{staticClass:"multiScreenBox"},[n("div",{staticClass:"multiScreenItem",class:{selectedMultiScreen:1===e.multi_screen},on:{click:function(t){t.stopPropagation(),e.changeVideoBox(1)}}},[e._v("\n              "+e._s(e.$t("screen1"))+"\n            ")]),e._v(" "),n("div",{staticClass:"multiScreenItem",class:{selectedMultiScreen:4===e.multi_screen},on:{click:function(t){t.stopPropagation(),e.changeVideoBox(4)}}},[e._v("\n              "+e._s(e.$t("screens4"))+"\n            ")]),e._v(" "),n("div",{staticClass:"multiScreenItem",class:{selectedMultiScreen:9===e.multi_screen},on:{click:function(t){t.stopPropagation(),e.changeVideoBox(9)}}},[e._v("\n              "+e._s(e.$t("screens9"))+"\n            ")]),e._v(" "),n("div",{staticClass:"multiScreenItem",class:{selectedMultiScreen:16===e.multi_screen},on:{click:function(t){t.stopPropagation(),e.changeVideoBox(16)}}},[e._v("\n              "+e._s(e.$t("screens16"))+"\n            ")]),e._v(" "),n("div",{staticClass:"multiScreenItem",class:{selectedMultiScreen:25===e.multi_screen},on:{click:function(t){t.stopPropagation(),e.changeVideoBox(25)}}},[e._v("\n              "+e._s(e.$t("screens25"))+"\n            ")])]),e._v(" "),n("div",{staticClass:"pageChangeBox"},[n("div",{staticClass:"multiScreenItem",on:{click:function(t){t.stopPropagation(),e.changePreviousPage()},mousedown:function(t){e.onHoverPrev(!0)},mouseup:function(t){e.onHoverPrev(!1)}}},[e.isHoveringPrev?n("img",{attrs:{src:i("984b"),alt:""}}):n("img",{attrs:{src:i("NhWU"),alt:""}})]),e._v(" "),n("div",{staticClass:"multiScreenItem",on:{click:function(t){t.stopPropagation(),e.changeNextPage()},mousedown:function(t){e.onHoverNext(!0)},mouseup:function(t){e.onHoverNext(!1)}}},[e.isHoveringNext?n("img",{attrs:{src:i("IT+0"),alt:""}}):n("img",{attrs:{src:i("jYtb"),alt:""}})])]),e._v(" "),n("div",{staticClass:"carouselBox",on:{click:function(e){e.stopPropagation()}}},[n("el-switch",{attrs:{"active-color":"var(--color-primary)","inactive-color":"var(--color-neutral-500)"},on:{change:function(t){e.checkCarouselSwitch()}},model:{value:e.carousel_switch,callback:function(t){e.carousel_switch=t},expression:"carousel_switch"}}),e._v(" "),n("span",{staticClass:"carouselText"},[e._v(e._s(e.$t("carousel")))])],1),e._v(" "),n("div",{staticStyle:{flex:"1"}}),e._v(" "),n("div",{staticClass:"rightControlBox",class:{fullingBox:e.fulling},on:{click:function(t){t.stopPropagation(),e.fulling?e.exitFullscreen():e.fullScreen()}}},[e.fulling?n("div",{staticClass:"exitFullScreenIcon pointer"}):n("div",{staticClass:"fullScreenIcon pointer"})])]),e._v(" "),e.fulling?n("div",{directives:[{name:"show",rawName:"v-show",value:!e.isShowModeFooter,expression:"!isShowModeFooter"}],staticClass:"modeFooterHolder",on:{mouseenter:e.onMouseEnterFooterHolder}}):e._e(),e._v(" "),e.fulling||e.fulling_single_video?n("div",{directives:[{name:"show",rawName:"v-show",value:e.dialog_visible||e.dialogSaveVideo_visible,expression:"dialog_visible || dialogSaveVideo_visible"}],staticClass:"tipDialogMask"}):e._e()]),e._v(" "),n("div",{staticStyle:{display:"none"}},[n("div",{staticClass:"video-container"},[n("video",{ref:"localView",staticClass:"local-view",attrs:{autoplay:"",playsinline:"",controls:"",disablePictureInPicture:""}})])])])]),e._v(" "),n("el-dialog",{staticClass:"tipDialog",attrs:{title:e.dialog_title,visible:e.dialog_visible,"append-to-body":!1,width:"340px"},on:{"update:visible":function(t){e.dialog_visible=t},close:function(t){e.closeDialog()}}},[n("section",[n("div",{staticClass:"dialogCont"},[n("div",[e._v(e._s(e.dialog_content))]),e._v(" "),n("div",[e._v(e._s(e.dialog_tip))])])]),e._v(" "),n("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[n("el-button",{staticClass:"tipSubmitBtn",attrs:{loading:e.submit_loading,type:"primary"},on:{click:function(t){t.stopPropagation(),e.submitDialog()}}},[e._v(e._s(e.$t("confirm")))]),e._v(" "),n("el-button",{staticClass:"tipCancelBtn",attrs:{type:"info"},on:{click:function(t){t.stopPropagation(),e.closeDialog()}}},[e._v(e._s(e.$t("cancel")))])],1)]),e._v(" "),n("el-dialog",{staticClass:"saveVideoDialog",attrs:{title:e.saveVideo_title,visible:e.dialogSaveVideo_visible,"append-to-body":!1,width:"512px"},on:{"update:visible":function(t){e.dialogSaveVideo_visible=t},close:function(t){e.closeDialog("cancelSaveVideo")}}},[n("div",{staticClass:"saveVideoBox"},[n("div",{staticClass:"saveVideoTitle"},[e._v(e._s(e.$t("time")))]),e._v(" "),n("div",{staticClass:"datePickerBox"},[n("el-date-picker",{staticStyle:{width:"200px"},attrs:{placeholder:e.$t("startTime"),"picker-options":e.pickerOptionsBegin,type:"datetime",format:"yyyy-MM-dd HH:mm"},on:{change:e.onBeginTimeChange},model:{value:e.saveVideoBeginTime,callback:function(t){e.saveVideoBeginTime=t},expression:"saveVideoBeginTime"}}),e._v(" "),n("span",[e._v("~")]),e._v(" "),n("el-select",{staticStyle:{width:"160px"},attrs:{placeholder:e.$t("pleaseSelect")},model:{value:e.selectedTimeRange,callback:function(t){e.selectedTimeRange=t},expression:"selectedTimeRange"}},e._l(e.downloadTimeRange,function(e){return n("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})}))],1),e._v(" "),n("div",{staticClass:"saveVideoTip"},[e._v(e._s(e.$t("timeLimit")))]),e._v(" "),n("div",{staticStyle:{flex:"1"}}),e._v(" "),n("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[n("el-button",{staticClass:"tipCancelBtn",on:{"!click":function(t){e.closeDialog("cancelSaveVideo")}}},[e._v(e._s(e.$t("cancel")))]),e._v(" "),n("el-button",{staticClass:"tipSubmitBtn",attrs:{loading:e.isSubmitting},on:{"!click":function(t){return e.submitSaveVideo(t)}}},[e._v(e._s(e.$t("sure")))])],1)])]),e._v(" "),n("tip-dialog",{ref:"tipDialog",attrs:{title:e.tip_dialog_title,tip:e.tip_dialog_content,one_btn_text:e.tip_dialog_btn_text,request:e.tip_dialog_request,params:e.tip_dialog_params},on:{handleTip:e.handleTip,handleCancel:e.handleTipCancel}})],1)},a=[];n._withStripped=!0,i.d(t,"a",function(){return n}),i.d(t,"b",function(){return a})},UMfv:function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=h(i("gDS+")),a=h(i("P2sY")),o=h(i("QbLZ")),s=h(i("m1cH")),r=i("OU4T"),l=(i("vjVj"),i("X4fA")),d=h(i("YV6/")),c=(h(i("eOPU")),i("IHaW"),i("IJUe"),i("5Hul")),u=h(i("rseg"));function h(e){return e&&e.__esModule?e:{default:e}}var _=null;t.default={name:"Video",components:{TipDialog:u.default},data:function(){return{account_type:sessionStorage.account_type,group_dropdown_visible:!1,group_list:[],selected_group:"",selected_group_label:"",device_loading:!0,all_device_list:[],filter_device_list:[],video_list:[],multi_screen:25,single_selected_device:{},single_selected_video:{},viewer:[],fulling:!1,flvPlayer:[],flv_heartbeat_time:1e4,flv_loading_time:1e4,carousel_switch:!1,carousel_time:1e4,carousel_interval:null,page_num:1,dialog_title:"",dialog_visible:!1,dialog_content:"",dialog_tip:"",dialog_type:"",reboot_device:{},submit_loading:!1,isShowModeFooterWithClick:!1,isShowModeFooterWithMouse:!1,isHoveringPrev:!1,isHoveringNext:!1,fulling_single_video:!1,session_video_url:JSON.parse(sessionStorage.videoUrl),saveVideo_title:this.$t("saveVideo"),dialogSaveVideo_visible:!1,saveVideoBeginTime:new Date,saveVideoEndTime:new Date,isSubmitting:!1,saveVideoSn:"",pickerOptionsBegin:{disabledDate:function(e){return e.getTime()>Date.now()}},pickerOptionsEnd:{disabledDate:function(e){return e.getTime()>Date.now()}},isVideoOutBoxFulling:!1,play_delay_reload_time:6e5,play_fail_reload_time:15e3,tip_dialog_title:"",tip_dialog_content:"",tip_dialog_btn_text:"",tip_dialog_request:c.getDeviceVideo,tip_dialog_params:{},downloadTimeRange:[{value:"1",label:"1 "+this.$t("minute")},{value:"2",label:"2 "+this.$t("minute")},{value:"3",label:"3 "+this.$t("minute")},{value:"4",label:"4 "+this.$t("minute")},{value:"5",label:"5 "+this.$t("minute")},{value:"6",label:"6 "+this.$t("minute")},{value:"7",label:"7 "+this.$t("minute")},{value:"8",label:"8 "+this.$t("minute")},{value:"9",label:"9 "+this.$t("minute")},{value:"10",label:"10 "+this.$t("minute")}],selectedTimeRange:"1",fullingSn:"",isSuperAdmin:!1,isNornalAdmin:!1}},computed:{isShowModeFooter:function(){return this.fulling&&(this.isShowModeFooterWithClick||this.isShowModeFooterWithMouse)||!this.fulling}},watch:{},created:function(){var e=this;this.video_list=[].concat(Array(this.multi_screen).fill({})),console.log(window.location.protocol),console.log("sessionStorage.home："+sessionStorage.home),sessionStorage.home&&"1"===sessionStorage.home?(console.log("刷新"),this.$websocket&&this.$websocket.closeWebsocket(),sessionStorage.username&&""!==sessionStorage.username&&(this.$websocket.initWebSocket("refreshReconnection"),this.$bus.$once("cli_miru_login_rsp",function(t){e.$store.commit("SET_TOKEN",t.body.sessid),(0,l.setToken)(t.body.sessid),e.getDeviceList()}))):(console.log("登录进来"),this.checkWebsocket()),this.$nextTick(function(){["fullscreenchange","mozfullscreenchange","webkitfullscreenchange","msfullscreenchange"].forEach(function(t){window.addEventListener(t,function(){return e.fullScreenChange()})})}),this.isSuperAdmin="1"==sessionStorage.account_type,this.isNornalAdmin="3"==sessionStorage.account_type},mounted:function(){var e=this;sessionStorage.home="1",this.$bus.$on("webSocketConnecteType",function(t){"closeReconnection"===t&&setTimeout(function(){e.closeReconnectionToPlay()},600)}),this.$bus.$on("pub_dp_act",function(t){if(539===t.body.dp_list[0].id)e.$message.warning(e.$t("scrolledToEnd"));else if(590===t.body.dp_list[0].id){var i=JSON.parse(t.body.dp_list[0].value),n=i.cid,a=i.status,o=e.video_list.find(function(e){return e.sn===n});n&&1===a?(o&&e.$set(e.video_list[o.video_index],"savingVideo",!0),e.closeDialog(),e.isSubmitting=!1):n&&2===a?(o&&e.$set(e.video_list[o.video_index],"savingVideo",!0),e.isSubmitting=!1,e.tip_dialog_title=e.$t("saveVideo"),e.tip_dialog_content=e.$t("saveVideoSuccess"),e.tip_dialog_btn_text=e.$t("ok"),e.tip_dialog_request=null,e.tip_dialog_params={},e.$refs.tipDialog.show("saveVideoSuccess",n)):n&&3===a&&(o&&e.$set(e.video_list[o.video_index],"savingVideo",!0),e.isSubmitting=!1,e.tip_dialog_title=e.$t("warnings"),e.tip_dialog_content=e.$t("novideo"),e.tip_dialog_btn_text=e.$t("ok"),e.tip_dialog_request=null,e.tip_dialog_params={},e.$refs.tipDialog.show("noVideoSave"))}}),this.$bus.$on("pub_dp_push_data",function(t){console.log("pub_dp_push_data");var i=t.headers.caller;if(t.body.length>0&&201===t.body[0].id){var n=JSON.parse(t.body[0].value).net,a=n<=0?e.$t("offline"):e.$t("online");e.group_list.map(function(t){t.sn_list.length>0&&t.sn_list.map(function(t){i===t.sn&&(e.$set(t,"net",n),e.$set(t,"net_status",a))})}),e.video_list.map(function(t,o){i===t.sn&&(e.$set(t,"net",n),e.$set(t,"net_status",a),n<=0?(e.toStopFlvVideo(o),e.video_list[o].remote_stream_loading=!1,e.video_list[o].flvPlayer_flag=!1,e.video_list[o].play_state=3):e.carousel_switch||e.toPlayFlvVideo(o,t.device_index))})}}),this.$bus.$on("pub_dp_get_rsp",function(t){if(console.log("是否正在上传视频"),"number"!=typeof t){var i=t.body;if(i.length>0&&590===i[0].id){var n=JSON.parse(i[0].value),a=e.video_list.find(function(e){return e.sn===n.cid});if(!a)return;1===n.status?(e.$set(e.video_list[a.video_index],"play_state",4),e.$set(e.video_list[a.video_index],"savingVideo",!0)):e.$nextTick(function(){e.toPlayFlvVideo(a.video_index,a.device_index)})}}})},beforeDestroy:function(){for(var e=(this.page_num-1)*this.multi_screen;e<this.page_num*this.multi_screen;e++)this.filter_device_list[e]&&(this.$set(this.filter_device_list[e],"selected_device_flag",!1),this.$set(this.filter_device_list[e],"selected_single_device_flag",!1),this.toStopFlvVideo(e-(this.page_num-1)*this.multi_screen));this.single_selected_device={},this.video_list=[],this.endCarousel(),this.$bus.$off("pub_dp_act"),this.$bus.$off("pub_dp_push_data"),this.$bus.$off("pub_dp_get_rsp"),console.log("beforeDestroy")},methods:{isPropertySupported:function(e){return e in document.body.style},checkWebsocket:function(){var e=this;console.log(this.$websocket);var t=this.$websocket.socket.readyState;console.log(t),0===t?setTimeout(function(){e.checkWebsocket()},300):1===t&&this.getDeviceList()},getDeviceList:function(e){var t=this;this.device_loading=!0,this.group_list=[],(0,r.getDeviceGroupList)().then(function(e){200===e.code&&e.datas.length>0&&e.datas&&e.datas.map(function(e,i){t.group_list.push({group_id:e.group_id,alias:e.name})})}).catch(function(e){t.device_loading=!1}).finally(function(){if(0!==t.group_list.length){console.log(t.group_list),(0,r.getDeviceList)({offset:0,limit:1e3}).then(function(i){if(200===i.code){var n=i.datas.map(function(e){var i=t.group_list.find(function(t){return t.alias===e.group_name});return{sn:e.cid,net:e.net,net_status:e.net<=0?t.$t("offline"):t.$t("online"),version:e.version,alias:e.name,group_name:e.group_name,group_id:i?i.group_id:"-1"}}),r=[].concat((0,s.default)(t.group_list));if(r.forEach(function(e){e.sn_list=n.filter(function(t){return t.group_id===e.group_id}).map(function(e,t){return(0,o.default)({},e,{device_index:t})})}),t.group_list=r,e&&"refresh"===e)console.log("刷新组设备列表"),t.$message.success(t.$t("refreshSuccess")),t.filterDeviceList(t.selected_group,"refresh");else{if(console.log("刚进入页面"),0===t.group_list.length)return;if(sessionStorage.homeSelectedGroup){var l=t.group_list.find(function(e){return e.group_id===sessionStorage.homeSelectedGroup});t.selected_group=l.group_id,t.selected_group_label=l.alias,t.filter_device_list=l.sn_list}else sessionStorage.homeSelectedGroup=t.group_list[0].group_id,t.selected_group=t.group_list[0].group_id,t.selected_group_label=t.group_list[0].alias,t.filter_device_list=t.group_list[0].sn_list;if(t.video_list=[].concat(Array(t.multi_screen).fill({})),t.filter_device_list.length>0)for(var d=0;d<t.multi_screen;d++){t.filter_device_list[d]&&(t.filter_device_list[d].selected_device_flag=!0,t.filter_device_list[d].selected_single_device_flag=!1);var c={channel_name:t.filter_device_list[d]?t.filter_device_list[d].sn:"",video_ref:"remoteView"+d,video_con_ref:"videoContainer"+d,video_index:d,live_stream:!1,muted:!0,volume:0,show_PTZ_status:!1,show_headwrap_operation:!1,remote_stream_loading:!1};if(t.filter_device_list[d]&&t.filter_device_list[d].net<=0&&(c.flv_heartbeat=null,c.play_state=3),(0,a.default)(c,t.filter_device_list[d]),t.$set(t.video_list,d,c),t.filter_device_list[d]&&t.filter_device_list[d].net>0){var u={sn:t.filter_device_list[d].sn,dpids:[590]};t.$websocket.webSocketSend("pub_dp_get",u)}}}t.single_selected_device={},t.device_loading=!1,console.log(t.filter_device_list),console.log("video_list: ",t.video_list)}}).catch(function(e){}).finally(function(){})}else t.device_loading=!1})},groupListVisibleChange:function(e){this.group_dropdown_visible=e},filterDeviceList:function(e,t){var i=this;"refresh"===t?console.log("刷新分组"):console.log("选择分组"),this.device_loading=!0;var n=this.filter_device_list;n.length>0&&n.map(function(e){i.$set(e,"selected_device_flag",!1),i.$set(e,"selected_single_device_flag",!1)});for(var o=(this.page_num-1)*this.multi_screen;o<this.page_num*this.multi_screen;o++)this.filter_device_list[o]&&(this.$set(this.filter_device_list[o],"selected_device_flag",!1),this.$set(this.filter_device_list[o],"selected_single_device_flag",!1),this.toStopFlvVideo(o-(this.page_num-1)*this.multi_screen));this.single_selected_device={},this.video_list=[].concat(Array(this.multi_screen).fill({})),sessionStorage.homeSelectedGroup=e,this.selected_group=e;var s=-1;if(this.group_list.map(function(t,n){t.group_id===e&&(i.selected_group_label=t.alias,s=n)}),this.filter_device_list=this.group_list[s].sn_list,this.flvPlayer=[],this.page_num=1,this.checkCarouselSwitch(),this.filter_device_list.length>0)for(var r=0;r<this.multi_screen;r++){this.filter_device_list[r]&&(this.filter_device_list[r].selected_device_flag=!0,this.filter_device_list[r].selected_single_device_flag=!1);var l={channel_name:this.filter_device_list[r]?this.filter_device_list[r].sn:"",video_ref:"remoteView"+r,video_con_ref:"videoContainer"+r,video_index:r,live_stream:!1,volume:0,show_PTZ_status:!1,show_headwrap_operation:!1,remote_stream_loading:!1};if(this.filter_device_list[r]&&this.filter_device_list[r].net<=0&&(l.flv_heartbeat=null,l.play_state=3),(0,a.default)(l,this.filter_device_list[r]),this.$set(this.video_list,r,l),this.filter_device_list[r]&&this.filter_device_list[r].net>0){var d={sn:this.filter_device_list[r].sn,dpids:[590]};this.$websocket.webSocketSend("pub_dp_get",d)}}this.device_loading=!1,console.log("video_list: ",this.video_list)},resizeVideoBox:function(){for(var e=document.getElementsByClassName("video-container"),t=document.getElementsByClassName("video-container")[0].offsetWidth,i=0;i<e.length;i++)e[i].style.height=(9*t/16).toFixed(2)+"px"},resizePtzBox:function(){for(var e=document.getElementsByClassName("showPtzBox"),t=document.getElementsByClassName("showPtzBox")[0].offsetWidth,i=0;i<e.length;i++)e[i].style.height=t+"px"},debounce:function(e,t){var i=null,n=null,a=null;function o(){(a=+new Date)-i<t?n=setTimeout(o,t):(e(),n=null),i=a}return function(){i=+new Date,n||(n=setTimeout(o,t))}},changeVideoBox:function(e){var t=this;console.log("多屏切换");for(var i=(this.page_num-1)*this.multi_screen;i<this.page_num*this.multi_screen;i++)this.filter_device_list[i]&&(this.$set(this.filter_device_list[i],"selected_device_flag",!1),this.$set(this.filter_device_list[i],"selected_single_device_flag",!1),this.toStopFlvVideo(i-(this.page_num-1)*this.multi_screen));if(this.multi_screen=e,this.page_num=1,this.video_list=[].concat(Array(this.multi_screen).fill({})),this.single_selected_device={},this.checkCarouselSwitch(),this.filter_device_list.length>0)for(var n=function(e){t.filter_device_list[e]&&(t.filter_device_list[e].selected_device_flag=!0,t.filter_device_list[e].selected_single_device_flag=!1);var i={channel_name:t.filter_device_list[e]?t.filter_device_list[e].sn:"",video_ref:"remoteView"+e,video_con_ref:"videoContainer"+e,video_index:e,live_stream:!1,volume:0,show_PTZ_status:!1,show_headwrap_operation:!1,remote_stream_loading:!1};t.filter_device_list[e]&&t.filter_device_list[e].net<=0&&(i.flv_heartbeat=null,i.play_state=3),(0,a.default)(i,t.filter_device_list[e]),t.$set(t.video_list,e,i),t.filter_device_list[e]&&t.filter_device_list[e].net>0&&t.$nextTick(function(){t.toPlayFlvVideo(e,e)})},o=0;o<this.multi_screen;o++)n(o);setTimeout(function(){t.resizeVideoBox()},10)},direction:function(e,t){var i={act:1,dp_list:[{id:537,time:Date.parse(new Date),value:(0,n.default)({direction:e})}]};this.$websocket.webSocketSend("pub_dp_act",i,t)},stopDirection:function(e){var t={act:1,dp_list:[{id:537,time:Date.parse(new Date),value:(0,n.default)({direction:0})}]};this.$websocket.webSocketSend("pub_dp_act",t,e)},fullScreen:function(){this.isVideoOutBoxFulling=!0,this.fullingSn="";var e=document.querySelector("body");e.requestFullscreen?e.requestFullscreen():element.mozRequestFullScreen?e.mozRequestFullScreen():element.webkitRequestFullscreen?e.webkitRequestFullscreen():element.msRequestFullscreen&&e.msRequestFullscreen()},exitFullscreen:function(){document.exitFullScreen?document.exitFullScreen():document.mozCancelFullScreen?document.mozCancelFullScreen():document.webkitExitFullscreen?document.webkitExitFullscreen():document.msExitFullscreen&&document.msExitFullscreen(),this.isVideoOutBoxFulling=!1},fullScreenChange:function(){var e=this,t=document.fullscreenElement||document.mozFullScreenElement||document.webkitFullscreenElement||document.msFullscreenElement;t&&this.isVideoOutBoxFulling?(this.fulling=!0,this.isShowModeFooterWithClick=!1,this.onClickVideoBoxOutter()):t&&this.fullingSn?this.fulling_single_video=!0:(this.fulling=!1,this.fulling_single_video=!1,this.single_selected_video={},this.isVideoOutBoxFulling=!1),setTimeout(function(){e.resizeVideoBox()},100)},mute:function(e){if(!(e.net<=0)){var t=e.video_ref;this.$refs[t][0].volume=0,e.volume=0}},unmute:function(e){if(!(e.net<=0)){var t=e.video_ref,i=this.$refs[t][0];this.$set(e,"muted",!1),i.volume=.5,e.volume=50}},dragChangeVolume:function(e){if(!(e.net<=0)){var t=e.video_ref;this.$refs[t][0].volume=e.volume/100}},showPTZ:function(e,t){this.$set(e,"show_PTZ_status",t)},toPlayFlvVideo:function(e,t){d.default.getFeatureList().mseLivePlayback&&(this.video_list[e]&&!this.video_list[e].flv_heartbeat&&(this.video_list[e].remote_stream_loading=!0,this.sendFlvHeartbeat(e,t),this.toPlayLoading(e,t)),this.initFlvPlayer(e,t))},sendFlvHeartbeat:function(e,t){var i={act:1,dp_list:[{id:254,time:Date.parse(new Date),value:(0,n.default)({cid:this.filter_device_list[t].sn})}]};this.$websocket.webSocketSend("pub_dp_act",i,this.filter_device_list[t].sn);var a=this;this.video_list[e].flv_heartbeat=setTimeout(function(){a.sendFlvHeartbeat(e,t)},this.flv_heartbeat_time)},toPlayLoading:function(e,t){var i=this,n=this;this.video_list[e].flv_loading_timeout=setTimeout(function(){console.log("10s了"),n.toStopFlvVideo(e),n.video_list[e].remote_stream_loading=!1,n.video_list[e].flvPlayer_flag=!1,n.video_list[e].play_state=2,i.video_list[e].flv_play_fail_reload_timeout=setTimeout(function(){console.log("播放失败15s自动重试"),n.toPlayFlvVideo(e,t)},i.play_fail_reload_time)},this.flv_loading_time)},initFlvPlayer:function(e,t){var i,n=this,a="remoteView"+t,o=this.$refs[a][0],s=Math.floor(e/5);i=this.session_video_url[s].replace(/%CID%/i,this.filter_device_list[t].sn),console.log(i),this.flvPlayer[e]=d.default.createPlayer({type:"mse",isLive:!0,url:i}),this.flvPlayer[e].attachMediaElement(o),this.flvPlayer[e].load(),this.flvPlayer[e].play().then(function(){return n.onPlaySuccess(e,t)}).catch(function(t){return n.onPlayError(e,t)})},onPlaySuccess:function(e,t){console.log("开始播放",e);var i=this.video_list[e].video_ref;this.$refs[i][0].volume=0,this.video_list[e].flv_loading_timeout&&(clearTimeout(this.video_list[e].flv_loading_timeout),this.video_list[e].flv_loading_timeout=null),this.video_list[e].flv_play_fail_reload_timeout&&(clearInterval(this.video_list[e].flv_play_fail_reload_timeout),this.video_list[e].flv_play_fail_reload_timeout=null),this.video_list[e].remote_stream_loading=!1,this.video_list[e].flvPlayer_flag=!0,this.video_list[e].play_state=1,this.$set(this.video_list,e,this.video_list[e]);var n=this;this.video_list[e].flv_play_delay_reload_interval=setInterval(function(){n.reloadSingleVideo(e,t)},this.play_delay_reload_time);var a=this.video_list.find(function(t){return t.video_index===e});this.$set(this.video_list[a.video_index],"savingVideo",!1)},onPlayError:function(e,t){console.error("Player "+e+" failed to start playing:",t),this.video_list[e].remote_stream_loading=!1,this.video_list[e].flvPlayer_flag=!1,this.video_list[e].play_state=2,this.$set(this.video_list,e,this.video_list[e]);var i=this.video_list.find(function(t){return t.video_index===e});this.$set(this.video_list[i.video_index],"savingVideo",!1)},toStopFlvVideo:function(e){this.destroyFlvPlayer(e)},destroyFlvPlayer:function(e){this.video_list[e].flv_heartbeat&&(clearInterval(this.video_list[e].flv_heartbeat),this.video_list[e].flv_heartbeat=null),this.video_list[e].flv_loading_timeout&&(clearTimeout(this.video_list[e].flv_loading_timeout),this.video_list[e].flv_loading_timeout=null),this.video_list[e].flv_play_delay_reload_interval&&(clearInterval(this.video_list[e].flv_play_delay_reload_interval),this.video_list[e].flv_play_delay_reload_interval=null),this.video_list[e].flv_play_fail_reload_timeout&&(clearInterval(this.video_list[e].flv_play_fail_reload_timeout),this.video_list[e].flv_play_fail_reload_timeout=null),this.flvPlayer[e]&&(this.flvPlayer[e].pause(),this.flvPlayer[e].unload(),this.flvPlayer[e].detachMediaElement(),this.flvPlayer[e].destroy(),this.flvPlayer[e]=null)},reboot:function(){var e={cid:this.reboot_device.sn};this.$websocket.webSocketSend("cli_push_reboot",e);var t=this.reboot_device.video_index;this.toStopFlvVideo(t),this.video_list[t].remote_stream_loading=!1,this.video_list[t].flvPlayer_flag=!1,this.video_list[t].play_state=3},changePreviousPage:function(){var e=this,t=Math.ceil(this.filter_device_list.length/this.multi_screen);if(!(t<=1)){for(var i=(this.page_num-1)*this.multi_screen;i<this.page_num*this.multi_screen;i++)this.filter_device_list[i]&&(this.$set(this.filter_device_list[i],"selected_device_flag",!1),this.$set(this.filter_device_list[i],"selected_single_device_flag",!1),this.toStopFlvVideo(i-(this.page_num-1)*this.multi_screen));this.video_list=[].concat(Array(this.multi_screen).fill({})),this.single_selected_device={},1===this.page_num?this.page_num=t:this.page_num-=1,this.checkCarouselSwitch();for(var n=function(t){e.filter_device_list[t]&&(e.filter_device_list[t].selected_device_flag=!0,e.filter_device_list[t].selected_single_device_flag=!1);var i={channel_name:e.filter_device_list[t]?e.filter_device_list[t].sn:"",video_ref:"remoteView"+t,video_con_ref:"videoContainer"+t,video_index:t-(e.page_num-1)*e.multi_screen,live_stream:!1,volume:0,show_PTZ_status:!1,show_headwrap_operation:!1,remote_stream_loading:!1};e.filter_device_list[t]&&e.filter_device_list[t].net<=0&&(i.flv_heartbeat=null,i.play_state=3),(0,a.default)(i,e.filter_device_list[t]),e.$set(e.video_list,t-(e.page_num-1)*e.multi_screen,i),console.log(e.video_list),e.filter_device_list[t]&&e.filter_device_list[t].net>0&&e.$nextTick(function(){e.toPlayFlvVideo(t-(e.page_num-1)*e.multi_screen,t)})},o=(this.page_num-1)*this.multi_screen;o<this.page_num*this.multi_screen;o++)n(o)}},changeNextPage:function(){var e=this,t=Math.ceil(this.filter_device_list.length/this.multi_screen);if(!(t<=1)){for(var i=(this.page_num-1)*this.multi_screen;i<this.page_num*this.multi_screen;i++)this.filter_device_list[i]&&(this.$set(this.filter_device_list[i],"selected_device_flag",!1),this.$set(this.filter_device_list[i],"selected_single_device_flag",!1),this.toStopFlvVideo(i-(this.page_num-1)*this.multi_screen));this.video_list=[].concat(Array(this.multi_screen).fill({})),this.single_selected_device={},this.page_num===t?this.page_num=1:this.page_num+=1,this.checkCarouselSwitch();for(var n=function(t){e.filter_device_list[t]&&(e.filter_device_list[t].selected_device_flag=!0,e.filter_device_list[t].selected_single_device_flag=!1);var i={channel_name:e.filter_device_list[t]?e.filter_device_list[t].sn:"",video_ref:"remoteView"+t,video_con_ref:"videoContainer"+t,video_index:t-(e.page_num-1)*e.multi_screen,live_stream:!1,volume:0,show_PTZ_status:!1,show_headwrap_operation:!1,remote_stream_loading:!1};e.filter_device_list[t]&&e.filter_device_list[t].net<=0&&(i.flv_heartbeat=null,i.play_state=3),(0,a.default)(i,e.filter_device_list[t]),e.$set(e.video_list,t-(e.page_num-1)*e.multi_screen,i),e.filter_device_list[t]&&e.filter_device_list[t].net>0&&e.$nextTick(function(){e.toPlayFlvVideo(t-(e.page_num-1)*e.multi_screen,t)})},o=(this.page_num-1)*this.multi_screen;o<this.page_num*this.multi_screen;o++)n(o);console.log(this.video_list)}},checkCarouselSwitch:function(){console.log("checkCarouselSwitch"),this.carousel_switch?this.startCarousel():this.endCarousel()},startCarousel:function(){if(this.carousel_interval&&(clearInterval(this.carousel_interval),this.carousel_interval=null),!(Math.ceil(this.filter_device_list.length/this.multi_screen)<=1)){var e=this;this.carousel_interval=setInterval(function(){e.changeNextPage()},this.carousel_time)}},endCarousel:function(){this.carousel_interval&&(clearInterval(this.carousel_interval),this.carousel_interval=null),this.carousel_switch=!1},showDialog:function(e,t){if(this.dialog_type=e,"refresh"===e)this.dialog_title=this.$t("refreshList"),this.dialog_content=this.$t("refreshListTipCon"),this.dialog_tip=this.$t("refreshListTipCon1");else if("reboot"===e){if(t.net<=0)return void this.$message.error(this.$t("deviceOffline"));this.dialog_title=this.$t("rebootDevice"),this.dialog_content=this.$t("rebootDeviceTipCon"),this.dialog_tip="",this.reboot_device=t}this.dialog_visible=!0},closeDialog:function(e){var t=this;if(this.dialog_visible=!1,this.dialogSaveVideo_visible=!1,this.submit_loading=!1,this.isSubmitting=!1,this.reboot_device={},"cancelSaveVideo"===e){var i=this.video_list.find(function(e){return e.sn===t.saveVideoSn});if(i.savingVideo)return;i.net>0&&this.$nextTick(function(){t.toPlayFlvVideo(i.video_index,i.device_index)})}},submitDialog:function(){this.submit_loading=!0,"refresh"===this.dialog_type?(this.getDeviceList("refresh"),this.closeDialog()):"reboot"===this.dialog_type&&(this.reboot(),this.closeDialog())},onClickVideoBoxOutter:function(){var e=this;this.fulling&&(this.isShowModeFooterWithClick=!this.isShowModeFooterWithClick,this.isShowModeFooterWithClick&&(_&&clearTimeout(_),_=setTimeout(function(){e.isShowModeFooterWithClick=!1},3e3)))},onMouseEnterFooterHolder:function(){this.isShowModeFooterWithMouse=!0},ouMouseLiveFooter:function(){this.isShowModeFooterWithMouse=!1,this.isShowModeFooterWithClick=!1,clearTimeout(_)},onHoverPrev:function(e){this.isHoveringPrev=e},onHoverNext:function(e){this.isHoveringNext=e},onMouseEnterVideoItem:function(e){this.$set(e,"show_headwrap_operation",!0)},ouMouseLeaveVideoItem:function(e){this.$set(e,"show_headwrap_operation",!1)},selectSingleDevice:function(e){var t=this;if(this.carousel_switch)this.$message.warning(this.$t("clickSingleDeviceTip"));else{this.single_selected_device.sn&&this.single_selected_device.sn===e.sn||this.single_selected_device.sn&&this.single_selected_device.sn!==e.sn&&(this.filter_device_list.forEach(function(e){t.$set(e,"selected_single_device_flag",!1)}),this.video_list.forEach(function(e){t.$set(e,"selected_single_device_flag",!1)})),this.single_selected_device=e;var i=this.video_list.find(function(t){return t.sn===e.sn});i||this.jumpPage(Math.ceil((e.device_index+1)/this.multi_screen)),this.$nextTick(function(){var n=e.selected_single_device_flag;t.$set(e,"selected_single_device_flag",!n),i||(i=t.video_list.find(function(t){return t.sn===e.sn})),t.$set(i,"selected_single_device_flag",!n)})}},handleSelectedSingleDevice:function(e){e.selected_single_device_flag&&(this.$set(e,"selected_single_device_flag",!1),this.$set(this.filter_device_list[e.device_index],"selected_single_device_flag",!1),this.single_selected_device={})},jumpPage:function(e){for(var t=this,i=(this.page_num-1)*this.multi_screen;i<this.page_num*this.multi_screen;i++)this.filter_device_list[i]&&(this.$set(this.filter_device_list[i],"selected_device_flag",!1),this.$set(this.filter_device_list[i],"selected_single_device_flag",!1),this.toStopFlvVideo(i-(this.page_num-1)*this.multi_screen));this.video_list=[].concat(Array(this.multi_screen).fill({})),this.page_num=e,this.checkCarouselSwitch();for(var n=function(e){t.filter_device_list[e]&&(t.filter_device_list[e].selected_device_flag=!0,t.filter_device_list[e].selected_single_device_flag=!1);var i={channel_name:t.filter_device_list[e]?t.filter_device_list[e].sn:"",video_ref:"remoteView"+e,video_con_ref:"videoContainer"+e,video_index:e-(t.page_num-1)*t.multi_screen,live_stream:!1,volume:0,show_PTZ_status:!1,show_headwrap_operation:!1,remote_stream_loading:!1};t.filter_device_list[e]&&t.filter_device_list[e].net<=0&&(i.flv_heartbeat=null,i.play_state=3),(0,a.default)(i,t.filter_device_list[e]),t.$set(t.video_list,e-(t.page_num-1)*t.multi_screen,i),t.filter_device_list[e]&&t.filter_device_list[e].net>0&&t.$nextTick(function(){t.toPlayFlvVideo(e-(t.page_num-1)*t.multi_screen,e)})},o=(this.page_num-1)*this.multi_screen;o<this.page_num*this.multi_screen;o++)n(o);console.log(this.video_list)},fullSingleScreen:function(e){var t=document.querySelector("body"),i=this.$refs[e.video_con_ref][0];this.single_selected_video=i,this.fullingSn=e.sn,t.requestFullscreen?t.requestFullscreen():t.mozRequestFullScreen?t.mozRequestFullScreen():t.webkitRequestFullscreen?t.webkitRequestFullscreen():t.msRequestFullscreen&&t.msRequestFullscreen()},showSaveVideoPop:function(e){if(e.net<=0)this.$message.error(this.$t("deviceOffline"));else{this.$message.warning(this.$t("Uploading"));var t=e.video_index;this.video_list[t].flv_play_fail_reload_timeout&&(clearInterval(this.video_list[t].flv_play_fail_reload_timeout),this.video_list[t].flv_play_fail_reload_timeout=null),e.flvPlayer_flag&&(this.toStopFlvVideo(t),this.video_list[t].remote_stream_loading=!1,this.video_list[t].flvPlayer_flag=!1,this.video_list[t].play_state=4),this.dialogSaveVideo_visible=!0,this.saveVideoSn=e.sn}},submitSaveVideo:function(){var e=this;if(this.saveVideoBeginTime){var t=new Date(this.saveVideoBeginTime);t.setSeconds(0),t.setMilliseconds(0);var i=new Date(this.saveVideoBeginTime);i.setMinutes(i.getMinutes()+Number(this.selectedTimeRange)),i.setSeconds(0),i.setMilliseconds(0);var n=new Date;if(n.setSeconds(0),n.setMilliseconds(0),console.log(t,i,n),t>=n||i>n)return this.tip_dialog_title=this.$t("warnings"),this.tip_dialog_content=this.$t("novideo"),this.tip_dialog_btn_text=this.$t("ok"),this.tip_dialog_request=null,this.tip_dialog_params={},void this.$refs.tipDialog.show("warnings","1");if(t.getTime()>i.getTime())return this.tip_dialog_title=this.$t("warnings"),this.tip_dialog_content=this.$t("timerangeexceeds"),this.tip_dialog_btn_text=this.$t("ok"),this.tip_dialog_request=null,this.tip_dialog_params={},void this.$refs.tipDialog.show("warnings","1");this.isSubmitting=!0,(0,c.getDeviceVideo)({cid:this.saveVideoSn,enable:!0,begin_time:Math.round(t.valueOf()/1e3),end_time:Math.round(i.valueOf()/1e3)}).then(function(e){e.code}).finally(function(){}).catch(function(){e.isSubmitting=!1})}else this.$message.error(this.$t("pleaseSelect"))},onBeginTimeChange:function(e){},onEndTimeChange:function(e){},closeReconnectionToPlay:function(){for(var e=this,t=(this.page_num-1)*this.multi_screen;t<this.page_num*this.multi_screen;t++)this.filter_device_list[t]&&(this.$set(this.filter_device_list[t],"selected_device_flag",!1),this.$set(this.filter_device_list[t],"selected_single_device_flag",!1),this.toStopFlvVideo(t-(this.page_num-1)*this.multi_screen));this.checkCarouselSwitch();for(var i=function(t){e.filter_device_list[t]&&(e.filter_device_list[t].selected_device_flag=!0,e.filter_device_list[t].selected_single_device_flag=!1),e.filter_device_list[t]&&e.filter_device_list[t].net>0&&e.$nextTick(function(){e.toPlayFlvVideo(t-(e.page_num-1)*e.multi_screen,t)})},n=(this.page_num-1)*this.multi_screen;n<this.page_num*this.multi_screen;n++)i(n);console.log(this.video_list)},reloadSingleVideo:function(e,t){var i=this;this.$set(this.filter_device_list[t],"selected_device_flag",!1),this.$set(this.filter_device_list[t],"selected_single_device_flag",!1),this.toStopFlvVideo(e),this.filter_device_list[t]&&this.filter_device_list[t].net>0&&(this.filter_device_list[t].selected_device_flag=!0,this.filter_device_list[t].selected_single_device_flag=!1,this.$nextTick(function(){i.toPlayFlvVideo(e,t)}))},handleTip:function(e,t){var i=this;if("saveVideoSuccess"===e||"stopSaveVideo"===e){var n=this.video_list.find(function(e){return e.sn===t});n&&this.$set(this.video_list[n.video_index],"savingVideo",!1),n&&n.net>0&&this.$nextTick(function(){i.toPlayFlvVideo(n.video_index,n.device_index)})}},handleTipCancel:function(e,t){this.handleTip(e,t)},showCancelSaveVideoPop:function(e){this.tip_dialog_title=this.$t("tip"),this.tip_dialog_content=this.$t("cancelVideoSaveTip"),this.tip_dialog_btn_text=this.$t("sure"),this.tip_dialog_request=c.getDeviceVideo,this.saveVideoSn=e.sn,this.tip_dialog_params={cid:e.sn,enable:!1},this.$refs.tipDialog.show("stopSaveVideo",e.sn)},handleGroupFocus:function(e){var t=this;if(e){this.$nextTick(function(){var e=t.$refs.groupSelector;e&&(e.visible||e.toggleMenu(),t.$nextTick(function(){var t=e.$refs.input&&e.$refs.input.$refs?e.$refs.input.$refs.input:e.$refs.reference&&e.$refs.reference.$refs?e.$refs.reference.$refs.input:e.$el.querySelector("input");t&&(e.query="PEC",t.value="PEC")}))})}}}}},"UUO+":function(e,t,i){"use strict";i.r(t);var n=i("bRjK"),a=i("FyMl");for(var o in a)"default"!==o&&function(e){i.d(t,e,function(){return a[e]})}(o);i("Zj4A");var s=i("KHd+"),r=Object(s.a)(a.default,n.a,n.b,!1,null,"9019ac9a",null);r.options.__file="src\\views\\errorPage\\401.vue",t.default=r.exports},"Uf/o":function(e,t,i){var n={"./404.svg":"oUrx","./app.svg":"Q7z+","./cloud.svg":"YeFu","./device.svg":"LIc2","./plat.svg":"XYk7","./system.svg":"MKk9","./view.svg":"u5wx"};function a(e){var t=o(e);return i(t)}function o(e){var t=n[e];if(!(t+1)){var i=new Error("Cannot find module '"+e+"'");throw i.code="MODULE_NOT_FOUND",i}return t}a.keys=function(){return Object.keys(n)},a.resolve=o,e.exports=a,a.id="Uf/o"},VR5q:function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=i("X4fA"),a=i("hZMg"),o=function(e){return e&&e.__esModule?e:{default:e}}(i("CMXa"));t.default={name:"SystemSetting",mixins:[o.default],data:function(){var e=this;return{nickName:"",account:"",dialogVisible:!1,dialogTitle:this.$t("modifyPassword"),modifyPasswordDialogForm:{data:{oldPassword:"",newPassword:"",confirmPassword:""},rules:{oldPassword:[{required:!0,message:this.$t("pleaseEnterCorrectPwd18"),trigger:"blur",min:6,max:18}],newPassword:[{required:!0,trigger:"blur",message:this.$t("pleaseEnterCorrectPwd18"),min:6,max:18}],confirmPassword:[{required:!0,validator:function(t,i,n){""===i?n(new Error(e.$t("pleaseEnterCorrectPwd18"))):i!==e.modifyPasswordDialogForm.data.newPassword?n(new Error(e.$t("passwordNotMatching"))):n()},trigger:"blur"}]}},isSubmitting:!1,isSaving:!1}},created:function(){this.handleAutoRelogin(),this.nickName=sessionStorage.account_alias,this.account=sessionStorage.username},methods:{onReloginSuccess:function(e){console.log("systemSetting页面重登成功")},onWebsocketReady:function(){console.log("systemSetting页面websocket准备就绪")},save:function(){var e=this;this.isSaving=!0,(0,a.changeName)({name:this.nickName}).then(function(t){200===t.code&&(sessionStorage.account_alias=e.nickName,e.$message.success(e.$t("editSuccess")),e.$bus.$emit("refreshUserInfo",e.nickName))}).catch(function(e){}).finally(function(){e.isSaving=!1})},modifyPassword:function(){console.log("修改密码"),this.showDialog()},showDialog:function(){this.dialogVisible=!0},hideDialog:function(){var e=this;this.dialogVisible=!1,setTimeout(function(){e.clearFormData()},100)},submit:function(){var e=this;console.log("确认"),this.$refs.modifyPasswordDialogForm.validate(function(t){if(!t)return!1;if(e.modifyPasswordDialogForm.data.newPassword===e.modifyPasswordDialogForm.data.oldPassword)return e.$message.error(e.$t("passwordSame"));e.isSubmitting=!0;var i={password_old:e.$md5(e.modifyPasswordDialogForm.data.oldPassword),password_new:e.$md5(e.modifyPasswordDialogForm.data.newPassword)};(0,a.changePassword)(i).then(function(t){200===t.code&&(e.hideDialog(),e.$message.success(e.$t("modifySuccess")),e.clearFormData(),e.$store.commit("SET_TOKEN",""),(0,n.removeToken)(),(0,n.removeRole)(),e.$router.push({path:"/login"}))}).catch(function(e){}).finally(function(){e.isSubmitting=!1})})},clearFormData:function(){this.modifyPasswordDialogForm.data={oldPassword:"",newPassword:"",confirmPassword:""}}}}},VeXQ:function(e,t,i){},VhpY:function(e,t,i){"use strict";var n=i("yK6b");i.n(n).a},Vtdi:function(e,t,i){"use strict";var n=_(i("Kw5r")),a=_(i("p46w")),o=_(i("XJYT"));i("D66Q"),i("sg+I");var s=_(i("Pf3K")),r=_(i("oYx3")),l=_(i("Q2AE")),d=_(i("mSNy"));i("mF2h"),i("Ij2q");var c=_(i("gjeX")),u=_(i("sutq")),h=_(i("65Ft"));function _(e){return e&&e.__esModule?e:{default:e}}n.default.prototype.$md5=c.default,n.default.use(o.default,{size:a.default.get("size")||"medium",i18n:function(e,t){return d.default.t(e,t)}}),n.default.prototype.base_url="/api/web/",n.default.config.productionTip=!1,document.title=d.default.t("mgtPlat"),n.default.prototype.$websocket=u.default,n.default.prototype.$tabManager=h.default,new n.default({el:"#app",router:r.default,store:l.default,i18n:d.default,render:function(e){return e(s.default)}})},WBkj:function(e,t,i){},WoFz:function(e,t,i){"use strict";var n=i("odDC");i.n(n).a},X4fA:function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getToken=function(){return sessionStorage.token},t.setToken=function(e){return sessionStorage.token=e},t.removeToken=function(){return sessionStorage.clear("token")},t.removeRole=function(){return n.default.remove(a)},t.getRole=function(){return n.default.get(a)},t.setRole=function(e){return n.default.set(a,e)},t.getLanguage=function(){return n.default.get("language")};var n=function(e){return e&&e.__esModule?e:{default:e}}(i("p46w"));var a="bsAdmin-Role"},X8xi:function(e,t,i){e.exports=i.p+"static/img/home_logo.10fb3fe.png"},XAuw:function(e,t,i){"use strict";var n=i("y4PM");i.n(n).a},XLtf:function(e,t,i){e.exports=i.p+"static/img/ptzClick.a89077a.png"},XYk7:function(e,t,i){"use strict";i.r(t);var n=i("4BeY"),a=i.n(n),o=i("IaFt"),s=i.n(o),r=new a.a({id:"icon-plat",use:"icon-plat-usage",viewBox:"0 0 36 36",content:'<symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 36 36" id="icon-plat"><defs><style>#icon-plat .a{fill:#fff;stroke:#707070;opacity:0;}#icon-plat .b{fill:#8a9097;stroke:#8a9097;stroke-width:0.6px;}#icon-plat .c{stroke:none;}#icon-plat .d{fill:none;}</style></defs><g transform="translate(-25 -541)"><g class="a" transform="translate(25 541)"><rect class="c" width="36" height="36" /><rect class="d" x="0.5" y="0.5" width="35" height="35" /></g><path class="b" d="M78.531,111.312a.469.469,0,0,1,0,.938H66.969a.469.469,0,0,1,0-.937ZM80.25,96a1.25,1.25,0,0,1,1.25,1.25v10.313a1.25,1.25,0,0,1-1.25,1.25h-15a1.25,1.25,0,0,1-1.25-1.25V97.25A1.25,1.25,0,0,1,65.25,96Zm0,.938h-15a.312.312,0,0,0-.312.312v10.313a.312.312,0,0,0,.313.313h15a.312.312,0,0,0,.313-.312V97.25A.312.312,0,0,0,80.25,96.938Zm-7.5,1.25a2.5,2.5,0,0,1,1.635,4.391,4.22,4.22,0,0,1,2.584,3.89.469.469,0,1,1-.937,0,3.281,3.281,0,0,0-6.563,0,.469.469,0,1,1-.937,0,4.22,4.22,0,0,1,2.584-3.89,2.5,2.5,0,0,1,1.635-4.391Zm0,.938a1.563,1.563,0,1,0,1.562,1.563A1.563,1.563,0,0,0,72.75,99.125Z" transform="translate(-29.5 455)" /></g></symbol>'});s.a.add(r);t.default=r},Y5bG:function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.scrollTo=function(e,t,i){var a=document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop,o=e-a,s=0;t=void 0===t?500:t,function e(){s+=20;var r=Math.easeInOutQuad(s,a,o,t);!function(e){document.documentElement.scrollTop=e,document.body.parentNode.scrollTop=e,document.body.scrollTop=e}(r),s<t?n(e):i&&"function"==typeof i&&i()}()},Math.easeInOutQuad=function(e,t,i,n){return(e/=n/2)<1?i/2*e*e+t:-i/2*(--e*(e-2)-1)+t};var n=window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(e){window.setTimeout(e,1e3/60)}},YHMm:function(e,t,i){e.exports=i.p+"static/img/fileUploading.7025a60.png"},"YV6/":function(e,t,i){"use strict";(function(e){var n,a,o,s=S(i("rfXi")),r=S(i("gDS+")),l=S(i("GQeE")),d=S(i("Yz+Y")),c=S(i("4d7F")),u=S(i("+Ej1")),h=S(i("OyzN")),_=S(i("nLf9")),f=S(i("P2sY")),p=S(i("s3Ml")),m=S(i("AyUB")),g=S(i("81Jv")),v=S(i("+JPL")),y=S(i("SEkw")),b=S(i("EJiy"));function S(e){return e&&e.__esModule?e:{default:e}}!function(i,s){"object"==(0,b.default)(t)&&"object"==(0,b.default)(e)?e.exports=s():(a=[],void 0===(o="function"==typeof(n=s)?n.apply(t,a):n)||(e.exports=o))}(window,function(){return function(e){var t={};function i(n){if(t[n])return t[n].exports;var a=t[n]={i:n,l:!1,exports:{}};return e[n].call(a.exports,a,a.exports,i),a.l=!0,a.exports}return i.m=e,i.c=t,i.d=function(e,t,n){i.o(e,t)||(0,y.default)(e,t,{enumerable:!0,get:n})},i.r=function(e){void 0!==v.default&&g.default&&(0,y.default)(e,g.default,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},i.t=function(e,t){if(1&t&&(e=i(e)),8&t)return e;if(4&t&&"object"==(void 0===e?"undefined":(0,b.default)(e))&&e&&e.__esModule)return e;var n=(0,m.default)(null);if(i.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var a in e)i.d(n,a,function(t){return e[t]}.bind(null,a));return n},i.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return i.d(t,"a",t),t},i.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},i.p="",i(i.s=14)}([function(e,t,i){var n=i(6),a=i.n(n),o=function(){function e(){}return e.e=function(t,i){t&&!e.FORCE_GLOBAL_TAG||(t=e.GLOBAL_TAG);var n="["+t+"] > "+i;e.ENABLE_CALLBACK&&e.emitter.emit("log","error",n),e.ENABLE_ERROR&&(console.error?console.error(n):console.warn?console.warn(n):console.log(n))},e.i=function(t,i){t&&!e.FORCE_GLOBAL_TAG||(t=e.GLOBAL_TAG);var n="["+t+"] > "+i;e.ENABLE_CALLBACK&&e.emitter.emit("log","info",n),e.ENABLE_INFO&&(console.info?console.info(n):console.log(n))},e.w=function(t,i){t&&!e.FORCE_GLOBAL_TAG||(t=e.GLOBAL_TAG);var n="["+t+"] > "+i;e.ENABLE_CALLBACK&&e.emitter.emit("log","warn",n),e.ENABLE_WARN&&(console.warn?console.warn(n):console.log(n))},e.d=function(t,i){t&&!e.FORCE_GLOBAL_TAG||(t=e.GLOBAL_TAG);var n="["+t+"] > "+i;e.ENABLE_CALLBACK&&e.emitter.emit("log","debug",n),e.ENABLE_DEBUG&&(console.debug?console.debug(n):console.log(n))},e.v=function(t,i){t&&!e.FORCE_GLOBAL_TAG||(t=e.GLOBAL_TAG);var n="["+t+"] > "+i;e.ENABLE_CALLBACK&&e.emitter.emit("log","verbose",n),e.ENABLE_VERBOSE&&console.log(n)},e}();o.GLOBAL_TAG="mpegts.js",o.FORCE_GLOBAL_TAG=!1,o.ENABLE_ERROR=!0,o.ENABLE_INFO=!0,o.ENABLE_WARN=!0,o.ENABLE_DEBUG=!0,o.ENABLE_VERBOSE=!0,o.ENABLE_CALLBACK=!1,o.emitter=new a.a,t.a=o},function(e,t,i){t.a={IO_ERROR:"io_error",DEMUX_ERROR:"demux_error",INIT_SEGMENT:"init_segment",MEDIA_SEGMENT:"media_segment",LOADING_COMPLETE:"loading_complete",RECOVERED_EARLY_EOF:"recovered_early_eof",MEDIA_INFO:"media_info",METADATA_ARRIVED:"metadata_arrived",SCRIPTDATA_ARRIVED:"scriptdata_arrived",TIMED_ID3_METADATA_ARRIVED:"timed_id3_metadata_arrived",SMPTE2038_METADATA_ARRIVED:"smpte2038_metadata_arrived",SCTE35_METADATA_ARRIVED:"scte35_metadata_arrived",PES_PRIVATE_DATA_DESCRIPTOR:"pes_private_data_descriptor",PES_PRIVATE_DATA_ARRIVED:"pes_private_data_arrived",STATISTICS_INFO:"statistics_info",RECOMMEND_SEEKPOINT:"recommend_seekpoint"}},function(e,t,i){i.d(t,"c",function(){return a}),i.d(t,"b",function(){return o}),i.d(t,"a",function(){return s});var n=i(3),a={kIdle:0,kConnecting:1,kBuffering:2,kError:3,kComplete:4},o={OK:"OK",EXCEPTION:"Exception",HTTP_STATUS_CODE_INVALID:"HttpStatusCodeInvalid",CONNECTING_TIMEOUT:"ConnectingTimeout",EARLY_EOF:"EarlyEof",UNRECOVERABLE_EARLY_EOF:"UnrecoverableEarlyEof"},s=function(){function e(e){this._type=e||"undefined",this._status=a.kIdle,this._needStash=!1,this._onContentLengthKnown=null,this._onURLRedirect=null,this._onDataArrival=null,this._onError=null,this._onComplete=null}return e.prototype.destroy=function(){this._status=a.kIdle,this._onContentLengthKnown=null,this._onURLRedirect=null,this._onDataArrival=null,this._onError=null,this._onComplete=null},e.prototype.isWorking=function(){return this._status===a.kConnecting||this._status===a.kBuffering},Object.defineProperty(e.prototype,"type",{get:function(){return this._type},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"status",{get:function(){return this._status},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"needStashBuffer",{get:function(){return this._needStash},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"onContentLengthKnown",{get:function(){return this._onContentLengthKnown},set:function(e){this._onContentLengthKnown=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"onURLRedirect",{get:function(){return this._onURLRedirect},set:function(e){this._onURLRedirect=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"onDataArrival",{get:function(){return this._onDataArrival},set:function(e){this._onDataArrival=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"onError",{get:function(){return this._onError},set:function(e){this._onError=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"onComplete",{get:function(){return this._onComplete},set:function(e){this._onComplete=e},enumerable:!1,configurable:!0}),e.prototype.open=function(e,t){throw new n.c("Unimplemented abstract function!")},e.prototype.abort=function(){throw new n.c("Unimplemented abstract function!")},e}()},function(e,t,i){i.d(t,"d",function(){return o}),i.d(t,"a",function(){return s}),i.d(t,"b",function(){return r}),i.d(t,"c",function(){return l});var n,a=(n=function(e,t){return(n=p.default||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var i in t)t.hasOwnProperty(i)&&(e[i]=t[i])})(e,t)},function(e,t){function i(){this.constructor=e}n(e,t),e.prototype=null===t?(0,m.default)(t):(i.prototype=t.prototype,new i)}),o=function(){function e(e){this._message=e}return Object.defineProperty(e.prototype,"name",{get:function(){return"RuntimeException"},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"message",{get:function(){return this._message},enumerable:!1,configurable:!0}),e.prototype.toString=function(){return this.name+": "+this.message},e}(),s=function(e){function t(t){return e.call(this,t)||this}return a(t,e),Object.defineProperty(t.prototype,"name",{get:function(){return"IllegalStateException"},enumerable:!1,configurable:!0}),t}(o),r=function(e){function t(t){return e.call(this,t)||this}return a(t,e),Object.defineProperty(t.prototype,"name",{get:function(){return"InvalidArgumentException"},enumerable:!1,configurable:!0}),t}(o),l=function(e){function t(t){return e.call(this,t)||this}return a(t,e),Object.defineProperty(t.prototype,"name",{get:function(){return"NotImplementedException"},enumerable:!1,configurable:!0}),t}(o)},function(e,t,i){var n={};!function(){var e=self.navigator.userAgent.toLowerCase(),t=/(edge)\/([\w.]+)/.exec(e)||/(opr)[\/]([\w.]+)/.exec(e)||/(chrome)[ \/]([\w.]+)/.exec(e)||/(iemobile)[\/]([\w.]+)/.exec(e)||/(version)(applewebkit)[ \/]([\w.]+).*(safari)[ \/]([\w.]+)/.exec(e)||/(webkit)[ \/]([\w.]+).*(version)[ \/]([\w.]+).*(safari)[ \/]([\w.]+)/.exec(e)||/(webkit)[ \/]([\w.]+)/.exec(e)||/(opera)(?:.*version|)[ \/]([\w.]+)/.exec(e)||/(msie) ([\w.]+)/.exec(e)||e.indexOf("trident")>=0&&/(rv)(?::| )([\w.]+)/.exec(e)||e.indexOf("compatible")<0&&/(firefox)[ \/]([\w.]+)/.exec(e)||[],i=/(ipad)/.exec(e)||/(ipod)/.exec(e)||/(windows phone)/.exec(e)||/(iphone)/.exec(e)||/(kindle)/.exec(e)||/(android)/.exec(e)||/(windows)/.exec(e)||/(mac)/.exec(e)||/(linux)/.exec(e)||/(cros)/.exec(e)||[],a={browser:t[5]||t[3]||t[1]||"",version:t[2]||t[4]||"0",majorVersion:t[4]||t[2]||"0",platform:i[0]||""},o={};if(a.browser){o[a.browser]=!0;var s=a.majorVersion.split(".");o.version={major:parseInt(a.majorVersion,10),string:a.version},s.length>1&&(o.version.minor=parseInt(s[1],10)),s.length>2&&(o.version.build=parseInt(s[2],10))}for(var r in a.platform&&(o[a.platform]=!0),(o.chrome||o.opr||o.safari)&&(o.webkit=!0),(o.rv||o.iemobile)&&(o.rv&&delete o.rv,a.browser="msie",o.msie=!0),o.edge&&(delete o.edge,a.browser="msedge",o.msedge=!0),o.opr&&(a.browser="opera",o.opera=!0),o.safari&&o.android&&(a.browser="android",o.android=!0),o.name=a.browser,o.platform=a.platform,n)n.hasOwnProperty(r)&&delete n[r];(0,f.default)(n,o)}(),t.a=n},function(e,t,i){t.a={OK:"OK",FORMAT_ERROR:"FormatError",FORMAT_UNSUPPORTED:"FormatUnsupported",CODEC_UNSUPPORTED:"CodecUnsupported"}},function(e,t,i){var n,a="object"==("undefined"==typeof Reflect?"undefined":(0,b.default)(Reflect))?Reflect:null,o=a&&"function"==typeof a.apply?a.apply:function(e,t,i){return Function.prototype.apply.call(e,t,i)};n=a&&"function"==typeof a.ownKeys?a.ownKeys:_.default?function(e){return(0,h.default)(e).concat((0,_.default)(e))}:function(e){return(0,h.default)(e)};var s=u.default||function(e){return e!=e};function r(){r.init.call(this)}e.exports=r,e.exports.once=function(e,t){return new c.default(function(i,n){function a(i){e.removeListener(t,o),n(i)}function o(){"function"==typeof e.removeListener&&e.removeListener("error",a),i([].slice.call(arguments))}E(e,t,o,{once:!0}),"error"!==t&&function(e,t,i){"function"==typeof e.on&&E(e,"error",t,{once:!0})}(e,a)})},r.EventEmitter=r,r.prototype._events=void 0,r.prototype._eventsCount=0,r.prototype._maxListeners=void 0;var f=10;function p(e){if("function"!=typeof e)throw new TypeError('The "listener" argument must be of type Function. Received type '+(void 0===e?"undefined":(0,b.default)(e)))}function g(e){return void 0===e._maxListeners?r.defaultMaxListeners:e._maxListeners}function v(e,t,i,n){var a,o,s,r;if(p(i),void 0===(o=e._events)?(o=e._events=(0,m.default)(null),e._eventsCount=0):(void 0!==o.newListener&&(e.emit("newListener",t,i.listener?i.listener:i),o=e._events),s=o[t]),void 0===s)s=o[t]=i,++e._eventsCount;else if("function"==typeof s?s=o[t]=n?[i,s]:[s,i]:n?s.unshift(i):s.push(i),(a=g(e))>0&&s.length>a&&!s.warned){s.warned=!0;var l=new Error("Possible EventEmitter memory leak detected. "+s.length+" "+String(t)+" listeners added. Use emitter.setMaxListeners() to increase limit");l.name="MaxListenersExceededWarning",l.emitter=e,l.type=t,l.count=s.length,r=l,console&&console.warn&&console.warn(r)}return e}function y(e,t,i){var n={fired:!1,wrapFn:void 0,target:e,type:t,listener:i},a=function(){if(!this.fired)return this.target.removeListener(this.type,this.wrapFn),this.fired=!0,0===arguments.length?this.listener.call(this.target):this.listener.apply(this.target,arguments)}.bind(n);return a.listener=i,n.wrapFn=a,a}function S(e,t,i){var n=e._events;if(void 0===n)return[];var a=n[t];return void 0===a?[]:"function"==typeof a?i?[a.listener||a]:[a]:i?function(e){for(var t=new Array(e.length),i=0;i<t.length;++i)t[i]=e[i].listener||e[i];return t}(a):C(a,a.length)}function w(e){var t=this._events;if(void 0!==t){var i=t[e];if("function"==typeof i)return 1;if(void 0!==i)return i.length}return 0}function C(e,t){for(var i=new Array(t),n=0;n<t;++n)i[n]=e[n];return i}function E(e,t,i,n){if("function"==typeof e.on)n.once?e.once(t,i):e.on(t,i);else{if("function"!=typeof e.addEventListener)throw new TypeError('The "emitter" argument must be of type EventEmitter. Received type '+(void 0===e?"undefined":(0,b.default)(e)));e.addEventListener(t,function a(o){n.once&&e.removeEventListener(t,a),i(o)})}}Object.defineProperty(r,"defaultMaxListeners",{enumerable:!0,get:function(){return f},set:function(e){if("number"!=typeof e||e<0||s(e))throw new RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+e+".");f=e}}),r.init=function(){void 0!==this._events&&this._events!==(0,d.default)(this)._events||(this._events=(0,m.default)(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0},r.prototype.setMaxListeners=function(e){if("number"!=typeof e||e<0||s(e))throw new RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+e+".");return this._maxListeners=e,this},r.prototype.getMaxListeners=function(){return g(this)},r.prototype.emit=function(e){for(var t=[],i=1;i<arguments.length;i++)t.push(arguments[i]);var n="error"===e,a=this._events;if(void 0!==a)n=n&&void 0===a.error;else if(!n)return!1;if(n){var s;if(t.length>0&&(s=t[0]),s instanceof Error)throw s;var r=new Error("Unhandled error."+(s?" ("+s.message+")":""));throw r.context=s,r}var l=a[e];if(void 0===l)return!1;if("function"==typeof l)o(l,this,t);else{var d=l.length,c=C(l,d);for(i=0;i<d;++i)o(c[i],this,t)}return!0},r.prototype.addListener=function(e,t){return v(this,e,t,!1)},r.prototype.on=r.prototype.addListener,r.prototype.prependListener=function(e,t){return v(this,e,t,!0)},r.prototype.once=function(e,t){return p(t),this.on(e,y(this,e,t)),this},r.prototype.prependOnceListener=function(e,t){return p(t),this.prependListener(e,y(this,e,t)),this},r.prototype.removeListener=function(e,t){var i,n,a,o,s;if(p(t),void 0===(n=this._events))return this;if(void 0===(i=n[e]))return this;if(i===t||i.listener===t)0==--this._eventsCount?this._events=(0,m.default)(null):(delete n[e],n.removeListener&&this.emit("removeListener",e,i.listener||t));else if("function"!=typeof i){for(a=-1,o=i.length-1;o>=0;o--)if(i[o]===t||i[o].listener===t){s=i[o].listener,a=o;break}if(a<0)return this;0===a?i.shift():function(e,t){for(;t+1<e.length;t++)e[t]=e[t+1];e.pop()}(i,a),1===i.length&&(n[e]=i[0]),void 0!==n.removeListener&&this.emit("removeListener",e,s||t)}return this},r.prototype.off=r.prototype.removeListener,r.prototype.removeAllListeners=function(e){var t,i,n;if(void 0===(i=this._events))return this;if(void 0===i.removeListener)return 0===arguments.length?(this._events=(0,m.default)(null),this._eventsCount=0):void 0!==i[e]&&(0==--this._eventsCount?this._events=(0,m.default)(null):delete i[e]),this;if(0===arguments.length){var a,o=(0,l.default)(i);for(n=0;n<o.length;++n)"removeListener"!==(a=o[n])&&this.removeAllListeners(a);return this.removeAllListeners("removeListener"),this._events=(0,m.default)(null),this._eventsCount=0,this}if("function"==typeof(t=i[e]))this.removeListener(e,t);else if(void 0!==t)for(n=t.length-1;n>=0;n--)this.removeListener(e,t[n]);return this},r.prototype.listeners=function(e){return S(this,e,!0)},r.prototype.rawListeners=function(e){return S(this,e,!1)},r.listenerCount=function(e,t){return"function"==typeof e.listenerCount?e.listenerCount(t):w.call(e,t)},r.prototype.listenerCount=w,r.prototype.eventNames=function(){return this._eventsCount>0?n(this._events):[]}},function(e,t,i){i.d(t,"d",function(){return n}),i.d(t,"b",function(){return a}),i.d(t,"a",function(){return o}),i.d(t,"c",function(){return s});var n=function(e,t,i,n,a){this.dts=e,this.pts=t,this.duration=i,this.originalDts=n,this.isSyncPoint=a,this.fileposition=null},a=function(){function e(){this.beginDts=0,this.endDts=0,this.beginPts=0,this.endPts=0,this.originalBeginDts=0,this.originalEndDts=0,this.syncPoints=[],this.firstSample=null,this.lastSample=null}return e.prototype.appendSyncPoint=function(e){e.isSyncPoint=!0,this.syncPoints.push(e)},e}(),o=function(){function e(){this._list=[]}return e.prototype.clear=function(){this._list=[]},e.prototype.appendArray=function(e){var t=this._list;0!==e.length&&(t.length>0&&e[0].originalDts<t[t.length-1].originalDts&&this.clear(),Array.prototype.push.apply(t,e))},e.prototype.getLastSyncPointBeforeDts=function(e){if(0==this._list.length)return null;var t=this._list,i=0,n=t.length-1,a=0,o=0,s=n;for(e<t[0].dts&&(i=0,o=s+1);o<=s;){if((a=o+Math.floor((s-o)/2))===n||e>=t[a].dts&&e<t[a+1].dts){i=a;break}t[a].dts<e?o=a+1:s=a-1}return this._list[i]},e}(),s=function(){function e(e){this._type=e,this._list=[],this._lastAppendLocation=-1}return Object.defineProperty(e.prototype,"type",{get:function(){return this._type},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"length",{get:function(){return this._list.length},enumerable:!1,configurable:!0}),e.prototype.isEmpty=function(){return 0===this._list.length},e.prototype.clear=function(){this._list=[],this._lastAppendLocation=-1},e.prototype._searchNearestSegmentBefore=function(e){var t=this._list;if(0===t.length)return-2;var i=t.length-1,n=0,a=0,o=i,s=0;if(e<t[0].originalBeginDts)return-1;for(;a<=o;){if((n=a+Math.floor((o-a)/2))===i||e>t[n].lastSample.originalDts&&e<t[n+1].originalBeginDts){s=n;break}t[n].originalBeginDts<e?a=n+1:o=n-1}return s},e.prototype._searchNearestSegmentAfter=function(e){return this._searchNearestSegmentBefore(e)+1},e.prototype.append=function(e){var t=this._list,i=e,n=this._lastAppendLocation,a=0;-1!==n&&n<t.length&&i.originalBeginDts>=t[n].lastSample.originalDts&&(n===t.length-1||n<t.length-1&&i.originalBeginDts<t[n+1].originalBeginDts)?a=n+1:t.length>0&&(a=this._searchNearestSegmentBefore(i.originalBeginDts)+1),this._lastAppendLocation=a,this._list.splice(a,0,i)},e.prototype.getLastSegmentBefore=function(e){var t=this._searchNearestSegmentBefore(e);return t>=0?this._list[t]:null},e.prototype.getLastSampleBefore=function(e){var t=this.getLastSegmentBefore(e);return null!=t?t.lastSample:null},e.prototype.getLastSyncPointBefore=function(e){for(var t=this._searchNearestSegmentBefore(e),i=this._list[t].syncPoints;0===i.length&&t>0;)t--,i=this._list[t].syncPoints;return i.length>0?i[i.length-1]:null},e}()},function(e,t,i){var n=function(){function e(){this.mimeType=null,this.duration=null,this.hasAudio=null,this.hasVideo=null,this.audioCodec=null,this.videoCodec=null,this.audioDataRate=null,this.videoDataRate=null,this.audioSampleRate=null,this.audioChannelCount=null,this.width=null,this.height=null,this.fps=null,this.profile=null,this.level=null,this.refFrames=null,this.chromaFormat=null,this.sarNum=null,this.sarDen=null,this.metadata=null,this.segments=null,this.segmentCount=null,this.hasKeyframesIndex=null,this.keyframesIndex=null}return e.prototype.isComplete=function(){var e=!1===this.hasAudio||!0===this.hasAudio&&null!=this.audioCodec&&null!=this.audioSampleRate&&null!=this.audioChannelCount,t=!1===this.hasVideo||!0===this.hasVideo&&null!=this.videoCodec&&null!=this.width&&null!=this.height&&null!=this.fps&&null!=this.profile&&null!=this.level&&null!=this.refFrames&&null!=this.chromaFormat&&null!=this.sarNum&&null!=this.sarDen;return null!=this.mimeType&&e&&t},e.prototype.isSeekable=function(){return!0===this.hasKeyframesIndex},e.prototype.getNearestKeyframe=function(e){if(null==this.keyframesIndex)return null;var t=this.keyframesIndex,i=this._search(t.times,e);return{index:i,milliseconds:t.times[i],fileposition:t.filepositions[i]}},e.prototype._search=function(e,t){var i=0,n=e.length-1,a=0,o=0,s=n;for(t<e[0]&&(i=0,o=s+1);o<=s;){if((a=o+Math.floor((s-o)/2))===n||t>=e[a]&&t<e[a+1]){i=a;break}e[a]<t?o=a+1:s=a-1}return i},e}();t.a=n},function(e,t,i){var n=i(6),a=i.n(n),o=i(0),s=function(){function e(){}return Object.defineProperty(e,"forceGlobalTag",{get:function(){return o.a.FORCE_GLOBAL_TAG},set:function(t){o.a.FORCE_GLOBAL_TAG=t,e._notifyChange()},enumerable:!1,configurable:!0}),Object.defineProperty(e,"globalTag",{get:function(){return o.a.GLOBAL_TAG},set:function(t){o.a.GLOBAL_TAG=t,e._notifyChange()},enumerable:!1,configurable:!0}),Object.defineProperty(e,"enableAll",{get:function(){return o.a.ENABLE_VERBOSE&&o.a.ENABLE_DEBUG&&o.a.ENABLE_INFO&&o.a.ENABLE_WARN&&o.a.ENABLE_ERROR},set:function(t){o.a.ENABLE_VERBOSE=t,o.a.ENABLE_DEBUG=t,o.a.ENABLE_INFO=t,o.a.ENABLE_WARN=t,o.a.ENABLE_ERROR=t,e._notifyChange()},enumerable:!1,configurable:!0}),Object.defineProperty(e,"enableDebug",{get:function(){return o.a.ENABLE_DEBUG},set:function(t){o.a.ENABLE_DEBUG=t,e._notifyChange()},enumerable:!1,configurable:!0}),Object.defineProperty(e,"enableVerbose",{get:function(){return o.a.ENABLE_VERBOSE},set:function(t){o.a.ENABLE_VERBOSE=t,e._notifyChange()},enumerable:!1,configurable:!0}),Object.defineProperty(e,"enableInfo",{get:function(){return o.a.ENABLE_INFO},set:function(t){o.a.ENABLE_INFO=t,e._notifyChange()},enumerable:!1,configurable:!0}),Object.defineProperty(e,"enableWarn",{get:function(){return o.a.ENABLE_WARN},set:function(t){o.a.ENABLE_WARN=t,e._notifyChange()},enumerable:!1,configurable:!0}),Object.defineProperty(e,"enableError",{get:function(){return o.a.ENABLE_ERROR},set:function(t){o.a.ENABLE_ERROR=t,e._notifyChange()},enumerable:!1,configurable:!0}),e.getConfig=function(){return{globalTag:o.a.GLOBAL_TAG,forceGlobalTag:o.a.FORCE_GLOBAL_TAG,enableVerbose:o.a.ENABLE_VERBOSE,enableDebug:o.a.ENABLE_DEBUG,enableInfo:o.a.ENABLE_INFO,enableWarn:o.a.ENABLE_WARN,enableError:o.a.ENABLE_ERROR,enableCallback:o.a.ENABLE_CALLBACK}},e.applyConfig=function(e){o.a.GLOBAL_TAG=e.globalTag,o.a.FORCE_GLOBAL_TAG=e.forceGlobalTag,o.a.ENABLE_VERBOSE=e.enableVerbose,o.a.ENABLE_DEBUG=e.enableDebug,o.a.ENABLE_INFO=e.enableInfo,o.a.ENABLE_WARN=e.enableWarn,o.a.ENABLE_ERROR=e.enableError,o.a.ENABLE_CALLBACK=e.enableCallback},e._notifyChange=function(){var t=e.emitter;if(t.listenerCount("change")>0){var i=e.getConfig();t.emit("change",i)}},e.registerListener=function(t){e.emitter.addListener("change",t)},e.removeListener=function(t){e.emitter.removeListener("change",t)},e.addLogListener=function(t){o.a.emitter.addListener("log",t),o.a.emitter.listenerCount("log")>0&&(o.a.ENABLE_CALLBACK=!0,e._notifyChange())},e.removeLogListener=function(t){o.a.emitter.removeListener("log",t),0===o.a.emitter.listenerCount("log")&&(o.a.ENABLE_CALLBACK=!1,e._notifyChange())},e}();s.emitter=new a.a,t.a=s},function(e,t,i){var n=i(6),a=i.n(n),o=i(0),d=i(4),u=i(8);function h(e,t,i){var n=e;if(t+i<n.length){for(;i--;)if(128!=(192&n[++t]))return!1;return!0}return!1}var _,g=function(e){for(var t=[],i=e,n=0,a=e.length;n<a;)if(i[n]<128)t.push(String.fromCharCode(i[n])),++n;else{if(i[n]<192);else if(i[n]<224){if(h(i,n,1)&&(o=(31&i[n])<<6|63&i[n+1])>=128){t.push(String.fromCharCode(65535&o)),n+=2;continue}}else if(i[n]<240){if(h(i,n,2)&&(o=(15&i[n])<<12|(63&i[n+1])<<6|63&i[n+2])>=2048&&55296!=(63488&o)){t.push(String.fromCharCode(65535&o)),n+=3;continue}}else if(i[n]<248){var o;if(h(i,n,3)&&(o=(7&i[n])<<18|(63&i[n+1])<<12|(63&i[n+2])<<6|63&i[n+3])>65536&&o<1114112){o-=65536,t.push(String.fromCharCode(o>>>10|55296)),t.push(String.fromCharCode(1023&o|56320)),n+=4;continue}}t.push(String.fromCharCode(65533)),++n}return t.join("")},v=i(3),y=(_=new ArrayBuffer(2),new DataView(_).setInt16(0,256,!0),256===new Int16Array(_)[0]),S=function(){function e(){}return e.parseScriptData=function(t,i,n){var a={};try{var s=e.parseValue(t,i,n),r=e.parseValue(t,i+s.size,n-s.size);a[s.data]=r.data}catch(e){o.a.e("AMF",e.toString())}return a},e.parseObject=function(t,i,n){if(n<3)throw new v.a("Data not enough when parse ScriptDataObject");var a=e.parseString(t,i,n),o=e.parseValue(t,i+a.size,n-a.size),s=o.objectEnd;return{data:{name:a.data,value:o.data},size:a.size+o.size,objectEnd:s}},e.parseVariable=function(t,i,n){return e.parseObject(t,i,n)},e.parseString=function(e,t,i){if(i<2)throw new v.a("Data not enough when parse String");var n=new DataView(e,t,i).getUint16(0,!y);return{data:n>0?g(new Uint8Array(e,t+2,n)):"",size:2+n}},e.parseLongString=function(e,t,i){if(i<4)throw new v.a("Data not enough when parse LongString");var n=new DataView(e,t,i).getUint32(0,!y);return{data:n>0?g(new Uint8Array(e,t+4,n)):"",size:4+n}},e.parseDate=function(e,t,i){if(i<10)throw new v.a("Data size invalid when parse Date");var n=new DataView(e,t,i),a=n.getFloat64(0,!y),o=n.getInt16(8,!y);return{data:new Date(a+=60*o*1e3),size:10}},e.parseValue=function(t,i,n){if(n<1)throw new v.a("Data not enough when parse Value");var a,s=new DataView(t,i,n),r=1,l=s.getUint8(0),d=!1;try{switch(l){case 0:a=s.getFloat64(1,!y),r+=8;break;case 1:a=!!s.getUint8(1),r+=1;break;case 2:var c=e.parseString(t,i+1,n-1);a=c.data,r+=c.size;break;case 3:a={};var u=0;for(9==(16777215&s.getUint32(n-4,!y))&&(u=3);r<n-4;){var h=e.parseObject(t,i+r,n-r-u);if(h.objectEnd)break;a[h.data.name]=h.data.value,r+=h.size}r<=n-3&&9==(16777215&s.getUint32(r-1,!y))&&(r+=3);break;case 8:for(a={},r+=4,u=0,9==(16777215&s.getUint32(n-4,!y))&&(u=3);r<n-8;){var _=e.parseVariable(t,i+r,n-r-u);if(_.objectEnd)break;a[_.data.name]=_.data.value,r+=_.size}r<=n-3&&9==(16777215&s.getUint32(r-1,!y))&&(r+=3);break;case 9:a=void 0,r=1,d=!0;break;case 10:a=[];var f=s.getUint32(1,!y);r+=4;for(var p=0;p<f;p++){var m=e.parseValue(t,i+r,n-r);a.push(m.data),r+=m.size}break;case 11:var g=e.parseDate(t,i+1,n-1);a=g.data,r+=g.size;break;case 12:var b=e.parseString(t,i+1,n-1);a=b.data,r+=b.size;break;default:r=n,o.a.w("AMF","Unsupported AMF value type "+l)}}catch(e){o.a.e("AMF",e.toString())}return{data:a,size:r,objectEnd:d}},e}(),w=function(){function e(e){this.TAG="ExpGolomb",this._buffer=e,this._buffer_index=0,this._total_bytes=e.byteLength,this._total_bits=8*e.byteLength,this._current_word=0,this._current_word_bits_left=0}return e.prototype.destroy=function(){this._buffer=null},e.prototype._fillCurrentWord=function(){var e=this._total_bytes-this._buffer_index;if(e<=0)throw new v.a("ExpGolomb: _fillCurrentWord() but no bytes available");var t=Math.min(4,e),i=new Uint8Array(4);i.set(this._buffer.subarray(this._buffer_index,this._buffer_index+t)),this._current_word=new DataView(i.buffer).getUint32(0,!1),this._buffer_index+=t,this._current_word_bits_left=8*t},e.prototype.readBits=function(e){if(e>32)throw new v.b("ExpGolomb: readBits() bits exceeded max 32bits!");if(e<=this._current_word_bits_left){var t=this._current_word>>>32-e;return this._current_word<<=e,this._current_word_bits_left-=e,t}var i=this._current_word_bits_left?this._current_word:0;i>>>=32-this._current_word_bits_left;var n=e-this._current_word_bits_left;this._fillCurrentWord();var a=Math.min(n,this._current_word_bits_left),o=this._current_word>>>32-a;return this._current_word<<=a,this._current_word_bits_left-=a,i<<a|o},e.prototype.readBool=function(){return 1===this.readBits(1)},e.prototype.readByte=function(){return this.readBits(8)},e.prototype._skipLeadingZero=function(){var e;for(e=0;e<this._current_word_bits_left;e++)if(0!=(this._current_word&2147483648>>>e))return this._current_word<<=e,this._current_word_bits_left-=e,e;return this._fillCurrentWord(),e+this._skipLeadingZero()},e.prototype.readUEG=function(){var e=this._skipLeadingZero();return this.readBits(e+1)-1},e.prototype.readSEG=function(){var e=this.readUEG();return 1&e?e+1>>>1:-1*(e>>>1)},e}(),C=function(){function e(){}return e._ebsp2rbsp=function(e){for(var t=e,i=t.byteLength,n=new Uint8Array(i),a=0,o=0;o<i;o++)o>=2&&3===t[o]&&0===t[o-1]&&0===t[o-2]||(n[a]=t[o],a++);return new Uint8Array(n.buffer,0,a)},e.parseSPS=function(t){for(var i=t.subarray(1,4),n="avc1.",a=0;a<3;a++){var o=i[a].toString(16);o.length<2&&(o="0"+o),n+=o}var s=e._ebsp2rbsp(t),r=new w(s);r.readByte();var l=r.readByte();r.readByte();var d=r.readByte();r.readUEG();var c=e.getProfileString(l),u=e.getLevelString(d),h=1,_=420,f=8,p=8;if((100===l||110===l||122===l||244===l||44===l||83===l||86===l||118===l||128===l||138===l||144===l)&&(3===(h=r.readUEG())&&r.readBits(1),h<=3&&(_=[0,420,422,444][h]),f=r.readUEG()+8,p=r.readUEG()+8,r.readBits(1),r.readBool()))for(var m=3!==h?8:12,g=0;g<m;g++)r.readBool()&&(g<6?e._skipScalingList(r,16):e._skipScalingList(r,64));r.readUEG();var v=r.readUEG();if(0===v)r.readUEG();else if(1===v){r.readBits(1),r.readSEG(),r.readSEG();var y=r.readUEG();for(g=0;g<y;g++)r.readSEG()}var b=r.readUEG();r.readBits(1);var S=r.readUEG(),C=r.readUEG(),E=r.readBits(1);0===E&&r.readBits(1),r.readBits(1);var k=0,T=0,A=0,D=0;r.readBool()&&(k=r.readUEG(),T=r.readUEG(),A=r.readUEG(),D=r.readUEG());var L=1,R=1,P=0,x=!0,I=0,M=0;if(r.readBool()){if(r.readBool()){var B=r.readByte();B>0&&B<16?(L=[1,12,10,16,40,24,20,32,80,18,15,64,160,4,3,2][B-1],R=[1,11,11,11,33,11,11,11,33,11,11,33,99,3,2,1][B-1]):255===B&&(L=r.readByte()<<8|r.readByte(),R=r.readByte()<<8|r.readByte())}if(r.readBool()&&r.readBool(),r.readBool()&&(r.readBits(4),r.readBool()&&r.readBits(24)),r.readBool()&&(r.readUEG(),r.readUEG()),r.readBool()){var O=r.readBits(32),U=r.readBits(32);x=r.readBool(),P=(I=U)/(M=2*O)}}var V=1;1===L&&1===R||(V=L/R);var $=0,N=0;0===h?($=1,N=2-E):($=3===h?1:2,N=(1===h?2:1)*(2-E));var F=16*(S+1),G=16*(C+1)*(2-E);F-=(k+T)*$,G-=(A+D)*N;var z=Math.ceil(F*V);return r.destroy(),r=null,{codec_mimetype:n,profile_idc:l,level_idc:d,profile_string:c,level_string:u,chroma_format_idc:h,bit_depth:f,bit_depth_luma:f,bit_depth_chroma:p,ref_frames:b,chroma_format:_,chroma_format_string:e.getChromaFormatString(_),frame_rate:{fixed:x,fps:P,fps_den:M,fps_num:I},sar_ratio:{width:L,height:R},codec_size:{width:F,height:G},present_size:{width:z,height:G}}},e._skipScalingList=function(e,t){for(var i=8,n=8,a=0;a<t;a++)0!==n&&(n=(i+e.readSEG()+256)%256),i=0===n?i:n},e.getProfileString=function(e){switch(e){case 66:return"Baseline";case 77:return"Main";case 88:return"Extended";case 100:return"High";case 110:return"High10";case 122:return"High422";case 244:return"High444";default:return"Unknown"}},e.getLevelString=function(e){return(e/10).toFixed(1)},e.getChromaFormatString=function(e){switch(e){case 420:return"4:2:0";case 422:return"4:2:2";case 444:return"4:4:4";default:return"Unknown"}},e}(),E=i(5),k=function(){function e(){}return e._ebsp2rbsp=function(e){for(var t=e,i=t.byteLength,n=new Uint8Array(i),a=0,o=0;o<i;o++)o>=2&&3===t[o]&&0===t[o-1]&&0===t[o-2]||(n[a]=t[o],a++);return new Uint8Array(n.buffer,0,a)},e.parseVPS=function(t){var i=e._ebsp2rbsp(t),n=new w(i);return n.readByte(),n.readByte(),n.readBits(4),n.readBits(2),n.readBits(6),{num_temporal_layers:n.readBits(3)+1,temporal_id_nested:n.readBool()}},e.parseSPS=function(t){var i=e._ebsp2rbsp(t),n=new w(i);n.readByte(),n.readByte();for(var a=0,o=0,s=0,r=0,l=(n.readBits(4),n.readBits(3)),d=(n.readBool(),n.readBits(2)),c=n.readBool(),u=n.readBits(5),h=n.readByte(),_=n.readByte(),f=n.readByte(),p=n.readByte(),m=n.readByte(),g=n.readByte(),v=n.readByte(),y=n.readByte(),b=n.readByte(),S=n.readByte(),C=n.readByte(),E=[],k=[],T=0;T<l;T++)E.push(n.readBool()),k.push(n.readBool());if(l>0)for(T=l;T<8;T++)n.readBits(2);for(T=0;T<l;T++)E[T]&&(n.readByte(),n.readByte(),n.readByte(),n.readByte(),n.readByte(),n.readByte(),n.readByte(),n.readByte(),n.readByte(),n.readByte(),n.readByte()),k[T]&&n.readByte();n.readUEG();var A=n.readUEG();3==A&&n.readBits(1);var D=n.readUEG(),L=n.readUEG();n.readBool()&&(a+=n.readUEG(),o+=n.readUEG(),s+=n.readUEG(),r+=n.readUEG());var R=n.readUEG(),P=n.readUEG(),x=n.readUEG();for(T=n.readBool()?0:l;T<=l;T++)n.readUEG(),n.readUEG(),n.readUEG();if(n.readUEG(),n.readUEG(),n.readUEG(),n.readUEG(),n.readUEG(),n.readUEG(),n.readBool()&&n.readBool())for(var I=0;I<4;I++)for(var M=0;M<(3===I?2:6);M++)if(n.readBool()){var B=Math.min(64,1<<4+(I<<1));for(I>1&&n.readSEG(),T=0;T<B;T++)n.readSEG()}else n.readUEG();n.readBool(),n.readBool(),n.readBool()&&(n.readByte(),n.readUEG(),n.readUEG(),n.readBool());var O=n.readUEG(),U=0;for(T=0;T<O;T++){var V=!1;if(0!==T&&(V=n.readBool()),V){T===O&&n.readUEG(),n.readBool(),n.readUEG();for(var $=0,N=0;N<=U;N++){var F=n.readBool(),G=!1;F||(G=n.readBool()),(F||G)&&$++}U=$}else{var z=n.readUEG(),j=n.readUEG();for(U=z+j,N=0;N<z;N++)n.readUEG(),n.readBool();for(N=0;N<j;N++)n.readUEG(),n.readBool()}}if(n.readBool()){var H=n.readUEG();for(T=0;T<H;T++){for(N=0;N<x+4;N++)n.readBits(1);n.readBits(1)}}var q=0,W=1,K=1,Y=!1,Z=1,Q=1;if(n.readBool(),n.readBool(),n.readBool()){if(n.readBool()){var X=n.readByte();X>0&&X<=16?(W=[1,12,10,16,40,24,20,32,80,18,15,64,160,4,3,2][X-1],K=[1,11,11,11,33,11,11,11,33,11,11,33,99,3,2,1][X-1]):255===X&&(W=n.readBits(16),K=n.readBits(16))}if(n.readBool()&&n.readBool(),n.readBool()&&(n.readBits(3),n.readBool(),n.readBool()&&(n.readByte(),n.readByte(),n.readByte())),n.readBool()&&(n.readUEG(),n.readUEG()),n.readBool(),n.readBool(),n.readBool(),n.readBool()&&(n.readUEG(),n.readUEG(),n.readUEG(),n.readUEG()),n.readBool()&&(Z=n.readBits(32),Q=n.readBits(32),n.readBool()&&(n.readUEG(),n.readBool()))){var J,ee,te=!1;for(J=n.readBool(),ee=n.readBool(),(J||ee)&&((te=n.readBool())&&(n.readByte(),n.readBits(5),n.readBool(),n.readBits(5)),n.readBits(4),n.readBits(4),te&&n.readBits(4),n.readBits(5),n.readBits(5),n.readBits(5)),T=0;T<=l;T++){var ie=n.readBool();Y=ie;var ne=!1,ae=1;ie||(ne=n.readBool());var oe=!1;if(ne?n.readSEG():oe=n.readBool(),oe||(ae=n.readUEG()+1),J)for(N=0;N<ae;N++)n.readUEG(),n.readUEG(),te&&(n.readUEG(),n.readUEG());if(ee)for(N=0;N<ae;N++)n.readUEG(),n.readUEG(),te&&(n.readUEG(),n.readUEG())}}n.readBool()&&(n.readBool(),n.readBool(),n.readBool(),q=n.readUEG(),n.readUEG(),n.readUEG(),n.readUEG(),n.readUEG())}n.readBool();var se="hvc1."+u+".1.L"+C+".B0",re=D-(a+o)*(1===A||2===A?2:1),le=L-(s+r)*(1===A?2:1),de=1;return 1!==W&&1!==K&&(de=W/K),n.destroy(),n=null,{codec_mimetype:se,level_string:e.getLevelString(C),profile_idc:u,bit_depth:R+8,ref_frames:1,chroma_format:A,chroma_format_string:e.getChromaFormatString(A),general_level_idc:C,general_profile_space:d,general_tier_flag:c,general_profile_idc:u,general_profile_compatibility_flags_1:h,general_profile_compatibility_flags_2:_,general_profile_compatibility_flags_3:f,general_profile_compatibility_flags_4:p,general_constraint_indicator_flags_1:m,general_constraint_indicator_flags_2:g,general_constraint_indicator_flags_3:v,general_constraint_indicator_flags_4:y,general_constraint_indicator_flags_5:b,general_constraint_indicator_flags_6:S,min_spatial_segmentation_idc:q,constant_frame_rate:0,chroma_format_idc:A,bit_depth_luma_minus8:R,bit_depth_chroma_minus8:P,frame_rate:{fixed:Y,fps:Q/Z,fps_den:Z,fps_num:Q},sar_ratio:{width:W,height:K},codec_size:{width:re,height:le},present_size:{width:re*de,height:le}}},e.parsePPS=function(t){var i=e._ebsp2rbsp(t),n=new w(i);n.readByte(),n.readByte(),n.readUEG(),n.readUEG(),n.readBool(),n.readBool(),n.readBits(3),n.readBool(),n.readBool(),n.readUEG(),n.readUEG(),n.readSEG(),n.readBool(),n.readBool(),n.readBool()&&n.readUEG(),n.readSEG(),n.readSEG(),n.readBool(),n.readBool(),n.readBool(),n.readBool();var a=n.readBool(),o=n.readBool(),s=1;return o&&a?s=0:o?s=3:a&&(s=2),{parallelismType:s}},e.getChromaFormatString=function(e){switch(e){case 0:return"4:0:0";case 1:return"4:2:0";case 2:return"4:2:2";case 3:return"4:4:4";default:return"Unknown"}},e.getProfileString=function(e){switch(e){case 1:return"Main";case 2:return"Main10";case 3:return"MainSP";case 4:return"Rext";case 9:return"SCC";default:return"Unknown"}},e.getLevelString=function(e){return(e/30).toFixed(1)},e}();function T(e){return e.byteOffset%2==0&&e.byteLength%2==0}function A(e){return e.byteOffset%4==0&&e.byteLength%4==0}function D(e,t){for(var i=0;i<e.length;i++)if(e[i]!==t[i])return!1;return!0}var L,R=function(e,t){return e.byteLength===t.byteLength&&(A(e)&&A(t)?function(e,t){return D(new Uint32Array(e.buffer,e.byteOffset,e.byteLength/4),new Uint32Array(t.buffer,t.byteOffset,t.byteLength/4))}(e,t):T(e)&&T(t)?function(e,t){return D(new Uint16Array(e.buffer,e.byteOffset,e.byteLength/2),new Uint16Array(t.buffer,t.byteOffset,t.byteLength/2))}(e,t):function(e,t){return D(e,t)}(e,t))},P=function(){function e(e,t){this.TAG="FLVDemuxer",this._config=t,this._onError=null,this._onMediaInfo=null,this._onMetaDataArrived=null,this._onScriptDataArrived=null,this._onTrackMetadata=null,this._onDataAvailable=null,this._dataOffset=e.dataOffset,this._firstParse=!0,this._dispatch=!1,this._hasAudio=e.hasAudioTrack,this._hasVideo=e.hasVideoTrack,this._hasAudioFlagOverrided=!1,this._hasVideoFlagOverrided=!1,this._audioInitialMetadataDispatched=!1,this._videoInitialMetadataDispatched=!1,this._mediaInfo=new u.a,this._mediaInfo.hasAudio=this._hasAudio,this._mediaInfo.hasVideo=this._hasVideo,this._metadata=null,this._audioMetadata=null,this._videoMetadata=null,this._naluLengthSize=4,this._timestampBase=0,this._timescale=1e3,this._duration=0,this._durationOverrided=!1,this._referenceFrameRate={fixed:!0,fps:23.976,fps_num:23976,fps_den:1e3},this._flvSoundRateTable=[5500,11025,22050,44100,48e3],this._mpegSamplingRates=[96e3,88200,64e3,48e3,44100,32e3,24e3,22050,16e3,12e3,11025,8e3,7350],this._mpegAudioV10SampleRateTable=[44100,48e3,32e3,0],this._mpegAudioV20SampleRateTable=[22050,24e3,16e3,0],this._mpegAudioV25SampleRateTable=[11025,12e3,8e3,0],this._mpegAudioL1BitRateTable=[0,32,64,96,128,160,192,224,256,288,320,352,384,416,448,-1],this._mpegAudioL2BitRateTable=[0,32,48,56,64,80,96,112,128,160,192,224,256,320,384,-1],this._mpegAudioL3BitRateTable=[0,32,40,48,56,64,80,96,112,128,160,192,224,256,320,-1],this._videoTrack={type:"video",id:1,sequenceNumber:0,samples:[],length:0},this._audioTrack={type:"audio",id:2,sequenceNumber:0,samples:[],length:0},this._littleEndian=function(){var e=new ArrayBuffer(2);return new DataView(e).setInt16(0,256,!0),256===new Int16Array(e)[0]}()}return e.prototype.destroy=function(){this._mediaInfo=null,this._metadata=null,this._audioMetadata=null,this._videoMetadata=null,this._videoTrack=null,this._audioTrack=null,this._onError=null,this._onMediaInfo=null,this._onMetaDataArrived=null,this._onScriptDataArrived=null,this._onTrackMetadata=null,this._onDataAvailable=null},e.probe=function(e){var t=new Uint8Array(e);if(t.byteLength<9)return{needMoreData:!0};var i={match:!1};if(70!==t[0]||76!==t[1]||86!==t[2]||1!==t[3])return i;var n,a=(4&t[4])>>>2!=0,o=0!=(1&t[4]),s=(n=t)[5]<<24|n[6]<<16|n[7]<<8|n[8];return s<9?i:{match:!0,consumed:s,dataOffset:s,hasAudioTrack:a,hasVideoTrack:o}},e.prototype.bindDataSource=function(e){return e.onDataArrival=this.parseChunks.bind(this),this},Object.defineProperty(e.prototype,"onTrackMetadata",{get:function(){return this._onTrackMetadata},set:function(e){this._onTrackMetadata=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"onMediaInfo",{get:function(){return this._onMediaInfo},set:function(e){this._onMediaInfo=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"onMetaDataArrived",{get:function(){return this._onMetaDataArrived},set:function(e){this._onMetaDataArrived=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"onScriptDataArrived",{get:function(){return this._onScriptDataArrived},set:function(e){this._onScriptDataArrived=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"onError",{get:function(){return this._onError},set:function(e){this._onError=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"onDataAvailable",{get:function(){return this._onDataAvailable},set:function(e){this._onDataAvailable=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"timestampBase",{get:function(){return this._timestampBase},set:function(e){this._timestampBase=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"overridedDuration",{get:function(){return this._duration},set:function(e){this._durationOverrided=!0,this._duration=e,this._mediaInfo.duration=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"overridedHasAudio",{set:function(e){this._hasAudioFlagOverrided=!0,this._hasAudio=e,this._mediaInfo.hasAudio=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"overridedHasVideo",{set:function(e){this._hasVideoFlagOverrided=!0,this._hasVideo=e,this._mediaInfo.hasVideo=e},enumerable:!1,configurable:!0}),e.prototype.resetMediaInfo=function(){this._mediaInfo=new u.a},e.prototype._isInitialMetadataDispatched=function(){return this._hasAudio&&this._hasVideo?this._audioInitialMetadataDispatched&&this._videoInitialMetadataDispatched:this._hasAudio&&!this._hasVideo?this._audioInitialMetadataDispatched:!(this._hasAudio||!this._hasVideo)&&this._videoInitialMetadataDispatched},e.prototype.parseChunks=function(t,i){if(!(this._onError&&this._onMediaInfo&&this._onTrackMetadata&&this._onDataAvailable))throw new v.a("Flv: onError & onMediaInfo & onTrackMetadata & onDataAvailable callback must be specified");var n=0,a=this._littleEndian;if(0===i){if(!(t.byteLength>13))return 0;n=e.probe(t).dataOffset}for(this._firstParse&&(this._firstParse=!1,i+n!==this._dataOffset&&o.a.w(this.TAG,"First time parsing but chunk byteStart invalid!"),0!==(s=new DataView(t,n)).getUint32(0,!a)&&o.a.w(this.TAG,"PrevTagSize0 !== 0 !!!"),n+=4);n<t.byteLength;){this._dispatch=!0;var s=new DataView(t,n);if(n+11+4>t.byteLength)break;var r=s.getUint8(0),l=16777215&s.getUint32(0,!a);if(n+11+l+4>t.byteLength)break;if(8===r||9===r||18===r){var d=s.getUint8(4),c=s.getUint8(5),u=s.getUint8(6)|c<<8|d<<16|s.getUint8(7)<<24;0!=(16777215&s.getUint32(7,!a))&&o.a.w(this.TAG,"Meet tag which has StreamID != 0!");var h=n+11;switch(r){case 8:this._parseAudioData(t,h,l,u);break;case 9:this._parseVideoData(t,h,l,u,i+n);break;case 18:this._parseScriptData(t,h,l)}var _=s.getUint32(11+l,!a);_!==11+l&&o.a.w(this.TAG,"Invalid PrevTagSize "+_),n+=11+l+4}else o.a.w(this.TAG,"Unsupported tag type "+r+", skipped"),n+=11+l+4}return this._isInitialMetadataDispatched()&&this._dispatch&&(this._audioTrack.length||this._videoTrack.length)&&this._onDataAvailable(this._audioTrack,this._videoTrack),n},e.prototype._parseScriptData=function(e,t,i){var n=S.parseScriptData(e,t,i);if(n.hasOwnProperty("onMetaData")){if(null==n.onMetaData||"object"!=(0,b.default)(n.onMetaData))return void o.a.w(this.TAG,"Invalid onMetaData structure!");this._metadata&&o.a.w(this.TAG,"Found another onMetaData tag!"),this._metadata=n;var a=this._metadata.onMetaData;if(this._onMetaDataArrived&&this._onMetaDataArrived((0,f.default)({},a)),"boolean"==typeof a.hasAudio&&!1===this._hasAudioFlagOverrided&&(this._hasAudio=a.hasAudio,this._mediaInfo.hasAudio=this._hasAudio),"boolean"==typeof a.hasVideo&&!1===this._hasVideoFlagOverrided&&(this._hasVideo=a.hasVideo,this._mediaInfo.hasVideo=this._hasVideo),"number"==typeof a.audiodatarate&&(this._mediaInfo.audioDataRate=a.audiodatarate),"number"==typeof a.videodatarate&&(this._mediaInfo.videoDataRate=a.videodatarate),"number"==typeof a.width&&(this._mediaInfo.width=a.width),"number"==typeof a.height&&(this._mediaInfo.height=a.height),"number"==typeof a.duration){if(!this._durationOverrided){var s=Math.floor(a.duration*this._timescale);this._duration=s,this._mediaInfo.duration=s}}else this._mediaInfo.duration=0;if("number"==typeof a.framerate){var r=Math.floor(1e3*a.framerate);if(r>0){var d=r/1e3;this._referenceFrameRate.fixed=!0,this._referenceFrameRate.fps=d,this._referenceFrameRate.fps_num=r,this._referenceFrameRate.fps_den=1e3,this._mediaInfo.fps=d}}if("object"==(0,b.default)(a.keyframes)){this._mediaInfo.hasKeyframesIndex=!0;var c=a.keyframes;this._mediaInfo.keyframesIndex=this._parseKeyframesIndex(c),a.keyframes=null}else this._mediaInfo.hasKeyframesIndex=!1;this._dispatch=!1,this._mediaInfo.metadata=a,o.a.v(this.TAG,"Parsed onMetaData"),this._mediaInfo.isComplete()&&this._onMediaInfo(this._mediaInfo)}(0,l.default)(n).length>0&&this._onScriptDataArrived&&this._onScriptDataArrived((0,f.default)({},n))},e.prototype._parseKeyframesIndex=function(e){for(var t=[],i=[],n=1;n<e.times.length;n++){var a=this._timestampBase+Math.floor(1e3*e.times[n]);t.push(a),i.push(e.filepositions[n])}return{times:t,filepositions:i}},e.prototype._parseAudioData=function(e,t,i,n){if(i<=1)o.a.w(this.TAG,"Flv: Invalid audio packet, missing SoundData payload!");else if(!0!==this._hasAudioFlagOverrided||!1!==this._hasAudio){this._littleEndian;var a=new DataView(e,t,i).getUint8(0),s=a>>>4;if(2===s||10===s){var r=0,l=(12&a)>>>2;if(l>=0&&l<=4){r=this._flvSoundRateTable[l];var d=1&a,c=this._audioMetadata,u=this._audioTrack;if(c||(!1===this._hasAudio&&!1===this._hasAudioFlagOverrided&&(this._hasAudio=!0,this._mediaInfo.hasAudio=!0),(c=this._audioMetadata={}).type="audio",c.id=u.id,c.timescale=this._timescale,c.duration=this._duration,c.audioSampleRate=r,c.channelCount=0===d?1:2),10===s){var h=this._parseAACAudioData(e,t+1,i-1);if(null==h)return;if(0===h.packetType){if(c.config){if(R(h.data.config,c.config))return;o.a.w(this.TAG,"AudioSpecificConfig has been changed, re-generate initialization segment")}var _=h.data;c.audioSampleRate=_.samplingRate,c.channelCount=_.channelCount,c.codec=_.codec,c.originalCodec=_.originalCodec,c.config=_.config,c.refSampleDuration=1024/c.audioSampleRate*c.timescale,o.a.v(this.TAG,"Parsed AudioSpecificConfig"),this._isInitialMetadataDispatched()?this._dispatch&&(this._audioTrack.length||this._videoTrack.length)&&this._onDataAvailable(this._audioTrack,this._videoTrack):this._audioInitialMetadataDispatched=!0,this._dispatch=!1,this._onTrackMetadata("audio",c),(m=this._mediaInfo).audioCodec=c.originalCodec,m.audioSampleRate=c.audioSampleRate,m.audioChannelCount=c.channelCount,m.hasVideo?null!=m.videoCodec&&(m.mimeType='video/x-flv; codecs="'+m.videoCodec+","+m.audioCodec+'"'):m.mimeType='video/x-flv; codecs="'+m.audioCodec+'"',m.isComplete()&&this._onMediaInfo(m)}else if(1===h.packetType){var f=this._timestampBase+n,p={unit:h.data,length:h.data.byteLength,dts:f,pts:f};u.samples.push(p),u.length+=h.data.length}else o.a.e(this.TAG,"Flv: Unsupported AAC data type "+h.packetType)}else if(2===s){if(!c.codec){var m;if(null==(_=this._parseMP3AudioData(e,t+1,i-1,!0)))return;c.audioSampleRate=_.samplingRate,c.channelCount=_.channelCount,c.codec=_.codec,c.originalCodec=_.originalCodec,c.refSampleDuration=1152/c.audioSampleRate*c.timescale,o.a.v(this.TAG,"Parsed MPEG Audio Frame Header"),this._audioInitialMetadataDispatched=!0,this._onTrackMetadata("audio",c),(m=this._mediaInfo).audioCodec=c.codec,m.audioSampleRate=c.audioSampleRate,m.audioChannelCount=c.channelCount,m.audioDataRate=_.bitRate,m.hasVideo?null!=m.videoCodec&&(m.mimeType='video/x-flv; codecs="'+m.videoCodec+","+m.audioCodec+'"'):m.mimeType='video/x-flv; codecs="'+m.audioCodec+'"',m.isComplete()&&this._onMediaInfo(m)}var g=this._parseMP3AudioData(e,t+1,i-1,!1);if(null==g)return;f=this._timestampBase+n;var v={unit:g,length:g.byteLength,dts:f,pts:f};u.samples.push(v),u.length+=g.length}}else this._onError(E.a.FORMAT_ERROR,"Flv: Invalid audio sample rate idx: "+l)}else this._onError(E.a.CODEC_UNSUPPORTED,"Flv: Unsupported audio codec idx: "+s)}},e.prototype._parseAACAudioData=function(e,t,i){if(!(i<=1)){var n={},a=new Uint8Array(e,t,i);return n.packetType=a[0],0===a[0]?n.data=this._parseAACAudioSpecificConfig(e,t+1,i-1):n.data=a.subarray(1),n}o.a.w(this.TAG,"Flv: Invalid AAC packet, missing AACPacketType or/and Data!")},e.prototype._parseAACAudioSpecificConfig=function(e,t,i){var n,a,o=new Uint8Array(e,t,i),s=null,r=0,l=null;if(r=n=o[0]>>>3,(a=(7&o[0])<<1|o[1]>>>7)<0||a>=this._mpegSamplingRates.length)this._onError(E.a.FORMAT_ERROR,"Flv: AAC invalid sampling frequency index!");else{var d=this._mpegSamplingRates[a],c=(120&o[1])>>>3;if(!(c<0||c>=8)){5===r&&(l=(7&o[1])<<1|o[2]>>>7,o[2]);var u=self.navigator.userAgent.toLowerCase();return-1!==u.indexOf("firefox")?a>=6?(r=5,s=new Array(4),l=a-3):(r=2,s=new Array(2),l=a):-1!==u.indexOf("android")?(r=2,s=new Array(2),l=a):(r=5,l=a,s=new Array(4),a>=6?l=a-3:1===c&&(r=2,s=new Array(2),l=a)),s[0]=r<<3,s[0]|=(15&a)>>>1,s[1]=(15&a)<<7,s[1]|=(15&c)<<3,5===r&&(s[1]|=(15&l)>>>1,s[2]=(1&l)<<7,s[2]|=8,s[3]=0),{config:s,samplingRate:d,channelCount:c,codec:"mp4a.40."+r,originalCodec:"mp4a.40."+n}}this._onError(E.a.FORMAT_ERROR,"Flv: AAC invalid channel configuration")}},e.prototype._parseMP3AudioData=function(e,t,i,n){if(!(i<4)){this._littleEndian;var a=new Uint8Array(e,t,i),s=null;if(n){if(255!==a[0])return;var r=a[1]>>>3&3,l=(6&a[1])>>1,d=(240&a[2])>>>4,c=(12&a[2])>>>2,u=3!=(a[3]>>>6&3)?2:1,h=0,_=0;switch(r){case 0:h=this._mpegAudioV25SampleRateTable[c];break;case 2:h=this._mpegAudioV20SampleRateTable[c];break;case 3:h=this._mpegAudioV10SampleRateTable[c]}switch(l){case 1:d<this._mpegAudioL3BitRateTable.length&&(_=this._mpegAudioL3BitRateTable[d]);break;case 2:d<this._mpegAudioL2BitRateTable.length&&(_=this._mpegAudioL2BitRateTable[d]);break;case 3:d<this._mpegAudioL1BitRateTable.length&&(_=this._mpegAudioL1BitRateTable[d])}s={bitRate:_,samplingRate:h,channelCount:u,codec:"mp3",originalCodec:"mp3"}}else s=a;return s}o.a.w(this.TAG,"Flv: Invalid MP3 packet, header missing!")},e.prototype._parseVideoData=function(e,t,i,n,a){if(i<=1)o.a.w(this.TAG,"Flv: Invalid video packet, missing VideoData payload!");else if(!0!==this._hasVideoFlagOverrided||!1!==this._hasVideo){var s=new Uint8Array(e,t,i)[0],r=(112&s)>>>4;if(0!=(128&s)){var l=15&s,d=String.fromCharCode.apply(String,new Uint8Array(e,t,i).slice(1,5));if("hvc1"!==d)return void this._onError(E.a.CODEC_UNSUPPORTED,"Flv: Unsupported codec in video frame: "+d);this._parseEnhancedHEVCVideoPacket(e,t+5,i-5,n,a,r,l)}else{var c=15&s;if(7===c)this._parseAVCVideoPacket(e,t+1,i-1,n,a,r);else{if(12!==c)return void this._onError(E.a.CODEC_UNSUPPORTED,"Flv: Unsupported codec in video frame: "+c);this._parseHEVCVideoPacket(e,t+1,i-1,n,a,r)}}}},e.prototype._parseAVCVideoPacket=function(e,t,i,n,a,s){if(i<4)o.a.w(this.TAG,"Flv: Invalid AVC packet, missing AVCPacketType or/and CompositionTime");else{var r=this._littleEndian,l=new DataView(e,t,i),d=l.getUint8(0),c=(16777215&l.getUint32(0,!r))<<8>>8;if(0===d)this._parseAVCDecoderConfigurationRecord(e,t+4,i-4);else if(1===d)this._parseAVCVideoData(e,t+4,i-4,n,a,s,c);else if(2!==d)return void this._onError(E.a.FORMAT_ERROR,"Flv: Invalid video packet type "+d)}},e.prototype._parseHEVCVideoPacket=function(e,t,i,n,a,s){if(i<4)o.a.w(this.TAG,"Flv: Invalid HEVC packet, missing HEVCPacketType or/and CompositionTime");else{var r=this._littleEndian,l=new DataView(e,t,i),d=l.getUint8(0),c=(16777215&l.getUint32(0,!r))<<8>>8;if(0===d)this._parseHEVCDecoderConfigurationRecord(e,t+4,i-4);else if(1===d)this._parseHEVCVideoData(e,t+4,i-4,n,a,s,c);else if(2!==d)return void this._onError(E.a.FORMAT_ERROR,"Flv: Invalid video packet type "+d)}},e.prototype._parseEnhancedHEVCVideoPacket=function(e,t,i,n,a,s,r){if(i<4)o.a.w(this.TAG,"Flv: Invalid HEVC packet, missing HEVCPacketType or/and CompositionTime");else{var l=this._littleEndian,d=new DataView(e,t,i);if(0===r)this._parseHEVCDecoderConfigurationRecord(e,t,i);else if(1===r){var c=(4294967040&d.getUint32(0,!l))>>8;this._parseHEVCVideoData(e,t+3,i-3,n,a,s,c)}else if(3===r)this._parseHEVCVideoData(e,t,i,n,a,s,0);else if(2!==r)return void this._onError(E.a.FORMAT_ERROR,"Flv: Invalid video packet type "+r)}},e.prototype._parseAVCDecoderConfigurationRecord=function(e,t,i){if(i<7)o.a.w(this.TAG,"Flv: Invalid AVCDecoderConfigurationRecord, lack of data!");else{var n=this._videoMetadata,a=this._videoTrack,s=this._littleEndian,r=new DataView(e,t,i);if(n){if(void 0!==n.avcc){var l=new Uint8Array(e,t,i);if(R(l,n.avcc))return;o.a.w(this.TAG,"AVCDecoderConfigurationRecord has been changed, re-generate initialization segment")}}else!1===this._hasVideo&&!1===this._hasVideoFlagOverrided&&(this._hasVideo=!0,this._mediaInfo.hasVideo=!0),(n=this._videoMetadata={}).type="video",n.id=a.id,n.timescale=this._timescale,n.duration=this._duration;var d=r.getUint8(0),c=r.getUint8(1);if(r.getUint8(2),r.getUint8(3),1===d&&0!==c)if(this._naluLengthSize=1+(3&r.getUint8(4)),3===this._naluLengthSize||4===this._naluLengthSize){var u=31&r.getUint8(5);if(0!==u){u>1&&o.a.w(this.TAG,"Flv: Strange AVCDecoderConfigurationRecord: SPS Count = "+u);for(var h=6,_=0;_<u;_++){var f=r.getUint16(h,!s);if(h+=2,0!==f){var p=new Uint8Array(e,t+h,f);h+=f;var m=C.parseSPS(p);if(0===_){n.codecWidth=m.codec_size.width,n.codecHeight=m.codec_size.height,n.presentWidth=m.present_size.width,n.presentHeight=m.present_size.height,n.profile=m.profile_string,n.level=m.level_string,n.bitDepth=m.bit_depth,n.chromaFormat=m.chroma_format,n.sarRatio=m.sar_ratio,n.frameRate=m.frame_rate,!1!==m.frame_rate.fixed&&0!==m.frame_rate.fps_num&&0!==m.frame_rate.fps_den||(n.frameRate=this._referenceFrameRate);var g=n.frameRate.fps_den,v=n.frameRate.fps_num;n.refSampleDuration=n.timescale*(g/v);for(var y=p.subarray(1,4),b="avc1.",S=0;S<3;S++){var w=y[S].toString(16);w.length<2&&(w="0"+w),b+=w}n.codec=b;var k=this._mediaInfo;k.width=n.codecWidth,k.height=n.codecHeight,k.fps=n.frameRate.fps,k.profile=n.profile,k.level=n.level,k.refFrames=m.ref_frames,k.chromaFormat=m.chroma_format_string,k.sarNum=n.sarRatio.width,k.sarDen=n.sarRatio.height,k.videoCodec=b,k.hasAudio?null!=k.audioCodec&&(k.mimeType='video/x-flv; codecs="'+k.videoCodec+","+k.audioCodec+'"'):k.mimeType='video/x-flv; codecs="'+k.videoCodec+'"',k.isComplete()&&this._onMediaInfo(k)}}}var T=r.getUint8(h);if(0!==T){for(T>1&&o.a.w(this.TAG,"Flv: Strange AVCDecoderConfigurationRecord: PPS Count = "+T),h++,_=0;_<T;_++)f=r.getUint16(h,!s),h+=2,0!==f&&(h+=f);n.avcc=new Uint8Array(i),n.avcc.set(new Uint8Array(e,t,i),0),o.a.v(this.TAG,"Parsed AVCDecoderConfigurationRecord"),this._isInitialMetadataDispatched()?this._dispatch&&(this._audioTrack.length||this._videoTrack.length)&&this._onDataAvailable(this._audioTrack,this._videoTrack):this._videoInitialMetadataDispatched=!0,this._dispatch=!1,this._onTrackMetadata("video",n)}else this._onError(E.a.FORMAT_ERROR,"Flv: Invalid AVCDecoderConfigurationRecord: No PPS")}else this._onError(E.a.FORMAT_ERROR,"Flv: Invalid AVCDecoderConfigurationRecord: No SPS")}else this._onError(E.a.FORMAT_ERROR,"Flv: Strange NaluLengthSizeMinusOne: "+(this._naluLengthSize-1));else this._onError(E.a.FORMAT_ERROR,"Flv: Invalid AVCDecoderConfigurationRecord")}},e.prototype._parseHEVCDecoderConfigurationRecord=function(e,t,i){if(i<22)o.a.w(this.TAG,"Flv: Invalid HEVCDecoderConfigurationRecord, lack of data!");else{var n=this._videoMetadata,a=this._videoTrack,s=this._littleEndian,r=new DataView(e,t,i);if(n){if(void 0!==n.hvcc){var l=new Uint8Array(e,t,i);if(R(l,n.hvcc))return;o.a.w(this.TAG,"HEVCDecoderConfigurationRecord has been changed, re-generate initialization segment")}}else!1===this._hasVideo&&!1===this._hasVideoFlagOverrided&&(this._hasVideo=!0,this._mediaInfo.hasVideo=!0),(n=this._videoMetadata={}).type="video",n.id=a.id,n.timescale=this._timescale,n.duration=this._duration;var d=r.getUint8(0),c=31&r.getUint8(1);if(1===d&&0!==c)if(this._naluLengthSize=1+(3&r.getUint8(21)),3===this._naluLengthSize||4===this._naluLengthSize){for(var u=r.getUint8(22),h=0,_=23;h<u;h++){var f=63&r.getUint8(_+0),p=r.getUint16(_+1,!s);_+=3;for(var m=0;m<p;m++){var g=r.getUint16(_+0,!s);if(0===m)if(33===f){_+=2;var v=new Uint8Array(e,t+_,g),y=k.parseSPS(v);n.codecWidth=y.codec_size.width,n.codecHeight=y.codec_size.height,n.presentWidth=y.present_size.width,n.presentHeight=y.present_size.height,n.profile=y.profile_string,n.level=y.level_string,n.bitDepth=y.bit_depth,n.chromaFormat=y.chroma_format,n.sarRatio=y.sar_ratio,n.frameRate=y.frame_rate,!1!==y.frame_rate.fixed&&0!==y.frame_rate.fps_num&&0!==y.frame_rate.fps_den||(n.frameRate=this._referenceFrameRate);var b=n.frameRate.fps_den,S=n.frameRate.fps_num;n.refSampleDuration=n.timescale*(b/S),n.codec=y.codec_mimetype;var w=this._mediaInfo;w.width=n.codecWidth,w.height=n.codecHeight,w.fps=n.frameRate.fps,w.profile=n.profile,w.level=n.level,w.refFrames=y.ref_frames,w.chromaFormat=y.chroma_format_string,w.sarNum=n.sarRatio.width,w.sarDen=n.sarRatio.height,w.videoCodec=y.codec_mimetype,w.hasAudio?null!=w.audioCodec&&(w.mimeType='video/x-flv; codecs="'+w.videoCodec+","+w.audioCodec+'"'):w.mimeType='video/x-flv; codecs="'+w.videoCodec+'"',w.isComplete()&&this._onMediaInfo(w),_+=g}else _+=2+g;else _+=2+g}}n.hvcc=new Uint8Array(i),n.hvcc.set(new Uint8Array(e,t,i),0),o.a.v(this.TAG,"Parsed HEVCDecoderConfigurationRecord"),this._isInitialMetadataDispatched()?this._dispatch&&(this._audioTrack.length||this._videoTrack.length)&&this._onDataAvailable(this._audioTrack,this._videoTrack):this._videoInitialMetadataDispatched=!0,this._dispatch=!1,this._onTrackMetadata("video",n)}else this._onError(E.a.FORMAT_ERROR,"Flv: Strange NaluLengthSizeMinusOne: "+(this._naluLengthSize-1));else this._onError(E.a.FORMAT_ERROR,"Flv: Invalid HEVCDecoderConfigurationRecord")}},e.prototype._parseAVCVideoData=function(e,t,i,n,a,s,r){for(var l=this._littleEndian,d=new DataView(e,t,i),c=[],u=0,h=0,_=this._naluLengthSize,f=this._timestampBase+n,p=1===s;h<i;){if(h+4>=i){o.a.w(this.TAG,"Malformed Nalu near timestamp "+f+", offset = "+h+", dataSize = "+i);break}var m=d.getUint32(h,!l);if(3===_&&(m>>>=8),m>i-_)return void o.a.w(this.TAG,"Malformed Nalus near timestamp "+f+", NaluSize > DataSize!");var g=31&d.getUint8(h+_);5===g&&(p=!0);var v=new Uint8Array(e,t+h,_+m),y={type:g,data:v};c.push(y),u+=v.byteLength,h+=_+m}if(c.length){var b=this._videoTrack,S={units:c,length:u,isKeyframe:p,dts:f,cts:r,pts:f+r};p&&(S.fileposition=a),b.samples.push(S),b.length+=u}},e.prototype._parseHEVCVideoData=function(e,t,i,n,a,s,r){for(var l=this._littleEndian,d=new DataView(e,t,i),c=[],u=0,h=0,_=this._naluLengthSize,f=this._timestampBase+n,p=1===s;h<i;){if(h+4>=i){o.a.w(this.TAG,"Malformed Nalu near timestamp "+f+", offset = "+h+", dataSize = "+i);break}var m=d.getUint32(h,!l);if(3===_&&(m>>>=8),m>i-_)return void o.a.w(this.TAG,"Malformed Nalus near timestamp "+f+", NaluSize > DataSize!");var g=31&d.getUint8(h+_);19!==g&&20!==g||(p=!0);var v=new Uint8Array(e,t+h,_+m),y={type:g,data:v};c.push(y),u+=v.byteLength,h+=_+m}if(c.length){var b=this._videoTrack,S={units:c,length:u,isKeyframe:p,dts:f,cts:r,pts:f+r};p&&(S.fileposition=a),b.samples.push(S),b.length+=u}},e}(),x=function(){function e(){}return e.prototype.destroy=function(){this.onError=null,this.onMediaInfo=null,this.onMetaDataArrived=null,this.onTrackMetadata=null,this.onDataAvailable=null,this.onTimedID3Metadata=null,this.onSMPTE2038Metadata=null,this.onSCTE35Metadata=null,this.onPESPrivateData=null,this.onPESPrivateDataDescriptor=null},e}();!function(e){e[e.kMPEG1Audio=3]="kMPEG1Audio",e[e.kMPEG2Audio=4]="kMPEG2Audio",e[e.kPESPrivateData=6]="kPESPrivateData",e[e.kADTSAAC=15]="kADTSAAC",e[e.kLOASAAC=17]="kLOASAAC",e[e.kAC3=129]="kAC3",e[e.kID3=21]="kID3",e[e.kSCTE35=134]="kSCTE35",e[e.kH264=27]="kH264",e[e.kH265=36]="kH265"}(L||(L={}));var I,M=function(){this.slices=[],this.total_length=0,this.expected_length=0,this.file_position=0};!function(e){e[e.kUnspecified=0]="kUnspecified",e[e.kSliceNonIDR=1]="kSliceNonIDR",e[e.kSliceDPA=2]="kSliceDPA",e[e.kSliceDPB=3]="kSliceDPB",e[e.kSliceDPC=4]="kSliceDPC",e[e.kSliceIDR=5]="kSliceIDR",e[e.kSliceSEI=6]="kSliceSEI",e[e.kSliceSPS=7]="kSliceSPS",e[e.kSlicePPS=8]="kSlicePPS",e[e.kSliceAUD=9]="kSliceAUD",e[e.kEndOfSequence=10]="kEndOfSequence",e[e.kEndOfStream=11]="kEndOfStream",e[e.kFiller=12]="kFiller",e[e.kSPSExt=13]="kSPSExt",e[e.kReserved0=14]="kReserved0"}(I||(I={}));var B,O,U=function(){},V=function(e){var t=e.data.byteLength;this.type=e.type,this.data=new Uint8Array(4+t),new DataView(this.data.buffer).setUint32(0,t),this.data.set(e.data,4)},$=function(){function e(e){this.TAG="H264AnnexBParser",this.current_startcode_offset_=0,this.eof_flag_=!1,this.data_=e,this.current_startcode_offset_=this.findNextStartCodeOffset(0),this.eof_flag_&&o.a.e(this.TAG,"Could not find H264 startcode until payload end!")}return e.prototype.findNextStartCodeOffset=function(e){for(var t=e,i=this.data_;;){if(t+3>=i.byteLength)return this.eof_flag_=!0,i.byteLength;var n=i[t+0]<<24|i[t+1]<<16|i[t+2]<<8|i[t+3],a=i[t+0]<<16|i[t+1]<<8|i[t+2];if(1===n||1===a)return t;t++}},e.prototype.readNextNaluPayload=function(){for(var e=this.data_,t=null;null==t&&!this.eof_flag_;){var i=this.current_startcode_offset_,n=31&e[i+=1==(e[i]<<24|e[i+1]<<16|e[i+2]<<8|e[i+3])?4:3],a=(128&e[i])>>>7,o=this.findNextStartCodeOffset(i);if(this.current_startcode_offset_=o,!(n>=I.kReserved0)&&0===a){var s=e.subarray(i,o);(t=new U).type=n,t.data=s}}return t},e}(),N=function(){function e(e,t,i){var n=8+e.byteLength+1+2+t.byteLength,a=!1;66!==e[3]&&77!==e[3]&&88!==e[3]&&(a=!0,n+=4);var o=this.data=new Uint8Array(n);o[0]=1,o[1]=e[1],o[2]=e[2],o[3]=e[3],o[4]=255,o[5]=225;var s=e.byteLength;o[6]=s>>>8,o[7]=255&s;var r=8;o.set(e,8),o[r+=s]=1;var l=t.byteLength;o[r+1]=l>>>8,o[r+2]=255&l,o.set(t,r+3),r+=3+l,a&&(o[r]=252|i.chroma_format_idc,o[r+1]=248|i.bit_depth_luma-8,o[r+2]=248|i.bit_depth_chroma-8,o[r+3]=0,r+=4)}return e.prototype.getData=function(){return this.data},e}();!function(e){e[e.kNull=0]="kNull",e[e.kAACMain=1]="kAACMain",e[e.kAAC_LC=2]="kAAC_LC",e[e.kAAC_SSR=3]="kAAC_SSR",e[e.kAAC_LTP=4]="kAAC_LTP",e[e.kAAC_SBR=5]="kAAC_SBR",e[e.kAAC_Scalable=6]="kAAC_Scalable",e[e.kLayer1=32]="kLayer1",e[e.kLayer2=33]="kLayer2",e[e.kLayer3=34]="kLayer3"}(B||(B={})),function(e){e[e.k96000Hz=0]="k96000Hz",e[e.k88200Hz=1]="k88200Hz",e[e.k64000Hz=2]="k64000Hz",e[e.k48000Hz=3]="k48000Hz",e[e.k44100Hz=4]="k44100Hz",e[e.k32000Hz=5]="k32000Hz",e[e.k24000Hz=6]="k24000Hz",e[e.k22050Hz=7]="k22050Hz",e[e.k16000Hz=8]="k16000Hz",e[e.k12000Hz=9]="k12000Hz",e[e.k11025Hz=10]="k11025Hz",e[e.k8000Hz=11]="k8000Hz",e[e.k7350Hz=12]="k7350Hz"}(O||(O={}));var F,G,z=[96e3,88200,64e3,48e3,44100,32e3,24e3,22050,16e3,12e3,11025,8e3,7350],j=(F=function(e,t){return(F=p.default||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var i in t)t.hasOwnProperty(i)&&(e[i]=t[i])})(e,t)},function(e,t){function i(){this.constructor=e}F(e,t),e.prototype=null===t?(0,m.default)(t):(i.prototype=t.prototype,new i)}),H=function(){},q=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return j(t,e),t}(H),W=function(){function e(e){this.TAG="AACADTSParser",this.data_=e,this.current_syncword_offset_=this.findNextSyncwordOffset(0),this.eof_flag_&&o.a.e(this.TAG,"Could not found ADTS syncword until payload end")}return e.prototype.findNextSyncwordOffset=function(e){for(var t=e,i=this.data_;;){if(t+7>=i.byteLength)return this.eof_flag_=!0,i.byteLength;if(4095==(i[t+0]<<8|i[t+1])>>>4)return t;t++}},e.prototype.readNextAACFrame=function(){for(var e=this.data_,t=null;null==t&&!this.eof_flag_;){var i=this.current_syncword_offset_,n=(8&e[i+1])>>>3,a=(6&e[i+1])>>>1,o=1&e[i+1],s=(192&e[i+2])>>>6,r=(60&e[i+2])>>>2,l=(1&e[i+2])<<2|(192&e[i+3])>>>6,d=(3&e[i+3])<<11|e[i+4]<<3|(224&e[i+5])>>>5;if(e[i+6],i+d>this.data_.byteLength){this.eof_flag_=!0,this.has_last_incomplete_data=!0;break}var c=1===o?7:9,u=d-c;i+=c;var h=this.findNextSyncwordOffset(i+u);if(this.current_syncword_offset_=h,(0===n||1===n)&&0===a){var _=e.subarray(i,i+u);(t=new H).audio_object_type=s+1,t.sampling_freq_index=r,t.sampling_frequency=z[r],t.channel_config=l,t.data=_}}return t},e.prototype.hasIncompleteData=function(){return this.has_last_incomplete_data},e.prototype.getIncompleteData=function(){return this.has_last_incomplete_data?this.data_.subarray(this.current_syncword_offset_):null},e}(),K=function(){function e(e){this.TAG="AACLOASParser",this.data_=e,this.current_syncword_offset_=this.findNextSyncwordOffset(0),this.eof_flag_&&o.a.e(this.TAG,"Could not found LOAS syncword until payload end")}return e.prototype.findNextSyncwordOffset=function(e){for(var t=e,i=this.data_;;){if(t+1>=i.byteLength)return this.eof_flag_=!0,i.byteLength;if(695==(i[t+0]<<3|i[t+1]>>>5))return t;t++}},e.prototype.getLATMValue=function(e){for(var t=e.readBits(2),i=0,n=0;n<=t;n++)i<<=8,i|=e.readByte();return i},e.prototype.readNextAACFrame=function(e){for(var t=this.data_,i=null;null==i&&!this.eof_flag_;){var n=this.current_syncword_offset_,a=(31&t[n+1])<<8|t[n+2];if(n+3+a>=this.data_.byteLength){this.eof_flag_=!0,this.has_last_incomplete_data=!0;break}var s=new w(t.subarray(n+3,n+3+a)),r=null;if(s.readBool()){if(null==e){o.a.w(this.TAG,"StreamMuxConfig Missing"),this.current_syncword_offset_=this.findNextSyncwordOffset(n+3+a),s.destroy();continue}r=e}else{var l=s.readBool();if(l&&s.readBool()){o.a.e(this.TAG,"audioMuxVersionA is Not Supported"),s.destroy();break}if(l&&this.getLATMValue(s),!s.readBool()){o.a.e(this.TAG,"allStreamsSameTimeFraming zero is Not Supported"),s.destroy();break}if(0!==s.readBits(6)){o.a.e(this.TAG,"more than 2 numSubFrames Not Supported"),s.destroy();break}if(0!==s.readBits(4)){o.a.e(this.TAG,"more than 2 numProgram Not Supported"),s.destroy();break}if(0!==s.readBits(3)){o.a.e(this.TAG,"more than 2 numLayer Not Supported"),s.destroy();break}var d=l?this.getLATMValue(s):0,c=s.readBits(5);d-=5;var u=s.readBits(4);d-=4;var h=s.readBits(4);d-=4,s.readBits(3),(d-=3)>0&&s.readBits(d);var _=s.readBits(3);if(0!==_){o.a.e(this.TAG,"frameLengthType = "+_+". Only frameLengthType = 0 Supported"),s.destroy();break}s.readByte();var f=s.readBool();if(f)if(l)this.getLATMValue(s);else{for(var p=0;;){p<<=8;var m=s.readBool();if(p+=s.readByte(),!m)break}console.log(p)}s.readBool()&&s.readByte(),(r=new q).audio_object_type=c,r.sampling_freq_index=u,r.sampling_frequency=z[r.sampling_freq_index],r.channel_config=h,r.other_data_present=f}for(var g=0;;){var v=s.readByte();if(g+=v,255!==v)break}for(var y=new Uint8Array(g),b=0;b<g;b++)y[b]=s.readByte();(i=new q).audio_object_type=r.audio_object_type,i.sampling_freq_index=r.sampling_freq_index,i.sampling_frequency=z[r.sampling_freq_index],i.channel_config=r.channel_config,i.other_data_present=r.other_data_present,i.data=y,this.current_syncword_offset_=this.findNextSyncwordOffset(n+3+a)}return i},e.prototype.hasIncompleteData=function(){return this.has_last_incomplete_data},e.prototype.getIncompleteData=function(){return this.has_last_incomplete_data?this.data_.subarray(this.current_syncword_offset_):null},e}(),Y=function(){};!function(e){e[e.kSpliceNull=0]="kSpliceNull",e[e.kSpliceSchedule=4]="kSpliceSchedule",e[e.kSpliceInsert=5]="kSpliceInsert",e[e.kTimeSignal=6]="kTimeSignal",e[e.kBandwidthReservation=7]="kBandwidthReservation",e[e.kPrivateCommand=255]="kPrivateCommand"}(G||(G={}));var Z,Q=function(e){var t=e.readBool();return t?(e.readBits(6),{time_specified_flag:t,pts_time:4*e.readBits(31)+e.readBits(2)}):(e.readBits(7),{time_specified_flag:t})},X=function(e){var t=e.readBool();return e.readBits(6),{auto_return:t,duration:4*e.readBits(31)+e.readBits(2)}},J=function(e,t){var i=t.readBits(8);return e?{component_tag:i}:{component_tag:i,splice_time:Q(t)}},ee=function(e){return{component_tag:e.readBits(8),utc_splice_time:e.readBits(32)}},te=function(e){var t=e.readBits(32),i=e.readBool();e.readBits(7);var n={splice_event_id:t,splice_event_cancel_indicator:i};if(i)return n;if(n.out_of_network_indicator=e.readBool(),n.program_splice_flag=e.readBool(),n.duration_flag=e.readBool(),e.readBits(5),n.program_splice_flag)n.utc_splice_time=e.readBits(32);else{n.component_count=e.readBits(8),n.components=[];for(var a=0;a<n.component_count;a++)n.components.push(ee(e))}return n.duration_flag&&(n.break_duration=X(e)),n.unique_program_id=e.readBits(16),n.avail_num=e.readBits(8),n.avails_expected=e.readBits(8),n},ie=function(e,t,i,n){return{descriptor_tag:e,descriptor_length:t,identifier:i,provider_avail_id:n.readBits(32)}},ne=function(e,t,i,n){var a=n.readBits(8),o=n.readBits(3);n.readBits(5);for(var s="",r=0;r<o;r++)s+=String.fromCharCode(n.readBits(8));return{descriptor_tag:e,descriptor_length:t,identifier:i,preroll:a,dtmf_count:o,DTMF_char:s}},ae=function(e){var t=e.readBits(8);return e.readBits(7),{component_tag:t,pts_offset:4*e.readBits(31)+e.readBits(2)}},oe=function(e,t,i,n){var a=n.readBits(32),o=n.readBool();n.readBits(7);var s={descriptor_tag:e,descriptor_length:t,identifier:i,segmentation_event_id:a,segmentation_event_cancel_indicator:o};if(o)return s;if(s.program_segmentation_flag=n.readBool(),s.segmentation_duration_flag=n.readBool(),s.delivery_not_restricted_flag=n.readBool(),s.delivery_not_restricted_flag?n.readBits(5):(s.web_delivery_allowed_flag=n.readBool(),s.no_regional_blackout_flag=n.readBool(),s.archive_allowed_flag=n.readBool(),s.device_restrictions=n.readBits(2)),!s.program_segmentation_flag){s.component_count=n.readBits(8),s.components=[];for(var r=0;r<s.component_count;r++)s.components.push(ae(n))}s.segmentation_duration_flag&&(s.segmentation_duration=n.readBits(40)),s.segmentation_upid_type=n.readBits(8),s.segmentation_upid_length=n.readBits(8);var l=new Uint8Array(s.segmentation_upid_length);for(r=0;r<s.segmentation_upid_length;r++)l[r]=n.readBits(8);return s.segmentation_upid=l.buffer,s.segmentation_type_id=n.readBits(8),s.segment_num=n.readBits(8),s.segments_expected=n.readBits(8),52!==s.segmentation_type_id&&54!==s.segmentation_type_id&&56!==s.segmentation_type_id&&58!==s.segmentation_type_id||(s.sub_segment_num=n.readBits(8),s.sub_segments_expected=n.readBits(8)),s},se=function(e,t,i,n){return{descriptor_tag:e,descriptor_length:t,identifier:i,TAI_seconds:n.readBits(48),TAI_ns:n.readBits(32),UTC_offset:n.readBits(16)}},re=function(e){return{component_tag:e.readBits(8),ISO_code:String.fromCharCode(e.readBits(8),e.readBits(8),e.readBits(8)),Bit_Stream_Mode:e.readBits(3),Num_Channels:e.readBits(4),Full_Srvc_Audio:e.readBool()}},le=function(e,t,i,n){for(var a=n.readBits(4),o=[],s=0;s<a;s++)o.push(re(n));return{descriptor_tag:e,descriptor_length:t,identifier:i,audio_count:a,components:o}};!function(e){e[e.kSliceIDR_W_RADL=19]="kSliceIDR_W_RADL",e[e.kSliceIDR_N_LP=20]="kSliceIDR_N_LP",e[e.kSliceCRA_NUT=21]="kSliceCRA_NUT",e[e.kSliceVPS=32]="kSliceVPS",e[e.kSliceSPS=33]="kSliceSPS",e[e.kSlicePPS=34]="kSlicePPS",e[e.kSliceAUD=35]="kSliceAUD"}(Z||(Z={}));var de=function(){},ce=function(e){var t=e.data.byteLength;this.type=e.type,this.data=new Uint8Array(4+t),new DataView(this.data.buffer).setUint32(0,t),this.data.set(e.data,4)},ue=function(){function e(e){this.TAG="H265AnnexBParser",this.current_startcode_offset_=0,this.eof_flag_=!1,this.data_=e,this.current_startcode_offset_=this.findNextStartCodeOffset(0),this.eof_flag_&&o.a.e(this.TAG,"Could not find H265 startcode until payload end!")}return e.prototype.findNextStartCodeOffset=function(e){for(var t=e,i=this.data_;;){if(t+3>=i.byteLength)return this.eof_flag_=!0,i.byteLength;var n=i[t+0]<<24|i[t+1]<<16|i[t+2]<<8|i[t+3],a=i[t+0]<<16|i[t+1]<<8|i[t+2];if(1===n||1===a)return t;t++}},e.prototype.readNextNaluPayload=function(){for(var e=this.data_,t=null;null==t&&!this.eof_flag_;){var i=this.current_startcode_offset_,n=e[i+=1==(e[i]<<24|e[i+1]<<16|e[i+2]<<8|e[i+3])?4:3]>>1&63,a=(128&e[i])>>>7,o=this.findNextStartCodeOffset(i);if(this.current_startcode_offset_=o,0===a){var s=e.subarray(i,o);(t=new de).type=n,t.data=s}}return t},e}(),he=function(){function e(e,t,i,n){var a=23+(5+e.byteLength)+(5+t.byteLength)+(5+i.byteLength),o=this.data=new Uint8Array(a);o[0]=1,o[1]=(3&n.general_profile_space)<<6|(n.general_tier_flag?1:0)<<5|31&n.general_profile_idc,o[2]=n.general_profile_compatibility_flags_1,o[3]=n.general_profile_compatibility_flags_2,o[4]=n.general_profile_compatibility_flags_3,o[5]=n.general_profile_compatibility_flags_4,o[6]=n.general_constraint_indicator_flags_1,o[7]=n.general_constraint_indicator_flags_2,o[8]=n.general_constraint_indicator_flags_3,o[9]=n.general_constraint_indicator_flags_4,o[10]=n.general_constraint_indicator_flags_5,o[11]=n.general_constraint_indicator_flags_6,o[12]=n.general_level_idc,o[13]=240|(3840&n.min_spatial_segmentation_idc)>>8,o[14]=255&n.min_spatial_segmentation_idc,o[15]=252|3&n.parallelismType,o[16]=252|3&n.chroma_format_idc,o[17]=248|7&n.bit_depth_luma_minus8,o[18]=248|7&n.bit_depth_chroma_minus8,o[19]=0,o[20]=0,o[21]=(3&n.constant_frame_rate)<<6|(7&n.num_temporal_layers)<<3|(n.temporal_id_nested?1:0)<<2|3,o[22]=3,o[23]=128|Z.kSliceVPS,o[24]=0,o[25]=1,o[26]=(65280&e.byteLength)>>8,o[27]=(255&e.byteLength)>>0,o.set(e,28),o[23+(5+e.byteLength)+0]=128|Z.kSliceSPS,o[23+(5+e.byteLength)+1]=0,o[23+(5+e.byteLength)+2]=1,o[23+(5+e.byteLength)+3]=(65280&t.byteLength)>>8,o[23+(5+e.byteLength)+4]=(255&t.byteLength)>>0,o.set(t,23+(5+e.byteLength)+5),o[23+(5+e.byteLength+5+t.byteLength)+0]=128|Z.kSlicePPS,o[23+(5+e.byteLength+5+t.byteLength)+1]=0,o[23+(5+e.byteLength+5+t.byteLength)+2]=1,o[23+(5+e.byteLength+5+t.byteLength)+3]=(65280&i.byteLength)>>8,o[23+(5+e.byteLength+5+t.byteLength)+4]=(255&i.byteLength)>>0,o.set(i,23+(5+e.byteLength+5+t.byteLength)+5)}return e.prototype.getData=function(){return this.data},e}(),_e=function(){},fe=[[64,64,80,80,96,96,112,112,128,128,160,160,192,192,224,224,256,256,320,320,384,384,448,448,512,512,640,640,768,768,896,896,1024,1024,1152,1152,1280,1280],[69,70,87,88,104,105,121,122,139,140,174,175,208,209,243,244,278,279,348,349,417,418,487,488,557,558,696,697,835,836,975,976,1114,1115,1253,1254,1393,1394],[96,96,120,120,144,144,168,168,192,192,240,240,288,288,336,336,384,384,480,480,576,576,672,672,768,768,960,960,1152,1152,1344,1344,1536,1536,1728,1728,1920,1920]],pe=function(){function e(e){this.TAG="AC3Parser",this.data_=e,this.current_syncword_offset_=this.findNextSyncwordOffset(0),this.eof_flag_&&o.a.e(this.TAG,"Could not found AC3 syncword until payload end")}return e.prototype.findNextSyncwordOffset=function(e){for(var t=e,i=this.data_;;){if(t+7>=i.byteLength)return this.eof_flag_=!0,i.byteLength;if(2935==(i[t+0]<<8|i[t+1]<<0))return t;t++}},e.prototype.readNextAC3Frame=function(){for(var e=this.data_,t=null;null==t&&!this.eof_flag_;){var i=this.current_syncword_offset_,n=e[i+4]>>6,a=[48e3,44200,33e3][n],o=63&e[i+4],s=2*fe[n][o];if(i+s>this.data_.byteLength){this.eof_flag_=!0,this.has_last_incomplete_data=!0;break}var r=this.findNextSyncwordOffset(i+s);this.current_syncword_offset_=r;var l=e[i+5]>>3,d=7&e[i+5],c=e[i+6]>>5,u=0;0!=(1&c)&&1!==c&&(u+=2),0!=(4&c)&&(u+=2),2===c&&(u+=2);var h=(e[i+6]<<8|e[i+7]<<0)>>12-u&1,_=[2,1,2,3,3,4,4,5][c]+h;(t=new _e).sampling_frequency=a,t.channel_count=_,t.channel_mode=c,t.bit_stream_identification=l,t.low_frequency_effects_channel_on=h,t.bit_stream_mode=d,t.frame_size_code=o,t.data=e.subarray(i,i+s)}return t},e.prototype.hasIncompleteData=function(){return this.has_last_incomplete_data},e.prototype.getIncompleteData=function(){return this.has_last_incomplete_data?this.data_.subarray(this.current_syncword_offset_):null},e}(),me=function(){var e=function(t,i){return(e=p.default||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var i in t)t.hasOwnProperty(i)&&(e[i]=t[i])})(t,i)};return function(t,i){function n(){this.constructor=t}e(t,i),t.prototype=null===i?(0,m.default)(i):(n.prototype=i.prototype,new n)}}(),ge=function(){return(ge=f.default||function(e){for(var t,i=1,n=arguments.length;i<n;i++)for(var a in t=arguments[i])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e}).apply(this,arguments)},ve=function(e){function t(t,i){var n=e.call(this)||this;return n.TAG="TSDemuxer",n.first_parse_=!0,n.media_info_=new u.a,n.timescale_=90,n.duration_=0,n.current_pmt_pid_=-1,n.program_pmt_map_={},n.pes_slice_queues_={},n.section_slice_queues_={},n.video_metadata_={vps:void 0,sps:void 0,pps:void 0,details:void 0},n.audio_metadata_={codec:void 0,audio_object_type:void 0,sampling_freq_index:void 0,sampling_frequency:void 0,channel_config:void 0},n.aac_last_sample_pts_=void 0,n.aac_last_incomplete_data_=null,n.has_video_=!1,n.has_audio_=!1,n.video_init_segment_dispatched_=!1,n.audio_init_segment_dispatched_=!1,n.video_metadata_changed_=!1,n.audio_metadata_changed_=!1,n.loas_previous_frame=null,n.video_track_={type:"video",id:1,sequenceNumber:0,samples:[],length:0},n.audio_track_={type:"audio",id:2,sequenceNumber:0,samples:[],length:0},n.ts_packet_size_=t.ts_packet_size,n.sync_offset_=t.sync_offset,n.config_=i,n}return me(t,e),t.prototype.destroy=function(){this.media_info_=null,this.pes_slice_queues_=null,this.section_slice_queues_=null,this.video_metadata_=null,this.audio_metadata_=null,this.aac_last_incomplete_data_=null,this.video_track_=null,this.audio_track_=null,e.prototype.destroy.call(this)},t.probe=function(e){var t=new Uint8Array(e),i=-1,n=188;if(t.byteLength<=3*n)return{needMoreData:!0};for(;-1===i;){for(var a=Math.min(1e3,t.byteLength-3*n),s=0;s<a;){if(71===t[s]&&71===t[s+n]&&71===t[s+2*n]){i=s;break}s++}if(-1===i)if(188===n)n=192;else{if(192!==n)break;n=204}}return-1===i?{match:!1}:(192===n&&i>=4?(o.a.v("TSDemuxer","ts_packet_size = 192, m2ts mode"),i-=4):204===n&&o.a.v("TSDemuxer","ts_packet_size = 204, RS encoded MPEG2-TS stream"),{match:!0,consumed:0,ts_packet_size:n,sync_offset:i})},t.prototype.bindDataSource=function(e){return e.onDataArrival=this.parseChunks.bind(this),this},t.prototype.resetMediaInfo=function(){this.media_info_=new u.a},t.prototype.parseChunks=function(e,t){if(!(this.onError&&this.onMediaInfo&&this.onTrackMetadata&&this.onDataAvailable))throw new v.a("onError & onMediaInfo & onTrackMetadata & onDataAvailable callback must be specified");var i=0;for(this.first_parse_&&(this.first_parse_=!1,i=this.sync_offset_);i+this.ts_packet_size_<=e.byteLength;){var n=t+i;192===this.ts_packet_size_&&(i+=4);var a=new Uint8Array(e,i,188),s=a[0];if(71!==s){o.a.e(this.TAG,"sync_byte = "+s+", not 0x47");break}var r=(64&a[1])>>>6,l=(a[1],(31&a[1])<<8|a[2]),d=(48&a[3])>>>4,c=15&a[3],u={},h=4;if(2==d||3==d){var _=a[4];if(5+_===188){i+=188,204===this.ts_packet_size_&&(i+=16);continue}_>0&&(u=this.parseAdaptationField(e,i+4,1+_)),h=5+_}if(1==d||3==d)if(0===l||l===this.current_pmt_pid_||null!=this.pmt_&&this.pmt_.pid_stream_type[l]===L.kSCTE35){var f=188-h;this.handleSectionSlice(e,i+h,f,{pid:l,file_position:n,payload_unit_start_indicator:r,continuity_conunter:c,random_access_indicator:u.random_access_indicator})}else if(null!=this.pmt_&&null!=this.pmt_.pid_stream_type[l]){f=188-h;var p=this.pmt_.pid_stream_type[l];l!==this.pmt_.common_pids.h264&&l!==this.pmt_.common_pids.h265&&l!==this.pmt_.common_pids.adts_aac&&l!==this.pmt_.common_pids.loas_aac&&l!==this.pmt_.common_pids.ac3&&l!==this.pmt_.common_pids.opus&&l!==this.pmt_.common_pids.mp3&&!0!==this.pmt_.pes_private_data_pids[l]&&!0!==this.pmt_.timed_id3_pids[l]||this.handlePESSlice(e,i+h,f,{pid:l,stream_type:p,file_position:n,payload_unit_start_indicator:r,continuity_conunter:c,random_access_indicator:u.random_access_indicator})}i+=188,204===this.ts_packet_size_&&(i+=16)}return this.dispatchAudioVideoMediaSegment(),i},t.prototype.parseAdaptationField=function(e,t,i){var n=new Uint8Array(e,t,i),a=n[0];return a>0?a>183?(o.a.w(this.TAG,"Illegal adaptation_field_length: "+a),{}):{discontinuity_indicator:(128&n[1])>>>7,random_access_indicator:(64&n[1])>>>6,elementary_stream_priority_indicator:(32&n[1])>>>5}:{}},t.prototype.handleSectionSlice=function(e,t,i,n){var a=new Uint8Array(e,t,i),o=this.section_slice_queues_[n.pid];if(n.payload_unit_start_indicator){var s=a[0];if(null!=o&&0!==o.total_length){var r=new Uint8Array(e,t+1,Math.min(i,s));o.slices.push(r),o.total_length+=r.byteLength,o.total_length===o.expected_length?this.emitSectionSlices(o,n):this.clearSlices(o,n)}for(var l=1+s;l<a.byteLength&&255!==a[l+0];){var d=(15&a[l+1])<<8|a[l+2];this.section_slice_queues_[n.pid]=new M,(o=this.section_slice_queues_[n.pid]).expected_length=d+3,o.file_position=n.file_position,o.random_access_indicator=n.random_access_indicator,r=new Uint8Array(e,t+l,Math.min(i-l,o.expected_length-o.total_length)),o.slices.push(r),o.total_length+=r.byteLength,o.total_length===o.expected_length?this.emitSectionSlices(o,n):o.total_length>=o.expected_length&&this.clearSlices(o,n),l+=r.byteLength}}else null!=o&&0!==o.total_length&&(r=new Uint8Array(e,t,Math.min(i,o.expected_length-o.total_length)),o.slices.push(r),o.total_length+=r.byteLength,o.total_length===o.expected_length?this.emitSectionSlices(o,n):o.total_length>=o.expected_length&&this.clearSlices(o,n))},t.prototype.handlePESSlice=function(e,t,i,n){var a=new Uint8Array(e,t,i),s=a[0]<<16|a[1]<<8|a[2],r=(a[3],a[4]<<8|a[5]);if(n.payload_unit_start_indicator){if(1!==s)return void o.a.e(this.TAG,"handlePESSlice: packet_start_code_prefix should be 1 but with value "+s);var l=this.pes_slice_queues_[n.pid];l&&(0===l.expected_length||l.expected_length===l.total_length?this.emitPESSlices(l,n):this.clearSlices(l,n)),this.pes_slice_queues_[n.pid]=new M,this.pes_slice_queues_[n.pid].file_position=n.file_position,this.pes_slice_queues_[n.pid].random_access_indicator=n.random_access_indicator}if(null!=this.pes_slice_queues_[n.pid]){var d=this.pes_slice_queues_[n.pid];d.slices.push(a),n.payload_unit_start_indicator&&(d.expected_length=0===r?0:r+6),d.total_length+=a.byteLength,d.expected_length>0&&d.expected_length===d.total_length?this.emitPESSlices(d,n):d.expected_length>0&&d.expected_length<d.total_length&&this.clearSlices(d,n)}},t.prototype.emitSectionSlices=function(e,t){for(var i=new Uint8Array(e.total_length),n=0,a=0;n<e.slices.length;n++){var o=e.slices[n];i.set(o,a),a+=o.byteLength}e.slices=[],e.expected_length=-1,e.total_length=0;var s=new function(){};s.pid=t.pid,s.data=i,s.file_position=e.file_position,s.random_access_indicator=e.random_access_indicator,this.parseSection(s)},t.prototype.emitPESSlices=function(e,t){for(var i=new Uint8Array(e.total_length),n=0,a=0;n<e.slices.length;n++){var o=e.slices[n];i.set(o,a),a+=o.byteLength}e.slices=[],e.expected_length=-1,e.total_length=0;var s=new function(){};s.pid=t.pid,s.data=i,s.stream_type=t.stream_type,s.file_position=e.file_position,s.random_access_indicator=e.random_access_indicator,this.parsePES(s)},t.prototype.clearSlices=function(e,t){e.slices=[],e.expected_length=-1,e.total_length=0},t.prototype.parseSection=function(e){var t=e.data,i=e.pid;0===i?this.parsePAT(t):i===this.current_pmt_pid_?this.parsePMT(t):null!=this.pmt_&&this.pmt_.scte_35_pids[i]&&this.parseSCTE35(t)},t.prototype.parsePES=function(e){var t=e.data,i=t[0]<<16|t[1]<<8|t[2],n=t[3],a=t[4]<<8|t[5];if(1===i)if(188!==n&&190!==n&&191!==n&&240!==n&&241!==n&&255!==n&&242!==n&&248!==n){t[6];var s=(192&t[7])>>>6,r=t[8],l=void 0,d=void 0;2!==s&&3!==s||(l=536870912*(14&t[9])+4194304*(255&t[10])+16384*(254&t[11])+128*(255&t[12])+(254&t[13])/2,d=3===s?536870912*(14&t[14])+4194304*(255&t[15])+16384*(254&t[16])+128*(255&t[17])+(254&t[18])/2:l);var c=9+r,u=void 0;if(0!==a){if(a<3+r)return void o.a.v(this.TAG,"Malformed PES: PES_packet_length < 3 + PES_header_data_length");u=a-3-r}else u=t.byteLength-c;var h=t.subarray(c,c+u);switch(e.stream_type){case L.kMPEG1Audio:case L.kMPEG2Audio:this.parseMP3Payload(h,l);break;case L.kPESPrivateData:this.pmt_.common_pids.opus===e.pid?this.parseOpusPayload(h,l):this.pmt_.common_pids.ac3===e.pid?this.parseAC3Payload(h,l):this.pmt_.smpte2038_pids[e.pid]?this.parseSMPTE2038MetadataPayload(h,l,d,e.pid,n):this.parsePESPrivateDataPayload(h,l,d,e.pid,n);break;case L.kADTSAAC:this.parseADTSAACPayload(h,l);break;case L.kLOASAAC:this.parseLOASAACPayload(h,l);break;case L.kAC3:this.parseAC3Payload(h,l);break;case L.kID3:this.parseTimedID3MetadataPayload(h,l,d,e.pid,n);break;case L.kH264:this.parseH264Payload(h,l,d,e.file_position,e.random_access_indicator);break;case L.kH265:this.parseH265Payload(h,l,d,e.file_position,e.random_access_indicator)}}else 188!==n&&191!==n&&240!==n&&241!==n&&255!==n&&242!==n&&248!==n||e.stream_type!==L.kPESPrivateData||(c=6,u=void 0,u=0!==a?a:t.byteLength-c,h=t.subarray(c,c+u),this.parsePESPrivateDataPayload(h,void 0,void 0,e.pid,n));else o.a.e(this.TAG,"parsePES: packet_start_code_prefix should be 1 but with value "+i)},t.prototype.parsePAT=function(e){var t=e[0];if(0===t){var i=(15&e[1])<<8|e[2],n=(e[3],e[4],(62&e[5])>>>1),a=1&e[5],s=e[6],l=(e[7],null);if(1===a&&0===s)(l=new function(){this.program_pmt_pid={}}).version_number=n;else if(null==(l=this.pat_))return;for(var d=i-5-4,c=-1,u=-1,h=8;h<8+d;h+=4){var _=e[h]<<8|e[h+1],f=(31&e[h+2])<<8|e[h+3];0===_?l.network_pid=f:(l.program_pmt_pid[_]=f,-1===c&&(c=_),-1===u&&(u=f))}1===a&&0===s&&(null==this.pat_&&o.a.v(this.TAG,"Parsed first PAT: "+(0,r.default)(l)),this.pat_=l,this.current_program_=c,this.current_pmt_pid_=u)}else o.a.e(this.TAG,"parsePAT: table_id "+t+" is not corresponded to PAT!")},t.prototype.parsePMT=function(e){var t=e[0];if(2===t){var i=(15&e[1])<<8|e[2],n=e[3]<<8|e[4],a=(62&e[5])>>>1,l=1&e[5],d=e[6],c=(e[7],null);if(1===l&&0===d)(c=new function(){this.pid_stream_type={},this.common_pids={h264:void 0,h265:void 0,adts_aac:void 0,loas_aac:void 0,opus:void 0,ac3:void 0,mp3:void 0},this.pes_private_data_pids={},this.timed_id3_pids={},this.scte_35_pids={},this.smpte2038_pids={}}).program_number=n,c.version_number=a,this.program_pmt_map_[n]=c;else if(null==(c=this.program_pmt_map_[n]))return;e[8],e[9];for(var u=(15&e[10])<<8|e[11],h=12+u,_=i-9-u-4,f=h;f<h+_;){var p=e[f],m=(31&e[f+1])<<8|e[f+2],g=(15&e[f+3])<<8|e[f+4];c.pid_stream_type[m]=p;var v=c.common_pids.h264||c.common_pids.h265,y=c.common_pids.adts_aac||c.common_pids.loas_aac||c.common_pids.ac3||c.common_pids.opus||c.common_pids.mp3;if(p!==L.kH264||v)if(p!==L.kH265||v)if(p!==L.kADTSAAC||y)if(p!==L.kLOASAAC||y)if(p!==L.kAC3||y)if(p!==L.kMPEG1Audio&&p!==L.kMPEG2Audio||y)if(p===L.kPESPrivateData){if(c.pes_private_data_pids[m]=!0,g>0){for(var b=f+5;b<f+5+g;){var S=e[b+0],w=e[b+1];if(5===S){var C=String.fromCharCode.apply(String,(0,s.default)(e.subarray(b+2,b+2+w)));"VANC"===C?c.smpte2038_pids[m]=!0:"Opus"===C&&(c.common_pids.opus=m)}else if(127===S&&m===c.common_pids.opus){var E=null;if(128===e[b+2]&&(E=e[b+3]),null==E){o.a.e(this.TAG,"Not Supported Opus channel count.");continue}var k={codec:"opus",channel_count:0==(15&E)?2:15&E,channel_config_code:E,sample_rate:48e3},T={codec:"opus",meta:k};0==this.audio_init_segment_dispatched_?(this.audio_metadata_=k,this.dispatchAudioInitSegment(T)):this.detectAudioMetadataChange(T)&&(this.dispatchAudioMediaSegment(),this.dispatchAudioInitSegment(T))}b+=2+w}var A=e.subarray(f+5,f+5+g);this.dispatchPESPrivateDataDescriptor(m,p,A)}}else p===L.kID3?c.timed_id3_pids[m]=!0:p===L.kSCTE35&&(c.scte_35_pids[m]=!0);else c.common_pids.mp3=m;else c.common_pids.ac3=m;else c.common_pids.loas_aac=m;else c.common_pids.adts_aac=m;else c.common_pids.h265=m;else c.common_pids.h264=m;f+=5+g}n===this.current_program_&&(null==this.pmt_&&o.a.v(this.TAG,"Parsed first PMT: "+(0,r.default)(c)),this.pmt_=c,(c.common_pids.h264||c.common_pids.h265)&&(this.has_video_=!0),(c.common_pids.adts_aac||c.common_pids.loas_aac||c.common_pids.ac3||c.common_pids.opus||c.common_pids.mp3)&&(this.has_audio_=!0))}else o.a.e(this.TAG,"parsePMT: table_id "+t+" is not corresponded to PMT!")},t.prototype.parseSCTE35=function(e){var t=function(e){var t=new w(e),i=t.readBits(8),n=t.readBool(),a=t.readBool();t.readBits(2);var o=t.readBits(12),s=t.readBits(8),r=t.readBool(),l=t.readBits(6),d=4*t.readBits(31)+t.readBits(2),c=t.readBits(8),u=t.readBits(12),h=t.readBits(12),_=t.readBits(8),f=null;_===G.kSpliceNull?f={}:_===G.kSpliceSchedule?f=function(e){for(var t=e.readBits(8),i=[],n=0;n<t;n++)i.push(te(e));return{splice_count:t,events:i}}(t):_===G.kSpliceInsert?f=function(e){var t=e.readBits(32),i=e.readBool();e.readBits(7);var n={splice_event_id:t,splice_event_cancel_indicator:i};if(i)return n;if(n.out_of_network_indicator=e.readBool(),n.program_splice_flag=e.readBool(),n.duration_flag=e.readBool(),n.splice_immediate_flag=e.readBool(),e.readBits(4),n.program_splice_flag&&!n.splice_immediate_flag&&(n.splice_time=Q(e)),!n.program_splice_flag){n.component_count=e.readBits(8),n.components=[];for(var a=0;a<n.component_count;a++)n.components.push(J(n.splice_immediate_flag,e))}return n.duration_flag&&(n.break_duration=X(e)),n.unique_program_id=e.readBits(16),n.avail_num=e.readBits(8),n.avails_expected=e.readBits(8),n}(t):_===G.kTimeSignal?f={splice_time:Q(t)}:_===G.kBandwidthReservation?f={}:_===G.kPrivateCommand?f=function(e,t){for(var i=String.fromCharCode(t.readBits(8),t.readBits(8),t.readBits(8),t.readBits(8)),n=new Uint8Array(e-4),a=0;a<e-4;a++)n[a]=t.readBits(8);return{identifier:i,private_data:n.buffer}}(h,t):t.readBits(8*h);for(var p=[],m=t.readBits(16),g=0;g<m;){var v=t.readBits(8),y=t.readBits(8),b=String.fromCharCode(t.readBits(8),t.readBits(8),t.readBits(8),t.readBits(8));0===v?p.push(ie(v,y,b,t)):1===v?p.push(ne(v,y,b,t)):2===v?p.push(oe(v,y,b,t)):3===v?p.push(se(v,y,b,t)):4===v?p.push(le(v,y,b,t)):t.readBits(8*(y-4)),g+=2+y}var S={table_id:i,section_syntax_indicator:n,private_indicator:a,section_length:o,protocol_version:s,encrypted_packet:r,encryption_algorithm:l,pts_adjustment:d,cw_index:c,tier:u,splice_command_length:h,splice_command_type:_,splice_command:f,descriptor_loop_length:m,splice_descriptors:p,E_CRC32:r?t.readBits(32):void 0,CRC32:t.readBits(32)};if(_===G.kSpliceInsert){var C=f;if(C.splice_event_cancel_indicator)return{splice_command_type:_,detail:S,data:e};if(C.program_splice_flag&&!C.splice_immediate_flag){var E=C.duration_flag?C.break_duration.auto_return:void 0,k=C.duration_flag?C.break_duration.duration/90:void 0;return C.splice_time.time_specified_flag?{splice_command_type:_,pts:(d+C.splice_time.pts_time)%Math.pow(2,33),auto_return:E,duraiton:k,detail:S,data:e}:{splice_command_type:_,auto_return:E,duraiton:k,detail:S,data:e}}return{splice_command_type:_,auto_return:E=C.duration_flag?C.break_duration.auto_return:void 0,duraiton:k=C.duration_flag?C.break_duration.duration/90:void 0,detail:S,data:e}}if(_===G.kTimeSignal){var T=f;return T.splice_time.time_specified_flag?{splice_command_type:_,pts:(d+T.splice_time.pts_time)%Math.pow(2,33),detail:S,data:e}:{splice_command_type:_,detail:S,data:e}}return{splice_command_type:_,detail:S,data:e}}(e);if(null!=t.pts){var i=Math.floor(t.pts/this.timescale_);t.pts=i}else t.nearest_pts=this.aac_last_sample_pts_;this.onSCTE35Metadata&&this.onSCTE35Metadata(t)},t.prototype.parseH264Payload=function(e,t,i,n,a){for(var s=new $(e),r=null,l=[],d=0,c=!1;null!=(r=s.readNextNaluPayload());){var u=new V(r);if(u.type===I.kSliceSPS){var h=C.parseSPS(r.data);this.video_init_segment_dispatched_?!0===this.detectVideoMetadataChange(u,h)&&(o.a.v(this.TAG,"H264: Critical h264 metadata has been changed, attempt to re-generate InitSegment"),this.video_metadata_changed_=!0,this.video_metadata_={vps:void 0,sps:u,pps:void 0,details:h}):(this.video_metadata_.sps=u,this.video_metadata_.details=h)}else u.type===I.kSlicePPS?this.video_init_segment_dispatched_&&!this.video_metadata_changed_||(this.video_metadata_.pps=u,this.video_metadata_.sps&&this.video_metadata_.pps&&(this.video_metadata_changed_&&this.dispatchVideoMediaSegment(),this.dispatchVideoInitSegment())):(u.type===I.kSliceIDR||u.type===I.kSliceNonIDR&&1===a)&&(c=!0);this.video_init_segment_dispatched_&&(l.push(u),d+=u.data.byteLength)}var _=Math.floor(t/this.timescale_),f=Math.floor(i/this.timescale_);if(l.length){var p=this.video_track_,m={units:l,length:d,isKeyframe:c,dts:f,pts:_,cts:_-f,file_position:n};p.samples.push(m),p.length+=d}},t.prototype.parseH265Payload=function(e,t,i,n,a){for(var s=new ue(e),r=null,l=[],d=0,c=!1;null!=(r=s.readNextNaluPayload());){var u=new ce(r);if(u.type===Z.kSliceVPS){if(!this.video_init_segment_dispatched_){var h=k.parseVPS(r.data);this.video_metadata_.vps=u,this.video_metadata_.details=ge(ge({},this.video_metadata_.details),h)}}else u.type===Z.kSliceSPS?(h=k.parseSPS(r.data),this.video_init_segment_dispatched_?!0===this.detectVideoMetadataChange(u,h)&&(o.a.v(this.TAG,"H265: Critical h265 metadata has been changed, attempt to re-generate InitSegment"),this.video_metadata_changed_=!0,this.video_metadata_={vps:void 0,sps:u,pps:void 0,details:h}):(this.video_metadata_.sps=u,this.video_metadata_.details=ge(ge({},this.video_metadata_.details),h))):u.type===Z.kSlicePPS?this.video_init_segment_dispatched_&&!this.video_metadata_changed_||(h=k.parsePPS(r.data),this.video_metadata_.pps=u,this.video_metadata_.details=ge(ge({},this.video_metadata_.details),h),this.video_metadata_.vps&&this.video_metadata_.sps&&this.video_metadata_.pps&&(this.video_metadata_changed_&&this.dispatchVideoMediaSegment(),this.dispatchVideoInitSegment())):u.type!==Z.kSliceIDR_W_RADL&&u.type!==Z.kSliceIDR_N_LP&&u.type!==Z.kSliceCRA_NUT||(c=!0);this.video_init_segment_dispatched_&&(l.push(u),d+=u.data.byteLength)}var _=Math.floor(t/this.timescale_),f=Math.floor(i/this.timescale_);if(l.length){var p=this.video_track_,m={units:l,length:d,isKeyframe:c,dts:f,pts:_,cts:_-f,file_position:n};p.samples.push(m),p.length+=d}},t.prototype.detectVideoMetadataChange=function(e,t){if(t.codec_mimetype!==this.video_metadata_.details.codec_mimetype)return o.a.v(this.TAG,"Video: Codec mimeType changed from "+this.video_metadata_.details.codec_mimetype+" to "+t.codec_mimetype),!0;if(t.codec_size.width!==this.video_metadata_.details.codec_size.width||t.codec_size.height!==this.video_metadata_.details.codec_size.height){var i=this.video_metadata_.details.codec_size,n=t.codec_size;return o.a.v(this.TAG,"Video: Coded Resolution changed from "+i.width+"x"+i.height+" to "+n.width+"x"+n.height),!0}return t.present_size.width!==this.video_metadata_.details.present_size.width&&(o.a.v(this.TAG,"Video: Present resolution width changed from "+this.video_metadata_.details.present_size.width+" to "+t.present_size.width),!0)},t.prototype.isInitSegmentDispatched=function(){return this.has_video_&&this.has_audio_?this.video_init_segment_dispatched_&&this.audio_init_segment_dispatched_:this.has_video_&&!this.has_audio_?this.video_init_segment_dispatched_:!(this.has_video_||!this.has_audio_)&&this.audio_init_segment_dispatched_},t.prototype.dispatchVideoInitSegment=function(){var e=this.video_metadata_.details,t={type:"video"};t.id=this.video_track_.id,t.timescale=1e3,t.duration=this.duration_,t.codecWidth=e.codec_size.width,t.codecHeight=e.codec_size.height,t.presentWidth=e.present_size.width,t.presentHeight=e.present_size.height,t.profile=e.profile_string,t.level=e.level_string,t.bitDepth=e.bit_depth,t.chromaFormat=e.chroma_format,t.sarRatio=e.sar_ratio,t.frameRate=e.frame_rate;var i=t.frameRate.fps_den,n=t.frameRate.fps_num;if(t.refSampleDuration=i/n*1e3,t.codec=e.codec_mimetype,this.video_metadata_.vps){var a=this.video_metadata_.vps.data.subarray(4),s=this.video_metadata_.sps.data.subarray(4),r=this.video_metadata_.pps.data.subarray(4),l=new he(a,s,r,e);t.hvcc=l.getData(),0==this.video_init_segment_dispatched_&&o.a.v(this.TAG,"Generated first HEVCDecoderConfigurationRecord for mimeType: "+t.codec)}else{s=this.video_metadata_.sps.data.subarray(4),r=this.video_metadata_.pps.data.subarray(4);var d=new N(s,r,e);t.avcc=d.getData(),0==this.video_init_segment_dispatched_&&o.a.v(this.TAG,"Generated first AVCDecoderConfigurationRecord for mimeType: "+t.codec)}this.onTrackMetadata("video",t),this.video_init_segment_dispatched_=!0,this.video_metadata_changed_=!1;var c=this.media_info_;c.hasVideo=!0,c.width=t.codecWidth,c.height=t.codecHeight,c.fps=t.frameRate.fps,c.profile=t.profile,c.level=t.level,c.refFrames=e.ref_frames,c.chromaFormat=e.chroma_format_string,c.sarNum=t.sarRatio.width,c.sarDen=t.sarRatio.height,c.videoCodec=t.codec,c.hasAudio&&c.audioCodec?c.mimeType='video/mp2t; codecs="'+c.videoCodec+","+c.audioCodec+'"':c.mimeType='video/mp2t; codecs="'+c.videoCodec+'"',c.isComplete()&&this.onMediaInfo(c)},t.prototype.dispatchVideoMediaSegment=function(){this.isInitSegmentDispatched()&&this.video_track_.length&&this.onDataAvailable(null,this.video_track_)},t.prototype.dispatchAudioMediaSegment=function(){this.isInitSegmentDispatched()&&this.audio_track_.length&&this.onDataAvailable(this.audio_track_,null)},t.prototype.dispatchAudioVideoMediaSegment=function(){this.isInitSegmentDispatched()&&(this.audio_track_.length||this.video_track_.length)&&this.onDataAvailable(this.audio_track_,this.video_track_)},t.prototype.parseADTSAACPayload=function(e,t){if(!this.has_video_||this.video_init_segment_dispatched_){if(this.aac_last_incomplete_data_){var i=new Uint8Array(e.byteLength+this.aac_last_incomplete_data_.byteLength);i.set(this.aac_last_incomplete_data_,0),i.set(e,this.aac_last_incomplete_data_.byteLength),e=i}var n,a;if(null!=t&&(a=t/this.timescale_),"aac"===this.audio_metadata_.codec){if(null==t&&null!=this.aac_last_sample_pts_)n=1024/this.audio_metadata_.sampling_frequency*1e3,a=this.aac_last_sample_pts_+n;else if(null==t)return void o.a.w(this.TAG,"AAC: Unknown pts");if(this.aac_last_incomplete_data_&&this.aac_last_sample_pts_){n=1024/this.audio_metadata_.sampling_frequency*1e3;var s=this.aac_last_sample_pts_+n;Math.abs(s-a)>1&&(o.a.w(this.TAG,"AAC: Detected pts overlapped, expected: "+s+"ms, PES pts: "+a+"ms"),a=s)}}for(var r,l=new W(e),d=null,c=a;null!=(d=l.readNextAACFrame());){n=1024/d.sampling_frequency*1e3;var u={codec:"aac",data:d};0==this.audio_init_segment_dispatched_?(this.audio_metadata_={codec:"aac",audio_object_type:d.audio_object_type,sampling_freq_index:d.sampling_freq_index,sampling_frequency:d.sampling_frequency,channel_config:d.channel_config},this.dispatchAudioInitSegment(u)):this.detectAudioMetadataChange(u)&&(this.dispatchAudioMediaSegment(),this.dispatchAudioInitSegment(u)),r=c;var h=Math.floor(c),_={unit:d.data,length:d.data.byteLength,pts:h,dts:h};this.audio_track_.samples.push(_),this.audio_track_.length+=d.data.byteLength,c+=n}l.hasIncompleteData()&&(this.aac_last_incomplete_data_=l.getIncompleteData()),r&&(this.aac_last_sample_pts_=r)}},t.prototype.parseLOASAACPayload=function(e,t){var i;if(!this.has_video_||this.video_init_segment_dispatched_){if(this.aac_last_incomplete_data_){var n=new Uint8Array(e.byteLength+this.aac_last_incomplete_data_.byteLength);n.set(this.aac_last_incomplete_data_,0),n.set(e,this.aac_last_incomplete_data_.byteLength),e=n}var a,s;if(null!=t&&(s=t/this.timescale_),"aac"===this.audio_metadata_.codec){if(null==t&&null!=this.aac_last_sample_pts_)a=1024/this.audio_metadata_.sampling_frequency*1e3,s=this.aac_last_sample_pts_+a;else if(null==t)return void o.a.w(this.TAG,"AAC: Unknown pts");if(this.aac_last_incomplete_data_&&this.aac_last_sample_pts_){a=1024/this.audio_metadata_.sampling_frequency*1e3;var r=this.aac_last_sample_pts_+a;Math.abs(r-s)>1&&(o.a.w(this.TAG,"AAC: Detected pts overlapped, expected: "+r+"ms, PES pts: "+s+"ms"),s=r)}}for(var l,d=new K(e),c=null,u=s;null!=(c=d.readNextAACFrame(null!==(i=this.loas_previous_frame)&&void 0!==i?i:void 0));){this.loas_previous_frame=c,a=1024/c.sampling_frequency*1e3;var h={codec:"aac",data:c};0==this.audio_init_segment_dispatched_?(this.audio_metadata_={codec:"aac",audio_object_type:c.audio_object_type,sampling_freq_index:c.sampling_freq_index,sampling_frequency:c.sampling_frequency,channel_config:c.channel_config},this.dispatchAudioInitSegment(h)):this.detectAudioMetadataChange(h)&&(this.dispatchAudioMediaSegment(),this.dispatchAudioInitSegment(h)),l=u;var _=Math.floor(u),f={unit:c.data,length:c.data.byteLength,pts:_,dts:_};this.audio_track_.samples.push(f),this.audio_track_.length+=c.data.byteLength,u+=a}d.hasIncompleteData()&&(this.aac_last_incomplete_data_=d.getIncompleteData()),l&&(this.aac_last_sample_pts_=l)}},t.prototype.parseAC3Payload=function(e,t){if(!this.has_video_||this.video_init_segment_dispatched_){var i,n;if(null!=t&&(n=t/this.timescale_),"ac-3"===this.audio_metadata_.codec)if(null==t&&null!=this.aac_last_sample_pts_)i=1536/this.audio_metadata_.sampling_frequency*1e3,n=this.aac_last_sample_pts_+i;else if(null==t)return void o.a.w(this.TAG,"Opus: Unknown pts");for(var a,s=new pe(e),l=null,d=n;null!=(l=s.readNextAC3Frame());){i=1536/l.sampling_frequency*1e3;var c={codec:"ac-3",data:l};0==this.audio_init_segment_dispatched_?(this.audio_metadata_={codec:"ac-3",sampling_frequency:l.sampling_frequency,bit_stream_identification:l.bit_stream_identification,bit_stream_mode:l.bit_stream_mode,low_frequency_effects_channel_on:l.low_frequency_effects_channel_on,channel_mode:l.channel_mode},console.log((0,r.default)(this.audio_metadata_)),this.dispatchAudioInitSegment(c)):this.detectAudioMetadataChange(c)&&(this.dispatchAudioMediaSegment(),this.dispatchAudioInitSegment(c)),a=d;var u=Math.floor(d),h={unit:l.data,length:l.data.byteLength,pts:u,dts:u};this.audio_track_.samples.push(h),this.audio_track_.length+=l.data.byteLength,d+=i}a&&(this.aac_last_sample_pts_=a)}},t.prototype.parseOpusPayload=function(e,t){if(!this.has_video_||this.video_init_segment_dispatched_){var i,n;if(null!=t&&(n=t/this.timescale_),"opus"===this.audio_metadata_.codec)if(null==t&&null!=this.aac_last_sample_pts_)i=20,n=this.aac_last_sample_pts_+i;else if(null==t)return void o.a.w(this.TAG,"Opus: Unknown pts");for(var a,s=n,r=0;r<e.length;){i=20;for(var l=0!=(16&e[r+1]),d=0!=(8&e[r+1]),c=r+2,u=0;255===e[c];)u+=255,c+=1;u+=e[c],c+=1,c+=l?2:0,c+=d?2:0,a=s;var h=Math.floor(s),_=e.slice(c,c+u),f={unit:_,length:_.byteLength,pts:h,dts:h};this.audio_track_.samples.push(f),this.audio_track_.length+=_.byteLength,s+=i,r=c+u}a&&(this.aac_last_sample_pts_=a)}},t.prototype.parseMP3Payload=function(e,t){if(!this.has_video_||this.video_init_segment_dispatched_){var i=e[1]>>>3&3,n=(6&e[1])>>1,a=(e[2],(12&e[2])>>>2),o=3!=(e[3]>>>6&3)?2:1,s=0,r=34;switch(i){case 0:s=[11025,12e3,8e3,0][a];break;case 2:s=[22050,24e3,16e3,0][a];break;case 3:s=[44100,48e3,32e3,0][a]}switch(n){case 1:r=34;break;case 2:r=33;break;case 3:r=32}var l=new function(){};l.object_type=r,l.sample_rate=s,l.channel_count=o,l.data=e;var d={codec:"mp3",data:l};0==this.audio_init_segment_dispatched_?(this.audio_metadata_={codec:"mp3",object_type:r,sample_rate:s,channel_count:o},this.dispatchAudioInitSegment(d)):this.detectAudioMetadataChange(d)&&(this.dispatchAudioMediaSegment(),this.dispatchAudioInitSegment(d));var c={unit:e,length:e.byteLength,pts:t/this.timescale_,dts:t/this.timescale_};this.audio_track_.samples.push(c),this.audio_track_.length+=e.byteLength}},t.prototype.detectAudioMetadataChange=function(e){if(e.codec!==this.audio_metadata_.codec)return o.a.v(this.TAG,"Audio: Audio Codecs changed from "+this.audio_metadata_.codec+" to "+e.codec),!0;if("aac"===e.codec&&"aac"===this.audio_metadata_.codec){if((t=e.data).audio_object_type!==this.audio_metadata_.audio_object_type)return o.a.v(this.TAG,"AAC: AudioObjectType changed from "+this.audio_metadata_.audio_object_type+" to "+t.audio_object_type),!0;if(t.sampling_freq_index!==this.audio_metadata_.sampling_freq_index)return o.a.v(this.TAG,"AAC: SamplingFrequencyIndex changed from "+this.audio_metadata_.sampling_freq_index+" to "+t.sampling_freq_index),!0;if(t.channel_config!==this.audio_metadata_.channel_config)return o.a.v(this.TAG,"AAC: Channel configuration changed from "+this.audio_metadata_.channel_config+" to "+t.channel_config),!0}else if("ac-3"===e.codec&&"ac-3"===this.audio_metadata_.codec){var t;if((t=e.data).sampling_frequency!==this.audio_metadata_.sampling_frequency)return o.a.v(this.TAG,"AC3: Sampling Frequency changed from "+this.audio_metadata_.sampling_frequency+" to "+t.sampling_frequency),!0;if(t.bit_stream_identification!==this.audio_metadata_.bit_stream_identification)return o.a.v(this.TAG,"AC3: Bit Stream Identification changed from "+this.audio_metadata_.bit_stream_identification+" to "+t.bit_stream_identification),!0;if(t.bit_stream_mode!==this.audio_metadata_.bit_stream_mode)return o.a.v(this.TAG,"AC3: BitStream Mode changed from "+this.audio_metadata_.bit_stream_mode+" to "+t.bit_stream_mode),!0;if(t.channel_mode!==this.audio_metadata_.channel_mode)return o.a.v(this.TAG,"AC3: Channel Mode changed from "+this.audio_metadata_.channel_mode+" to "+t.channel_mode),!0;if(t.low_frequency_effects_channel_on!==this.audio_metadata_.low_frequency_effects_channel_on)return o.a.v(this.TAG,"AC3: Low Frequency Effects Channel On changed from "+this.audio_metadata_.low_frequency_effects_channel_on+" to "+t.low_frequency_effects_channel_on),!0}else if("opus"===e.codec&&"opus"===this.audio_metadata_.codec){if((i=e.meta).sample_rate!==this.audio_metadata_.sample_rate)return o.a.v(this.TAG,"Opus: SamplingFrequencyIndex changed from "+this.audio_metadata_.sample_rate+" to "+i.sample_rate),!0;if(i.channel_count!==this.audio_metadata_.channel_count)return o.a.v(this.TAG,"Opus: Channel count changed from "+this.audio_metadata_.channel_count+" to "+i.channel_count),!0}else if("mp3"===e.codec&&"mp3"===this.audio_metadata_.codec){var i;if((i=e.data).object_type!==this.audio_metadata_.object_type)return o.a.v(this.TAG,"MP3: AudioObjectType changed from "+this.audio_metadata_.object_type+" to "+i.object_type),!0;if(i.sample_rate!==this.audio_metadata_.sample_rate)return o.a.v(this.TAG,"MP3: SamplingFrequencyIndex changed from "+this.audio_metadata_.sample_rate+" to "+i.sample_rate),!0;if(i.channel_count!==this.audio_metadata_.channel_count)return o.a.v(this.TAG,"MP3: Channel count changed from "+this.audio_metadata_.channel_count+" to "+i.channel_count),!0}return!1},t.prototype.dispatchAudioInitSegment=function(e){var t={type:"audio"};if(t.id=this.audio_track_.id,t.timescale=1e3,t.duration=this.duration_,"aac"===this.audio_metadata_.codec){var i=new function(e){var t=null,i=e.audio_object_type,n=e.audio_object_type,a=e.sampling_freq_index,o=e.channel_config,s=0,r=navigator.userAgent.toLowerCase();-1!==r.indexOf("firefox")?a>=6?(n=5,t=new Array(4),s=a-3):(n=2,t=new Array(2),s=a):-1!==r.indexOf("android")?(n=2,t=new Array(2),s=a):(n=5,s=a,t=new Array(4),a>=6?s=a-3:1===o&&(n=2,t=new Array(2),s=a)),t[0]=n<<3,t[0]|=(15&a)>>>1,t[1]=(15&a)<<7,t[1]|=(15&o)<<3,5===n&&(t[1]|=(15&s)>>>1,t[2]=(1&s)<<7,t[2]|=8,t[3]=0),this.config=t,this.sampling_rate=z[a],this.channel_count=o,this.codec_mimetype="mp4a.40."+n,this.original_codec_mimetype="mp4a.40."+i}("aac"===e.codec?e.data:null);t.audioSampleRate=i.sampling_rate,t.channelCount=i.channel_count,t.codec=i.codec_mimetype,t.originalCodec=i.original_codec_mimetype,t.config=i.config,t.refSampleDuration=1024/t.audioSampleRate*t.timescale}else if("ac-3"===this.audio_metadata_.codec){var n=new function(e){var t;t=[e.sampling_rate_code<<6|e.bit_stream_identification<<1|e.bit_stream_mode>>2,(3&e.bit_stream_mode)<<6|e.channel_mode<<3|e.low_frequency_effects_channel_on<<2|e.frame_size_code>>4,e.frame_size_code<<4&224],this.config=t,this.sampling_rate=e.sampling_frequency,this.bit_stream_identification=e.bit_stream_identification,this.bit_stream_mode=e.bit_stream_mode,this.low_frequency_effects_channel_on=e.low_frequency_effects_channel_on,this.channel_count=e.channel_count,this.channel_mode=e.channel_mode,this.codec_mimetype="ac-3",this.original_codec_mimetype="ac-3"}("ac-3"===e.codec?e.data:null);t.audioSampleRate=n.sampling_rate,t.channelCount=n.channel_count,t.codec=n.codec_mimetype,t.originalCodec=n.original_codec_mimetype,t.config=n.config,t.refSampleDuration=1536/t.audioSampleRate*t.timescale}else"opus"===this.audio_metadata_.codec?(t.audioSampleRate=this.audio_metadata_.sample_rate,t.channelCount=this.audio_metadata_.channel_count,t.channelConfigCode=this.audio_metadata_.channel_config_code,t.codec="opus",t.originalCodec="opus",t.config=void 0,t.refSampleDuration=20):"mp3"===this.audio_metadata_.codec&&(t.audioSampleRate=this.audio_metadata_.sample_rate,t.channelCount=this.audio_metadata_.channel_count,t.codec="mp3",t.originalCodec="mp3",t.config=void 0);0==this.audio_init_segment_dispatched_&&o.a.v(this.TAG,"Generated first AudioSpecificConfig for mimeType: "+t.codec),this.onTrackMetadata("audio",t),this.audio_init_segment_dispatched_=!0,this.video_metadata_changed_=!1;var a=this.media_info_;a.hasAudio=!0,a.audioCodec=t.originalCodec,a.audioSampleRate=t.audioSampleRate,a.audioChannelCount=t.channelCount,a.hasVideo&&a.videoCodec?a.mimeType='video/mp2t; codecs="'+a.videoCodec+","+a.audioCodec+'"':a.mimeType='video/mp2t; codecs="'+a.audioCodec+'"',a.isComplete()&&this.onMediaInfo(a)},t.prototype.dispatchPESPrivateDataDescriptor=function(e,t,i){var n=new function(){};n.pid=e,n.stream_type=t,n.descriptor=i,this.onPESPrivateDataDescriptor&&this.onPESPrivateDataDescriptor(n)},t.prototype.parsePESPrivateDataPayload=function(e,t,i,n,a){var o=new Y;if(o.pid=n,o.stream_id=a,o.len=e.byteLength,o.data=e,null!=t){var s=Math.floor(t/this.timescale_);o.pts=s}else o.nearest_pts=this.aac_last_sample_pts_;if(null!=i){var r=Math.floor(i/this.timescale_);o.dts=r}this.onPESPrivateData&&this.onPESPrivateData(o)},t.prototype.parseTimedID3MetadataPayload=function(e,t,i,n,a){var o=new Y;if(o.pid=n,o.stream_id=a,o.len=e.byteLength,o.data=e,null!=t){var s=Math.floor(t/this.timescale_);o.pts=s}if(null!=i){var r=Math.floor(i/this.timescale_);o.dts=r}this.onTimedID3Metadata&&this.onTimedID3Metadata(o)},t.prototype.parseSMPTE2038MetadataPayload=function(e,t,i,n,a){var o=new function(){};if(o.pid=n,o.stream_id=a,o.len=e.byteLength,o.data=e,null!=t){var s=Math.floor(t/this.timescale_);o.pts=s}if(o.nearest_pts=this.aac_last_sample_pts_,null!=i){var r=Math.floor(i/this.timescale_);o.dts=r}o.ancillaries=function(e){for(var t=new w(e),i=0,n=[];i+=6,0===t.readBits(6);){var a=t.readBool();i+=1;var o=t.readBits(11);i+=11;var s=t.readBits(12);i+=12;var r=255&t.readBits(10);i+=10;var l=255&t.readBits(10);i+=10;var d=255&t.readBits(10);i+=10;for(var c=new Uint8Array(d),u=0;u<d;u++){var h=255&t.readBits(10);i+=10,c[u]=h}t.readBits(10),i+=10;var _="User Defined";65===r?7===l&&(_="SCTE-104"):95===r?220===l?_="ARIB STD-B37 (1SEG)":221===l?_="ARIB STD-B37 (ANALOG)":222===l?_="ARIB STD-B37 (SD)":223===l&&(_="ARIB STD-B37 (HD)"):97===r&&(1===l?_="EIA-708":2===l&&(_="EIA-608")),n.push({yc_indicator:a,line_number:o,horizontal_offset:s,did:r,sdid:l,user_data:c,description:_,information:{}}),t.readBits(8-(i-Math.floor(i/8))%8),i+=(8-(i-Math.floor(i/8)))%8}return t.destroy(),t=null,n}(e),this.onSMPTE2038Metadata&&this.onSMPTE2038Metadata(o)},t}(x),ye=function(){function e(){}return e.init=function(){for(var t in e.types={avc1:[],avcC:[],btrt:[],dinf:[],dref:[],esds:[],ftyp:[],hdlr:[],hvc1:[],hvcC:[],mdat:[],mdhd:[],mdia:[],mfhd:[],minf:[],moof:[],moov:[],mp4a:[],mvex:[],mvhd:[],sdtp:[],stbl:[],stco:[],stsc:[],stsd:[],stsz:[],stts:[],tfdt:[],tfhd:[],traf:[],trak:[],trun:[],trex:[],tkhd:[],vmhd:[],smhd:[],".mp3":[],Opus:[],dOps:[],"ac-3":[],dac3:[]},e.types)e.types.hasOwnProperty(t)&&(e.types[t]=[t.charCodeAt(0),t.charCodeAt(1),t.charCodeAt(2),t.charCodeAt(3)]);var i=e.constants={};i.FTYP=new Uint8Array([105,115,111,109,0,0,0,1,105,115,111,109,97,118,99,49]),i.STSD_PREFIX=new Uint8Array([0,0,0,0,0,0,0,1]),i.STTS=new Uint8Array([0,0,0,0,0,0,0,0]),i.STSC=i.STCO=i.STTS,i.STSZ=new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0]),i.HDLR_VIDEO=new Uint8Array([0,0,0,0,0,0,0,0,118,105,100,101,0,0,0,0,0,0,0,0,0,0,0,0,86,105,100,101,111,72,97,110,100,108,101,114,0]),i.HDLR_AUDIO=new Uint8Array([0,0,0,0,0,0,0,0,115,111,117,110,0,0,0,0,0,0,0,0,0,0,0,0,83,111,117,110,100,72,97,110,100,108,101,114,0]),i.DREF=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,12,117,114,108,32,0,0,0,1]),i.SMHD=new Uint8Array([0,0,0,0,0,0,0,0]),i.VMHD=new Uint8Array([0,0,0,1,0,0,0,0,0,0,0,0])},e.box=function(e){for(var t=8,i=null,n=Array.prototype.slice.call(arguments,1),a=n.length,o=0;o<a;o++)t+=n[o].byteLength;(i=new Uint8Array(t))[0]=t>>>24&255,i[1]=t>>>16&255,i[2]=t>>>8&255,i[3]=255&t,i.set(e,4);var s=8;for(o=0;o<a;o++)i.set(n[o],s),s+=n[o].byteLength;return i},e.generateInitSegment=function(t){var i=e.box(e.types.ftyp,e.constants.FTYP),n=e.moov(t),a=new Uint8Array(i.byteLength+n.byteLength);return a.set(i,0),a.set(n,i.byteLength),a},e.moov=function(t){var i=e.mvhd(t.timescale,t.duration),n=e.trak(t),a=e.mvex(t);return e.box(e.types.moov,i,n,a)},e.mvhd=function(t,i){return e.box(e.types.mvhd,new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0,t>>>24&255,t>>>16&255,t>>>8&255,255&t,i>>>24&255,i>>>16&255,i>>>8&255,255&i,0,1,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,64,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,255,255,255,255]))},e.trak=function(t){return e.box(e.types.trak,e.tkhd(t),e.mdia(t))},e.tkhd=function(t){var i=t.id,n=t.duration,a=t.presentWidth,o=t.presentHeight;return e.box(e.types.tkhd,new Uint8Array([0,0,0,7,0,0,0,0,0,0,0,0,i>>>24&255,i>>>16&255,i>>>8&255,255&i,0,0,0,0,n>>>24&255,n>>>16&255,n>>>8&255,255&n,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,64,0,0,0,a>>>8&255,255&a,0,0,o>>>8&255,255&o,0,0]))},e.mdia=function(t){return e.box(e.types.mdia,e.mdhd(t),e.hdlr(t),e.minf(t))},e.mdhd=function(t){var i=t.timescale,n=t.duration;return e.box(e.types.mdhd,new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0,i>>>24&255,i>>>16&255,i>>>8&255,255&i,n>>>24&255,n>>>16&255,n>>>8&255,255&n,85,196,0,0]))},e.hdlr=function(t){var i;return i="audio"===t.type?e.constants.HDLR_AUDIO:e.constants.HDLR_VIDEO,e.box(e.types.hdlr,i)},e.minf=function(t){var i;return i="audio"===t.type?e.box(e.types.smhd,e.constants.SMHD):e.box(e.types.vmhd,e.constants.VMHD),e.box(e.types.minf,i,e.dinf(),e.stbl(t))},e.dinf=function(){return e.box(e.types.dinf,e.box(e.types.dref,e.constants.DREF))},e.stbl=function(t){return e.box(e.types.stbl,e.stsd(t),e.box(e.types.stts,e.constants.STTS),e.box(e.types.stsc,e.constants.STSC),e.box(e.types.stsz,e.constants.STSZ),e.box(e.types.stco,e.constants.STCO))},e.stsd=function(t){return"audio"===t.type?"mp3"===t.codec?e.box(e.types.stsd,e.constants.STSD_PREFIX,e.mp3(t)):"ac-3"===t.codec?e.box(e.types.stsd,e.constants.STSD_PREFIX,e.ac3(t)):"opus"===t.codec?e.box(e.types.stsd,e.constants.STSD_PREFIX,e.Opus(t)):e.box(e.types.stsd,e.constants.STSD_PREFIX,e.mp4a(t)):"video"===t.type&&t.codec.startsWith("hvc1")?e.box(e.types.stsd,e.constants.STSD_PREFIX,e.hvc1(t)):e.box(e.types.stsd,e.constants.STSD_PREFIX,e.avc1(t))},e.mp3=function(t){var i=t.channelCount,n=t.audioSampleRate,a=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,i,0,16,0,0,0,0,n>>>8&255,255&n,0,0]);return e.box(e.types[".mp3"],a)},e.mp4a=function(t){var i=t.channelCount,n=t.audioSampleRate,a=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,i,0,16,0,0,0,0,n>>>8&255,255&n,0,0]);return e.box(e.types.mp4a,a,e.esds(t))},e.ac3=function(t){var i=t.channelCount,n=t.audioSampleRate,a=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,i,0,16,0,0,0,0,n>>>8&255,255&n,0,0]);return e.box(e.types["ac-3"],a,e.box(e.types.dac3,new Uint8Array(t.config)))},e.esds=function(t){var i=t.config||[],n=i.length,a=new Uint8Array([0,0,0,0,3,23+n,0,1,0,4,15+n,64,21,0,0,0,0,0,0,0,0,0,0,0,5].concat([n]).concat(i).concat([6,1,2]));return e.box(e.types.esds,a)},e.Opus=function(t){var i=t.channelCount,n=t.audioSampleRate,a=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,i,0,16,0,0,0,0,n>>>8&255,255&n,0,0]);return e.box(e.types.Opus,a,e.dOps(t))},e.dOps=function(t){var i=t.channelCount,n=t.channelConfigCode,a=t.audioSampleRate;if(t.config)return e.box(e.types.dOps,s);var o=[];switch(n){case 1:case 2:o=[0];break;case 0:o=[255,1,1,0,1];break;case 128:o=[255,2,0,0,1];break;case 3:o=[1,2,1,0,2,1];break;case 4:o=[1,2,2,0,1,2,3];break;case 5:o=[1,3,2,0,4,1,2,3];break;case 6:o=[1,4,2,0,4,1,2,3,5];break;case 7:o=[1,4,2,0,4,1,2,3,5,6];break;case 8:o=[1,5,3,0,6,1,2,3,4,5,7];break;case 130:o=[1,1,2,0,1];break;case 131:o=[1,1,3,0,1,2];break;case 132:o=[1,1,4,0,1,2,3];break;case 133:o=[1,1,5,0,1,2,3,4];break;case 134:o=[1,1,6,0,1,2,3,4,5];break;case 135:o=[1,1,7,0,1,2,3,4,5,6];break;case 136:o=[1,1,8,0,1,2,3,4,5,6,7]}var s=new Uint8Array(function(){for(var e=0,t=0,i=arguments.length;t<i;t++)e+=arguments[t].length;var n=Array(e),a=0;for(t=0;t<i;t++)for(var o=arguments[t],s=0,r=o.length;s<r;s++,a++)n[a]=o[s];return n}([0,i,0,0,a>>>24&255,a>>>17&255,a>>>8&255,a>>>0&255,0,0],o));return e.box(e.types.dOps,s)},e.avc1=function(t){var i=t.avcc,n=t.codecWidth,a=t.codecHeight,o=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,n>>>8&255,255&n,a>>>8&255,255&a,0,72,0,0,0,72,0,0,0,0,0,0,0,1,10,120,113,113,47,102,108,118,46,106,115,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,24,255,255]);return e.box(e.types.avc1,o,e.box(e.types.avcC,i))},e.hvc1=function(t){var i=t.hvcc,n=t.codecWidth,a=t.codecHeight,o=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,n>>>8&255,255&n,a>>>8&255,255&a,0,72,0,0,0,72,0,0,0,0,0,0,0,1,10,120,113,113,47,102,108,118,46,106,115,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,24,255,255]);return e.box(e.types.hvc1,o,e.box(e.types.hvcC,i))},e.mvex=function(t){return e.box(e.types.mvex,e.trex(t))},e.trex=function(t){var i=t.id,n=new Uint8Array([0,0,0,0,i>>>24&255,i>>>16&255,i>>>8&255,255&i,0,0,0,1,0,0,0,0,0,0,0,0,0,1,0,1]);return e.box(e.types.trex,n)},e.moof=function(t,i){return e.box(e.types.moof,e.mfhd(t.sequenceNumber),e.traf(t,i))},e.mfhd=function(t){var i=new Uint8Array([0,0,0,0,t>>>24&255,t>>>16&255,t>>>8&255,255&t]);return e.box(e.types.mfhd,i)},e.traf=function(t,i){var n=t.id,a=e.box(e.types.tfhd,new Uint8Array([0,0,0,0,n>>>24&255,n>>>16&255,n>>>8&255,255&n])),o=e.box(e.types.tfdt,new Uint8Array([0,0,0,0,i>>>24&255,i>>>16&255,i>>>8&255,255&i])),s=e.sdtp(t),r=e.trun(t,s.byteLength+16+16+8+16+8+8);return e.box(e.types.traf,a,o,r,s)},e.sdtp=function(t){for(var i=t.samples||[],n=i.length,a=new Uint8Array(4+n),o=0;o<n;o++){var s=i[o].flags;a[o+4]=s.isLeading<<6|s.dependsOn<<4|s.isDependedOn<<2|s.hasRedundancy}return e.box(e.types.sdtp,a)},e.trun=function(t,i){var n=t.samples||[],a=n.length,o=12+16*a,s=new Uint8Array(o);i+=8+o,s.set([0,0,15,1,a>>>24&255,a>>>16&255,a>>>8&255,255&a,i>>>24&255,i>>>16&255,i>>>8&255,255&i],0);for(var r=0;r<a;r++){var l=n[r].duration,d=n[r].size,c=n[r].flags,u=n[r].cts;s.set([l>>>24&255,l>>>16&255,l>>>8&255,255&l,d>>>24&255,d>>>16&255,d>>>8&255,255&d,c.isLeading<<2|c.dependsOn,c.isDependedOn<<6|c.hasRedundancy<<4|c.isNonSync,0,0,u>>>24&255,u>>>16&255,u>>>8&255,255&u],12+16*r)}return e.box(e.types.trun,s)},e.mdat=function(t){return e.box(e.types.mdat,t)},e}();ye.init();var be=ye,Se=function(){function e(){}return e.getSilentFrame=function(e,t){if("mp4a.40.2"===e){if(1===t)return new Uint8Array([0,200,0,128,35,128]);if(2===t)return new Uint8Array([33,0,73,144,2,25,0,35,128]);if(3===t)return new Uint8Array([0,200,0,128,32,132,1,38,64,8,100,0,142]);if(4===t)return new Uint8Array([0,200,0,128,32,132,1,38,64,8,100,0,128,44,128,8,2,56]);if(5===t)return new Uint8Array([0,200,0,128,32,132,1,38,64,8,100,0,130,48,4,153,0,33,144,2,56]);if(6===t)return new Uint8Array([0,200,0,128,32,132,1,38,64,8,100,0,130,48,4,153,0,33,144,2,0,178,0,32,8,224])}else{if(1===t)return new Uint8Array([1,64,34,128,163,78,230,128,186,8,0,0,0,28,6,241,193,10,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,94]);if(2===t)return new Uint8Array([1,64,34,128,163,94,230,128,186,8,0,0,0,0,149,0,6,241,161,10,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,94]);if(3===t)return new Uint8Array([1,64,34,128,163,94,230,128,186,8,0,0,0,0,149,0,6,241,161,10,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,94])}return null},e}(),we=i(7),Ce=function(){function e(e){this.TAG="MP4Remuxer",this._config=e,this._isLive=!0===e.isLive,this._dtsBase=-1,this._dtsBaseInited=!1,this._audioDtsBase=1/0,this._videoDtsBase=1/0,this._audioNextDts=void 0,this._videoNextDts=void 0,this._audioStashedLastSample=null,this._videoStashedLastSample=null,this._audioMeta=null,this._videoMeta=null,this._audioSegmentInfoList=new we.c("audio"),this._videoSegmentInfoList=new we.c("video"),this._onInitSegment=null,this._onMediaSegment=null,this._forceFirstIDR=!(!d.a.chrome||!(d.a.version.major<50||50===d.a.version.major&&d.a.version.build<2661)),this._fillSilentAfterSeek=d.a.msedge||d.a.msie,this._mp3UseMpegAudio=!d.a.firefox,this._fillAudioTimestampGap=this._config.fixAudioTimestampGap}return e.prototype.destroy=function(){this._dtsBase=-1,this._dtsBaseInited=!1,this._audioMeta=null,this._videoMeta=null,this._audioSegmentInfoList.clear(),this._audioSegmentInfoList=null,this._videoSegmentInfoList.clear(),this._videoSegmentInfoList=null,this._onInitSegment=null,this._onMediaSegment=null},e.prototype.bindDataSource=function(e){return e.onDataAvailable=this.remux.bind(this),e.onTrackMetadata=this._onTrackMetadataReceived.bind(this),this},Object.defineProperty(e.prototype,"onInitSegment",{get:function(){return this._onInitSegment},set:function(e){this._onInitSegment=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"onMediaSegment",{get:function(){return this._onMediaSegment},set:function(e){this._onMediaSegment=e},enumerable:!1,configurable:!0}),e.prototype.insertDiscontinuity=function(){this._audioNextDts=this._videoNextDts=void 0},e.prototype.seek=function(e){this._audioStashedLastSample=null,this._videoStashedLastSample=null,this._videoSegmentInfoList.clear(),this._audioSegmentInfoList.clear()},e.prototype.remux=function(e,t){if(!this._onMediaSegment)throw new v.a("MP4Remuxer: onMediaSegment callback must be specificed!");this._dtsBaseInited||this._calculateDtsBase(e,t),t&&this._remuxVideo(t),e&&this._remuxAudio(e)},e.prototype._onTrackMetadataReceived=function(e,t){var i=null,n="mp4",a=t.codec;if("audio"===e)this._audioMeta=t,"mp3"===t.codec&&this._mp3UseMpegAudio?(n="mpeg",a="",i=new Uint8Array):i=be.generateInitSegment(t);else{if("video"!==e)return;this._videoMeta=t,i=be.generateInitSegment(t)}if(!this._onInitSegment)throw new v.a("MP4Remuxer: onInitSegment callback must be specified!");this._onInitSegment(e,{type:e,data:i.buffer,codec:a,container:e+"/"+n,mediaDuration:t.duration})},e.prototype._calculateDtsBase=function(e,t){this._dtsBaseInited||(e&&e.samples&&e.samples.length&&(this._audioDtsBase=e.samples[0].dts),t&&t.samples&&t.samples.length&&(this._videoDtsBase=t.samples[0].dts),this._dtsBase=Math.min(this._audioDtsBase,this._videoDtsBase),this._dtsBaseInited=!0)},e.prototype.getTimestampBase=function(){if(this._dtsBaseInited)return this._dtsBase},e.prototype.flushStashedSamples=function(){var e=this._videoStashedLastSample,t=this._audioStashedLastSample,i={type:"video",id:1,sequenceNumber:0,samples:[],length:0};null!=e&&(i.samples.push(e),i.length=e.length);var n={type:"audio",id:2,sequenceNumber:0,samples:[],length:0};null!=t&&(n.samples.push(t),n.length=t.length),this._videoStashedLastSample=null,this._audioStashedLastSample=null,this._remuxVideo(i,!0),this._remuxAudio(n,!0)},e.prototype._remuxAudio=function(e,t){if(null!=this._audioMeta){var i,n=e,a=n.samples,s=void 0,r=-1,l=this._audioMeta.refSampleDuration,c="mp3"===this._audioMeta.codec&&this._mp3UseMpegAudio,u=this._dtsBaseInited&&void 0===this._audioNextDts,h=!1;if(a&&0!==a.length&&(1!==a.length||t)){var _=0,f=null,p=0;c?(_=0,p=n.length):(_=8,p=8+n.length);var m=null;if(a.length>1&&(p-=(m=a.pop()).length),null!=this._audioStashedLastSample){var g=this._audioStashedLastSample;this._audioStashedLastSample=null,a.unshift(g),p+=g.length}null!=m&&(this._audioStashedLastSample=m);var v=a[0].dts-this._dtsBase;if(this._audioNextDts)s=v-this._audioNextDts;else if(this._audioSegmentInfoList.isEmpty())s=0,this._fillSilentAfterSeek&&!this._videoSegmentInfoList.isEmpty()&&"mp3"!==this._audioMeta.originalCodec&&(h=!0);else{var y=this._audioSegmentInfoList.getLastSampleBefore(v);if(null!=y){var b=v-(y.originalDts+y.duration);b<=3&&(b=0),s=v-(y.dts+y.duration+b)}else s=0}if(h){var S=v-s,w=this._videoSegmentInfoList.getLastSegmentBefore(v);if(null!=w&&w.beginDts<S){if(I=Se.getSilentFrame(this._audioMeta.originalCodec,this._audioMeta.channelCount)){var C=w.beginDts,E=S-w.beginDts;o.a.v(this.TAG,"InsertPrefixSilentAudio: dts: "+C+", duration: "+E),a.unshift({unit:I,dts:C,pts:C}),p+=I.byteLength}}else h=!1}for(var k=[],T=0;T<a.length;T++){var A=(g=a[T]).unit,D=g.dts-this._dtsBase,L=(C=D,!1),R=null,P=0;if(!(D<-.001)){if("mp3"!==this._audioMeta.codec){var x=D;if(this._audioNextDts&&(x=this._audioNextDts),(s=D-x)<=-3*l){o.a.w(this.TAG,"Dropping 1 audio frame (originalDts: "+D+" ms ,curRefDts: "+x+" ms)  due to dtsCorrection: "+s+" ms overlap.");continue}if(s>=3*l&&this._fillAudioTimestampGap&&!d.a.safari){L=!0;var I,M=Math.floor(s/l);o.a.w(this.TAG,"Large audio timestamp gap detected, may cause AV sync to drift. Silent frames will be generated to avoid unsync.\noriginalDts: "+D+" ms, curRefDts: "+x+" ms, dtsCorrection: "+Math.round(s)+" ms, generate: "+M+" frames"),C=Math.floor(x),P=Math.floor(x+l)-C,null==(I=Se.getSilentFrame(this._audioMeta.originalCodec,this._audioMeta.channelCount))&&(o.a.w(this.TAG,"Unable to generate silent frame for "+this._audioMeta.originalCodec+" with "+this._audioMeta.channelCount+" channels, repeat last frame"),I=A),R=[];for(var B=0;B<M;B++){x+=l;var O=Math.floor(x),U=Math.floor(x+l)-O,V={dts:O,pts:O,cts:0,unit:I,size:I.byteLength,duration:U,originalDts:D,flags:{isLeading:0,dependsOn:1,isDependedOn:0,hasRedundancy:0}};R.push(V),p+=V.size}this._audioNextDts=x+l}else C=Math.floor(x),P=Math.floor(x+l)-C,this._audioNextDts=x+l}else C=D-s,P=T!==a.length-1?a[T+1].dts-this._dtsBase-s-C:null!=m?m.dts-this._dtsBase-s-C:k.length>=1?k[k.length-1].duration:Math.floor(l),this._audioNextDts=C+P;-1===r&&(r=C),k.push({dts:C,pts:C,cts:0,unit:g.unit,size:g.unit.byteLength,duration:P,originalDts:D,flags:{isLeading:0,dependsOn:1,isDependedOn:0,hasRedundancy:0}}),L&&k.push.apply(k,R)}}if(0===k.length)return n.samples=[],void(n.length=0);for(c?f=new Uint8Array(p):((f=new Uint8Array(p))[0]=p>>>24&255,f[1]=p>>>16&255,f[2]=p>>>8&255,f[3]=255&p,f.set(be.types.mdat,4)),T=0;T<k.length;T++)A=k[T].unit,f.set(A,_),_+=A.byteLength;var $=k[k.length-1];i=$.dts+$.duration;var N=new we.b;N.beginDts=r,N.endDts=i,N.beginPts=r,N.endPts=i,N.originalBeginDts=k[0].originalDts,N.originalEndDts=$.originalDts+$.duration,N.firstSample=new we.d(k[0].dts,k[0].pts,k[0].duration,k[0].originalDts,!1),N.lastSample=new we.d($.dts,$.pts,$.duration,$.originalDts,!1),this._isLive||this._audioSegmentInfoList.append(N),n.samples=k,n.sequenceNumber++;var F;F=c?new Uint8Array:be.moof(n,r),n.samples=[],n.length=0;var G={type:"audio",data:this._mergeBoxes(F,f).buffer,sampleCount:k.length,info:N};c&&u&&(G.timestampOffset=r),this._onMediaSegment("audio",G)}}},e.prototype._remuxVideo=function(e,t){if(null!=this._videoMeta){var i,n,a=e,o=a.samples,s=void 0,r=-1,l=-1;if(o&&0!==o.length&&(1!==o.length||t)){var d=8,c=null,u=8+e.length,h=null;if(o.length>1&&(u-=(h=o.pop()).length),null!=this._videoStashedLastSample){var _=this._videoStashedLastSample;this._videoStashedLastSample=null,o.unshift(_),u+=_.length}null!=h&&(this._videoStashedLastSample=h);var f=o[0].dts-this._dtsBase;if(this._videoNextDts)s=f-this._videoNextDts;else if(this._videoSegmentInfoList.isEmpty())s=0;else{var p=this._videoSegmentInfoList.getLastSampleBefore(f);if(null!=p){var m=f-(p.originalDts+p.duration);m<=3&&(m=0),s=f-(p.dts+p.duration+m)}else s=0}for(var g=new we.b,v=[],y=0;y<o.length;y++){var b=(_=o[y]).dts-this._dtsBase,S=_.isKeyframe,w=b-s,C=_.cts,E=w+C;-1===r&&(r=w,l=E);var k=0;if(k=y!==o.length-1?o[y+1].dts-this._dtsBase-s-w:null!=h?h.dts-this._dtsBase-s-w:v.length>=1?v[v.length-1].duration:Math.floor(this._videoMeta.refSampleDuration),S){var T=new we.d(w,E,k,_.dts,!0);T.fileposition=_.fileposition,g.appendSyncPoint(T)}v.push({dts:w,pts:E,cts:C,units:_.units,size:_.length,isKeyframe:S,duration:k,originalDts:b,flags:{isLeading:0,dependsOn:S?2:1,isDependedOn:S?1:0,hasRedundancy:0,isNonSync:S?0:1}})}for((c=new Uint8Array(u))[0]=u>>>24&255,c[1]=u>>>16&255,c[2]=u>>>8&255,c[3]=255&u,c.set(be.types.mdat,4),y=0;y<v.length;y++)for(var A=v[y].units;A.length;){var D=A.shift().data;c.set(D,d),d+=D.byteLength}var L=v[v.length-1];if(i=L.dts+L.duration,n=L.pts+L.duration,this._videoNextDts=i,g.beginDts=r,g.endDts=i,g.beginPts=l,g.endPts=n,g.originalBeginDts=v[0].originalDts,g.originalEndDts=L.originalDts+L.duration,g.firstSample=new we.d(v[0].dts,v[0].pts,v[0].duration,v[0].originalDts,v[0].isKeyframe),g.lastSample=new we.d(L.dts,L.pts,L.duration,L.originalDts,L.isKeyframe),this._isLive||this._videoSegmentInfoList.append(g),a.samples=v,a.sequenceNumber++,this._forceFirstIDR){var R=v[0].flags;R.dependsOn=2,R.isNonSync=0}var P=be.moof(a,r);a.samples=[],a.length=0,this._onMediaSegment("video",{type:"video",data:this._mergeBoxes(P,c).buffer,sampleCount:v.length,info:g})}}},e.prototype._mergeBoxes=function(e,t){var i=new Uint8Array(e.byteLength+t.byteLength);return i.set(e,0),i.set(t,e.byteLength),i},e}(),Ee=i(11),ke=i(1),Te=function(){function e(e,t){this.TAG="TransmuxingController",this._emitter=new a.a,this._config=t,e.segments||(e.segments=[{duration:e.duration,filesize:e.filesize,url:e.url}]),"boolean"!=typeof e.cors&&(e.cors=!0),"boolean"!=typeof e.withCredentials&&(e.withCredentials=!1),this._mediaDataSource=e,this._currentSegmentIndex=0;var i=0;this._mediaDataSource.segments.forEach(function(n){n.timestampBase=i,i+=n.duration,n.cors=e.cors,n.withCredentials=e.withCredentials,t.referrerPolicy&&(n.referrerPolicy=t.referrerPolicy)}),isNaN(i)||this._mediaDataSource.duration===i||(this._mediaDataSource.duration=i),this._mediaInfo=null,this._demuxer=null,this._remuxer=null,this._ioctl=null,this._pendingSeekTime=null,this._pendingResolveSeekPoint=null,this._statisticsReporter=null}return e.prototype.destroy=function(){this._mediaInfo=null,this._mediaDataSource=null,this._statisticsReporter&&this._disableStatisticsReporter(),this._ioctl&&(this._ioctl.destroy(),this._ioctl=null),this._demuxer&&(this._demuxer.destroy(),this._demuxer=null),this._remuxer&&(this._remuxer.destroy(),this._remuxer=null),this._emitter.removeAllListeners(),this._emitter=null},e.prototype.on=function(e,t){this._emitter.addListener(e,t)},e.prototype.off=function(e,t){this._emitter.removeListener(e,t)},e.prototype.start=function(){this._loadSegment(0),this._enableStatisticsReporter()},e.prototype._loadSegment=function(e,t){this._currentSegmentIndex=e;var i=this._mediaDataSource.segments[e],n=this._ioctl=new Ee.a(i,this._config,e);n.onError=this._onIOException.bind(this),n.onSeeked=this._onIOSeeked.bind(this),n.onComplete=this._onIOComplete.bind(this),n.onRedirect=this._onIORedirect.bind(this),n.onRecoveredEarlyEof=this._onIORecoveredEarlyEof.bind(this),t?this._demuxer.bindDataSource(this._ioctl):n.onDataArrival=this._onInitChunkArrival.bind(this),n.open(t)},e.prototype.stop=function(){this._internalAbort(),this._disableStatisticsReporter()},e.prototype._internalAbort=function(){this._ioctl&&(this._ioctl.destroy(),this._ioctl=null)},e.prototype.pause=function(){this._ioctl&&this._ioctl.isWorking()&&(this._ioctl.pause(),this._disableStatisticsReporter())},e.prototype.resume=function(){this._ioctl&&this._ioctl.isPaused()&&(this._ioctl.resume(),this._enableStatisticsReporter())},e.prototype.seek=function(e){if(null!=this._mediaInfo&&this._mediaInfo.isSeekable()){var t=this._searchSegmentIndexContains(e);if(t===this._currentSegmentIndex){var i=this._mediaInfo.segments[t];if(null==i)this._pendingSeekTime=e;else{var n=i.getNearestKeyframe(e);this._remuxer.seek(n.milliseconds),this._ioctl.seek(n.fileposition),this._pendingResolveSeekPoint=n.milliseconds}}else{var a=this._mediaInfo.segments[t];null==a?(this._pendingSeekTime=e,this._internalAbort(),this._remuxer.seek(),this._remuxer.insertDiscontinuity(),this._loadSegment(t)):(n=a.getNearestKeyframe(e),this._internalAbort(),this._remuxer.seek(e),this._remuxer.insertDiscontinuity(),this._demuxer.resetMediaInfo(),this._demuxer.timestampBase=this._mediaDataSource.segments[t].timestampBase,this._loadSegment(t,n.fileposition),this._pendingResolveSeekPoint=n.milliseconds,this._reportSegmentMediaInfo(t))}this._enableStatisticsReporter()}},e.prototype._searchSegmentIndexContains=function(e){for(var t=this._mediaDataSource.segments,i=t.length-1,n=0;n<t.length;n++)if(e<t[n].timestampBase){i=n-1;break}return i},e.prototype._onInitChunkArrival=function(e,t){var i=this,n=0;if(t>0)this._demuxer.bindDataSource(this._ioctl),this._demuxer.timestampBase=this._mediaDataSource.segments[this._currentSegmentIndex].timestampBase,n=this._demuxer.parseChunks(e,t);else{var a=null;(a=P.probe(e)).match&&(this._setupFLVDemuxerRemuxer(a),n=this._demuxer.parseChunks(e,t)),a.match||a.needMoreData||(a=ve.probe(e)).match&&(this._setupTSDemuxerRemuxer(a),n=this._demuxer.parseChunks(e,t)),a.match||a.needMoreData||(a=null,o.a.e(this.TAG,"Non MPEG-TS/FLV, Unsupported media type!"),c.default.resolve().then(function(){i._internalAbort()}),this._emitter.emit(ke.a.DEMUX_ERROR,E.a.FORMAT_UNSUPPORTED,"Non MPEG-TS/FLV, Unsupported media type!"))}return n},e.prototype._setupFLVDemuxerRemuxer=function(e){this._demuxer=new P(e,this._config),this._remuxer||(this._remuxer=new Ce(this._config));var t=this._mediaDataSource;null==t.duration||isNaN(t.duration)||(this._demuxer.overridedDuration=t.duration),"boolean"==typeof t.hasAudio&&(this._demuxer.overridedHasAudio=t.hasAudio),"boolean"==typeof t.hasVideo&&(this._demuxer.overridedHasVideo=t.hasVideo),this._demuxer.timestampBase=t.segments[this._currentSegmentIndex].timestampBase,this._demuxer.onError=this._onDemuxException.bind(this),this._demuxer.onMediaInfo=this._onMediaInfo.bind(this),this._demuxer.onMetaDataArrived=this._onMetaDataArrived.bind(this),this._demuxer.onScriptDataArrived=this._onScriptDataArrived.bind(this),this._remuxer.bindDataSource(this._demuxer.bindDataSource(this._ioctl)),this._remuxer.onInitSegment=this._onRemuxerInitSegmentArrival.bind(this),this._remuxer.onMediaSegment=this._onRemuxerMediaSegmentArrival.bind(this)},e.prototype._setupTSDemuxerRemuxer=function(e){var t=this._demuxer=new ve(e,this._config);this._remuxer||(this._remuxer=new Ce(this._config)),t.onError=this._onDemuxException.bind(this),t.onMediaInfo=this._onMediaInfo.bind(this),t.onMetaDataArrived=this._onMetaDataArrived.bind(this),t.onTimedID3Metadata=this._onTimedID3Metadata.bind(this),t.onSMPTE2038Metadata=this._onSMPTE2038Metadata.bind(this),t.onSCTE35Metadata=this._onSCTE35Metadata.bind(this),t.onPESPrivateDataDescriptor=this._onPESPrivateDataDescriptor.bind(this),t.onPESPrivateData=this._onPESPrivateData.bind(this),this._remuxer.bindDataSource(this._demuxer),this._demuxer.bindDataSource(this._ioctl),this._remuxer.onInitSegment=this._onRemuxerInitSegmentArrival.bind(this),this._remuxer.onMediaSegment=this._onRemuxerMediaSegmentArrival.bind(this)},e.prototype._onMediaInfo=function(e){var t=this;null==this._mediaInfo&&(this._mediaInfo=(0,f.default)({},e),this._mediaInfo.keyframesIndex=null,this._mediaInfo.segments=[],this._mediaInfo.segmentCount=this._mediaDataSource.segments.length,(0,p.default)(this._mediaInfo,u.a.prototype));var i=(0,f.default)({},e);(0,p.default)(i,u.a.prototype),this._mediaInfo.segments[this._currentSegmentIndex]=i,this._reportSegmentMediaInfo(this._currentSegmentIndex),null!=this._pendingSeekTime&&c.default.resolve().then(function(){var e=t._pendingSeekTime;t._pendingSeekTime=null,t.seek(e)})},e.prototype._onMetaDataArrived=function(e){this._emitter.emit(ke.a.METADATA_ARRIVED,e)},e.prototype._onScriptDataArrived=function(e){this._emitter.emit(ke.a.SCRIPTDATA_ARRIVED,e)},e.prototype._onTimedID3Metadata=function(e){var t=this._remuxer.getTimestampBase();null!=t&&(null!=e.pts&&(e.pts-=t),null!=e.dts&&(e.dts-=t),this._emitter.emit(ke.a.TIMED_ID3_METADATA_ARRIVED,e))},e.prototype._onSMPTE2038Metadata=function(e){var t=this._remuxer.getTimestampBase();null!=t&&(null!=e.pts&&(e.pts-=t),null!=e.dts&&(e.dts-=t),null!=e.nearest_pts&&(e.nearest_pts-=t),this._emitter.emit(ke.a.SMPTE2038_METADATA_ARRIVED,e))},e.prototype._onSCTE35Metadata=function(e){var t=this._remuxer.getTimestampBase();null!=t&&(null!=e.pts&&(e.pts-=t),null!=e.nearest_pts&&(e.nearest_pts-=t),this._emitter.emit(ke.a.SCTE35_METADATA_ARRIVED,e))},e.prototype._onPESPrivateDataDescriptor=function(e){this._emitter.emit(ke.a.PES_PRIVATE_DATA_DESCRIPTOR,e)},e.prototype._onPESPrivateData=function(e){var t=this._remuxer.getTimestampBase();null!=t&&(null!=e.pts&&(e.pts-=t),null!=e.nearest_pts&&(e.nearest_pts-=t),null!=e.dts&&(e.dts-=t),this._emitter.emit(ke.a.PES_PRIVATE_DATA_ARRIVED,e))},e.prototype._onIOSeeked=function(){this._remuxer.insertDiscontinuity()},e.prototype._onIOComplete=function(e){var t=e+1;t<this._mediaDataSource.segments.length?(this._internalAbort(),this._remuxer&&this._remuxer.flushStashedSamples(),this._loadSegment(t)):(this._remuxer&&this._remuxer.flushStashedSamples(),this._emitter.emit(ke.a.LOADING_COMPLETE),this._disableStatisticsReporter())},e.prototype._onIORedirect=function(e){var t=this._ioctl.extraData;this._mediaDataSource.segments[t].redirectedURL=e},e.prototype._onIORecoveredEarlyEof=function(){this._emitter.emit(ke.a.RECOVERED_EARLY_EOF)},e.prototype._onIOException=function(e,t){o.a.e(this.TAG,"IOException: type = "+e+", code = "+t.code+", msg = "+t.msg),this._emitter.emit(ke.a.IO_ERROR,e,t),this._disableStatisticsReporter()},e.prototype._onDemuxException=function(e,t){o.a.e(this.TAG,"DemuxException: type = "+e+", info = "+t),this._emitter.emit(ke.a.DEMUX_ERROR,e,t)},e.prototype._onRemuxerInitSegmentArrival=function(e,t){this._emitter.emit(ke.a.INIT_SEGMENT,e,t)},e.prototype._onRemuxerMediaSegmentArrival=function(e,t){if(null==this._pendingSeekTime&&(this._emitter.emit(ke.a.MEDIA_SEGMENT,e,t),null!=this._pendingResolveSeekPoint&&"video"===e)){var i=t.info.syncPoints,n=this._pendingResolveSeekPoint;this._pendingResolveSeekPoint=null,d.a.safari&&i.length>0&&i[0].originalDts===n&&(n=i[0].pts),this._emitter.emit(ke.a.RECOMMEND_SEEKPOINT,n)}},e.prototype._enableStatisticsReporter=function(){null==this._statisticsReporter&&(this._statisticsReporter=self.setInterval(this._reportStatisticsInfo.bind(this),this._config.statisticsInfoReportInterval))},e.prototype._disableStatisticsReporter=function(){this._statisticsReporter&&(self.clearInterval(this._statisticsReporter),this._statisticsReporter=null)},e.prototype._reportSegmentMediaInfo=function(e){var t=this._mediaInfo.segments[e],i=(0,f.default)({},t);i.duration=this._mediaInfo.duration,i.segmentCount=this._mediaInfo.segmentCount,delete i.segments,delete i.keyframesIndex,this._emitter.emit(ke.a.MEDIA_INFO,i)},e.prototype._reportStatisticsInfo=function(){var e={};e.url=this._ioctl.currentURL,e.hasRedirect=this._ioctl.hasRedirect,e.hasRedirect&&(e.redirectedURL=this._ioctl.currentRedirectedURL),e.speed=this._ioctl.currentSpeed,e.loaderType=this._ioctl.loaderType,e.currentSegmentIndex=this._currentSegmentIndex,e.totalSegmentCount=this._mediaDataSource.segments.length,this._emitter.emit(ke.a.STATISTICS_INFO,e)},e}();t.a=Te},function(e,t,i){var n,a=i(0),o=function(){function e(){this._firstCheckpoint=0,this._lastCheckpoint=0,this._intervalBytes=0,this._totalBytes=0,this._lastSecondBytes=0,self.performance&&self.performance.now?this._now=self.performance.now.bind(self.performance):this._now=Date.now}return e.prototype.reset=function(){this._firstCheckpoint=this._lastCheckpoint=0,this._totalBytes=this._intervalBytes=0,this._lastSecondBytes=0},e.prototype.addBytes=function(e){0===this._firstCheckpoint?(this._firstCheckpoint=this._now(),this._lastCheckpoint=this._firstCheckpoint,this._intervalBytes+=e,this._totalBytes+=e):this._now()-this._lastCheckpoint<1e3?(this._intervalBytes+=e,this._totalBytes+=e):(this._lastSecondBytes=this._intervalBytes,this._intervalBytes=e,this._totalBytes+=e,this._lastCheckpoint=this._now())},Object.defineProperty(e.prototype,"currentKBps",{get:function(){this.addBytes(0);var e=(this._now()-this._lastCheckpoint)/1e3;return 0==e&&(e=1),this._intervalBytes/e/1024},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"lastSecondKBps",{get:function(){return this.addBytes(0),0!==this._lastSecondBytes?this._lastSecondBytes/1024:this._now()-this._lastCheckpoint>=500?this.currentKBps:0},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"averageKBps",{get:function(){var e=(this._now()-this._firstCheckpoint)/1e3;return this._totalBytes/e/1024},enumerable:!1,configurable:!0}),e}(),s=i(2),r=i(4),l=i(3),d=(n=function(e,t){return(n=p.default||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var i in t)t.hasOwnProperty(i)&&(e[i]=t[i])})(e,t)},function(e,t){function i(){this.constructor=e}n(e,t),e.prototype=null===t?(0,m.default)(t):(i.prototype=t.prototype,new i)}),c=function(e){function t(t,i){var n=e.call(this,"fetch-stream-loader")||this;return n.TAG="FetchStreamLoader",n._seekHandler=t,n._config=i,n._needStash=!0,n._requestAbort=!1,n._abortController=null,n._contentLength=null,n._receivedLength=0,n}return d(t,e),t.isSupported=function(){try{var e=r.a.msedge&&r.a.version.minor>=15048,t=!r.a.msedge||e;return self.fetch&&self.ReadableStream&&t}catch(e){return!1}},t.prototype.destroy=function(){this.isWorking()&&this.abort(),e.prototype.destroy.call(this)},t.prototype.open=function(e,t){var i=this;this._dataSource=e,this._range=t;var n=e.url;this._config.reuseRedirectedURL&&null!=e.redirectedURL&&(n=e.redirectedURL);var a=this._seekHandler.getConfig(n,t),o=new self.Headers;if("object"==(0,b.default)(a.headers)){var r=a.headers;for(var d in r)r.hasOwnProperty(d)&&o.append(d,r[d])}var c={method:"GET",headers:o,mode:"cors",cache:"default",referrerPolicy:"no-referrer-when-downgrade"};if("object"==(0,b.default)(this._config.headers))for(var d in this._config.headers)o.append(d,this._config.headers[d]);!1===e.cors&&(c.mode="same-origin"),e.withCredentials&&(c.credentials="include"),e.referrerPolicy&&(c.referrerPolicy=e.referrerPolicy),self.AbortController&&(this._abortController=new self.AbortController,c.signal=this._abortController.signal),this._status=s.c.kConnecting,self.fetch(a.url,c).then(function(e){if(i._requestAbort)return i._status=s.c.kIdle,void e.body.cancel();if(e.ok&&e.status>=200&&e.status<=299){if(e.url!==a.url&&i._onURLRedirect){var t=i._seekHandler.removeURLParameters(e.url);i._onURLRedirect(t)}var n=e.headers.get("Content-Length");return null!=n&&(i._contentLength=parseInt(n),0!==i._contentLength&&i._onContentLengthKnown&&i._onContentLengthKnown(i._contentLength)),i._pump.call(i,e.body.getReader())}if(i._status=s.c.kError,!i._onError)throw new l.d("FetchStreamLoader: Http code invalid, "+e.status+" "+e.statusText);i._onError(s.b.HTTP_STATUS_CODE_INVALID,{code:e.status,msg:e.statusText})}).catch(function(e){if(!i._abortController||!i._abortController.signal.aborted){if(i._status=s.c.kError,!i._onError)throw e;i._onError(s.b.EXCEPTION,{code:-1,msg:e.message})}})},t.prototype.abort=function(){if(this._requestAbort=!0,(this._status!==s.c.kBuffering||!r.a.chrome)&&this._abortController)try{this._abortController.abort()}catch(e){}},t.prototype._pump=function(e){var t=this;return e.read().then(function(i){if(i.done)if(null!==t._contentLength&&t._receivedLength<t._contentLength){t._status=s.c.kError;var n=s.b.EARLY_EOF,a={code:-1,msg:"Fetch stream meet Early-EOF"};if(!t._onError)throw new l.d(a.msg);t._onError(n,a)}else t._status=s.c.kComplete,t._onComplete&&t._onComplete(t._range.from,t._range.from+t._receivedLength-1);else{if(t._abortController&&t._abortController.signal.aborted)return void(t._status=s.c.kComplete);if(!0===t._requestAbort)return t._status=s.c.kComplete,e.cancel();t._status=s.c.kBuffering;var o=i.value.buffer,r=t._range.from+t._receivedLength;t._receivedLength+=o.byteLength,t._onDataArrival&&t._onDataArrival(o,r,t._receivedLength),t._pump(e)}}).catch(function(e){if(t._abortController&&t._abortController.signal.aborted)t._status=s.c.kComplete;else if(11!==e.code||!r.a.msedge){t._status=s.c.kError;var i=0,n=null;if(19!==e.code&&"network error"!==e.message||!(null===t._contentLength||null!==t._contentLength&&t._receivedLength<t._contentLength)?(i=s.b.EXCEPTION,n={code:e.code,msg:e.message}):(i=s.b.EARLY_EOF,n={code:e.code,msg:"Fetch stream meet Early-EOF"}),!t._onError)throw new l.d(n.msg);t._onError(i,n)}})},t}(s.a),u=function(){var e=function(t,i){return(e=p.default||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var i in t)t.hasOwnProperty(i)&&(e[i]=t[i])})(t,i)};return function(t,i){function n(){this.constructor=t}e(t,i),t.prototype=null===i?(0,m.default)(i):(n.prototype=i.prototype,new n)}}(),h=function(e){function t(t,i){var n=e.call(this,"xhr-moz-chunked-loader")||this;return n.TAG="MozChunkedLoader",n._seekHandler=t,n._config=i,n._needStash=!0,n._xhr=null,n._requestAbort=!1,n._contentLength=null,n._receivedLength=0,n}return u(t,e),t.isSupported=function(){try{var e=new XMLHttpRequest;return e.open("GET","https://example.com",!0),e.responseType="moz-chunked-arraybuffer","moz-chunked-arraybuffer"===e.responseType}catch(e){return a.a.w("MozChunkedLoader",e.message),!1}},t.prototype.destroy=function(){this.isWorking()&&this.abort(),this._xhr&&(this._xhr.onreadystatechange=null,this._xhr.onprogress=null,this._xhr.onloadend=null,this._xhr.onerror=null,this._xhr=null),e.prototype.destroy.call(this)},t.prototype.open=function(e,t){this._dataSource=e,this._range=t;var i=e.url;this._config.reuseRedirectedURL&&null!=e.redirectedURL&&(i=e.redirectedURL);var n=this._seekHandler.getConfig(i,t);this._requestURL=n.url;var a=this._xhr=new XMLHttpRequest;if(a.open("GET",n.url,!0),a.responseType="moz-chunked-arraybuffer",a.onreadystatechange=this._onReadyStateChange.bind(this),a.onprogress=this._onProgress.bind(this),a.onloadend=this._onLoadEnd.bind(this),a.onerror=this._onXhrError.bind(this),e.withCredentials&&(a.withCredentials=!0),"object"==(0,b.default)(n.headers)){var o=n.headers;for(var r in o)o.hasOwnProperty(r)&&a.setRequestHeader(r,o[r])}if("object"==(0,b.default)(this._config.headers))for(var r in o=this._config.headers)o.hasOwnProperty(r)&&a.setRequestHeader(r,o[r]);this._status=s.c.kConnecting,a.send()},t.prototype.abort=function(){this._requestAbort=!0,this._xhr&&this._xhr.abort(),this._status=s.c.kComplete},t.prototype._onReadyStateChange=function(e){var t=e.target;if(2===t.readyState){if(null!=t.responseURL&&t.responseURL!==this._requestURL&&this._onURLRedirect){var i=this._seekHandler.removeURLParameters(t.responseURL);this._onURLRedirect(i)}if(0!==t.status&&(t.status<200||t.status>299)){if(this._status=s.c.kError,!this._onError)throw new l.d("MozChunkedLoader: Http code invalid, "+t.status+" "+t.statusText);this._onError(s.b.HTTP_STATUS_CODE_INVALID,{code:t.status,msg:t.statusText})}else this._status=s.c.kBuffering}},t.prototype._onProgress=function(e){if(this._status!==s.c.kError){null===this._contentLength&&null!==e.total&&0!==e.total&&(this._contentLength=e.total,this._onContentLengthKnown&&this._onContentLengthKnown(this._contentLength));var t=e.target.response,i=this._range.from+this._receivedLength;this._receivedLength+=t.byteLength,this._onDataArrival&&this._onDataArrival(t,i,this._receivedLength)}},t.prototype._onLoadEnd=function(e){!0!==this._requestAbort?this._status!==s.c.kError&&(this._status=s.c.kComplete,this._onComplete&&this._onComplete(this._range.from,this._range.from+this._receivedLength-1)):this._requestAbort=!1},t.prototype._onXhrError=function(e){this._status=s.c.kError;var t=0,i=null;if(this._contentLength&&e.loaded<this._contentLength?(t=s.b.EARLY_EOF,i={code:-1,msg:"Moz-Chunked stream meet Early-Eof"}):(t=s.b.EXCEPTION,i={code:-1,msg:e.constructor.name+" "+e.type}),!this._onError)throw new l.d(i.msg);this._onError(t,i)},t}(s.a),_=function(){var e=function(t,i){return(e=p.default||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var i in t)t.hasOwnProperty(i)&&(e[i]=t[i])})(t,i)};return function(t,i){function n(){this.constructor=t}e(t,i),t.prototype=null===i?(0,m.default)(i):(n.prototype=i.prototype,new n)}}(),g=function(e){function t(t,i){var n=e.call(this,"xhr-range-loader")||this;return n.TAG="RangeLoader",n._seekHandler=t,n._config=i,n._needStash=!1,n._chunkSizeKBList=[128,256,384,512,768,1024,1536,2048,3072,4096,5120,6144,7168,8192],n._currentChunkSizeKB=384,n._currentSpeedNormalized=0,n._zeroSpeedChunkCount=0,n._xhr=null,n._speedSampler=new o,n._requestAbort=!1,n._waitForTotalLength=!1,n._totalLengthReceived=!1,n._currentRequestURL=null,n._currentRedirectedURL=null,n._currentRequestRange=null,n._totalLength=null,n._contentLength=null,n._receivedLength=0,n._lastTimeLoaded=0,n}return _(t,e),t.isSupported=function(){try{var e=new XMLHttpRequest;return e.open("GET","https://example.com",!0),e.responseType="arraybuffer","arraybuffer"===e.responseType}catch(e){return a.a.w("RangeLoader",e.message),!1}},t.prototype.destroy=function(){this.isWorking()&&this.abort(),this._xhr&&(this._xhr.onreadystatechange=null,this._xhr.onprogress=null,this._xhr.onload=null,this._xhr.onerror=null,this._xhr=null),e.prototype.destroy.call(this)},Object.defineProperty(t.prototype,"currentSpeed",{get:function(){return this._speedSampler.lastSecondKBps},enumerable:!1,configurable:!0}),t.prototype.open=function(e,t){this._dataSource=e,this._range=t,this._status=s.c.kConnecting;var i=!1;null!=this._dataSource.filesize&&0!==this._dataSource.filesize&&(i=!0,this._totalLength=this._dataSource.filesize),this._totalLengthReceived||i?this._openSubRange():(this._waitForTotalLength=!0,this._internalOpen(this._dataSource,{from:0,to:-1}))},t.prototype._openSubRange=function(){var e=1024*this._currentChunkSizeKB,t=this._range.from+this._receivedLength,i=t+e;null!=this._contentLength&&i-this._range.from>=this._contentLength&&(i=this._range.from+this._contentLength-1),this._currentRequestRange={from:t,to:i},this._internalOpen(this._dataSource,this._currentRequestRange)},t.prototype._internalOpen=function(e,t){this._lastTimeLoaded=0;var i=e.url;this._config.reuseRedirectedURL&&(null!=this._currentRedirectedURL?i=this._currentRedirectedURL:null!=e.redirectedURL&&(i=e.redirectedURL));var n=this._seekHandler.getConfig(i,t);this._currentRequestURL=n.url;var a=this._xhr=new XMLHttpRequest;if(a.open("GET",n.url,!0),a.responseType="arraybuffer",a.onreadystatechange=this._onReadyStateChange.bind(this),a.onprogress=this._onProgress.bind(this),a.onload=this._onLoad.bind(this),a.onerror=this._onXhrError.bind(this),e.withCredentials&&(a.withCredentials=!0),"object"==(0,b.default)(n.headers)){var o=n.headers;for(var s in o)o.hasOwnProperty(s)&&a.setRequestHeader(s,o[s])}if("object"==(0,b.default)(this._config.headers))for(var s in o=this._config.headers)o.hasOwnProperty(s)&&a.setRequestHeader(s,o[s]);a.send()},t.prototype.abort=function(){this._requestAbort=!0,this._internalAbort(),this._status=s.c.kComplete},t.prototype._internalAbort=function(){this._xhr&&(this._xhr.onreadystatechange=null,this._xhr.onprogress=null,this._xhr.onload=null,this._xhr.onerror=null,this._xhr.abort(),this._xhr=null)},t.prototype._onReadyStateChange=function(e){var t=e.target;if(2===t.readyState){if(null!=t.responseURL){var i=this._seekHandler.removeURLParameters(t.responseURL);t.responseURL!==this._currentRequestURL&&i!==this._currentRedirectedURL&&(this._currentRedirectedURL=i,this._onURLRedirect&&this._onURLRedirect(i))}if(t.status>=200&&t.status<=299){if(this._waitForTotalLength)return;this._status=s.c.kBuffering}else{if(this._status=s.c.kError,!this._onError)throw new l.d("RangeLoader: Http code invalid, "+t.status+" "+t.statusText);this._onError(s.b.HTTP_STATUS_CODE_INVALID,{code:t.status,msg:t.statusText})}}},t.prototype._onProgress=function(e){if(this._status!==s.c.kError){if(null===this._contentLength){var t=!1;if(this._waitForTotalLength){this._waitForTotalLength=!1,this._totalLengthReceived=!0,t=!0;var i=e.total;this._internalAbort(),null!=i&0!==i&&(this._totalLength=i)}if(-1===this._range.to?this._contentLength=this._totalLength-this._range.from:this._contentLength=this._range.to-this._range.from+1,t)return void this._openSubRange();this._onContentLengthKnown&&this._onContentLengthKnown(this._contentLength)}var n=e.loaded-this._lastTimeLoaded;this._lastTimeLoaded=e.loaded,this._speedSampler.addBytes(n)}},t.prototype._normalizeSpeed=function(e){var t=this._chunkSizeKBList,i=t.length-1,n=0,a=0,o=i;if(e<t[0])return t[0];for(;a<=o;){if((n=a+Math.floor((o-a)/2))===i||e>=t[n]&&e<t[n+1])return t[n];t[n]<e?a=n+1:o=n-1}},t.prototype._onLoad=function(e){if(this._status!==s.c.kError)if(this._waitForTotalLength)this._waitForTotalLength=!1;else{this._lastTimeLoaded=0;var t=this._speedSampler.lastSecondKBps;if(0===t&&(this._zeroSpeedChunkCount++,this._zeroSpeedChunkCount>=3&&(t=this._speedSampler.currentKBps)),0!==t){var i=this._normalizeSpeed(t);this._currentSpeedNormalized!==i&&(this._currentSpeedNormalized=i,this._currentChunkSizeKB=i)}var n=e.target.response,a=this._range.from+this._receivedLength;this._receivedLength+=n.byteLength;var o=!1;null!=this._contentLength&&this._receivedLength<this._contentLength?this._openSubRange():o=!0,this._onDataArrival&&this._onDataArrival(n,a,this._receivedLength),o&&(this._status=s.c.kComplete,this._onComplete&&this._onComplete(this._range.from,this._range.from+this._receivedLength-1))}},t.prototype._onXhrError=function(e){this._status=s.c.kError;var t=0,i=null;if(this._contentLength&&this._receivedLength>0&&this._receivedLength<this._contentLength?(t=s.b.EARLY_EOF,i={code:-1,msg:"RangeLoader meet Early-Eof"}):(t=s.b.EXCEPTION,i={code:-1,msg:e.constructor.name+" "+e.type}),!this._onError)throw new l.d(i.msg);this._onError(t,i)},t}(s.a),v=function(){var e=function(t,i){return(e=p.default||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var i in t)t.hasOwnProperty(i)&&(e[i]=t[i])})(t,i)};return function(t,i){function n(){this.constructor=t}e(t,i),t.prototype=null===i?(0,m.default)(i):(n.prototype=i.prototype,new n)}}(),y=function(e){function t(){var t=e.call(this,"websocket-loader")||this;return t.TAG="WebSocketLoader",t._needStash=!0,t._ws=null,t._requestAbort=!1,t._receivedLength=0,t}return v(t,e),t.isSupported=function(){try{return void 0!==self.WebSocket}catch(e){return!1}},t.prototype.destroy=function(){this._ws&&this.abort(),e.prototype.destroy.call(this)},t.prototype.open=function(e){try{var t=this._ws=new self.WebSocket(e.url);t.binaryType="arraybuffer",t.onopen=this._onWebSocketOpen.bind(this),t.onclose=this._onWebSocketClose.bind(this),t.onmessage=this._onWebSocketMessage.bind(this),t.onerror=this._onWebSocketError.bind(this),this._status=s.c.kConnecting}catch(e){this._status=s.c.kError;var i={code:e.code,msg:e.message};if(!this._onError)throw new l.d(i.msg);this._onError(s.b.EXCEPTION,i)}},t.prototype.abort=function(){var e=this._ws;!e||0!==e.readyState&&1!==e.readyState||(this._requestAbort=!0,e.close()),this._ws=null,this._status=s.c.kComplete},t.prototype._onWebSocketOpen=function(e){this._status=s.c.kBuffering},t.prototype._onWebSocketClose=function(e){!0!==this._requestAbort?(this._status=s.c.kComplete,this._onComplete&&this._onComplete(0,this._receivedLength-1)):this._requestAbort=!1},t.prototype._onWebSocketMessage=function(e){var t=this;if(e.data instanceof ArrayBuffer)this._dispatchArrayBuffer(e.data);else if(e.data instanceof Blob){var i=new FileReader;i.onload=function(){t._dispatchArrayBuffer(i.result)},i.readAsArrayBuffer(e.data)}else{this._status=s.c.kError;var n={code:-1,msg:"Unsupported WebSocket message type: "+e.data.constructor.name};if(!this._onError)throw new l.d(n.msg);this._onError(s.b.EXCEPTION,n)}},t.prototype._dispatchArrayBuffer=function(e){var t=e,i=this._receivedLength;this._receivedLength+=t.byteLength,this._onDataArrival&&this._onDataArrival(t,i,this._receivedLength)},t.prototype._onWebSocketError=function(e){this._status=s.c.kError;var t={code:e.code,msg:e.message};if(!this._onError)throw new l.d(t.msg);this._onError(s.b.EXCEPTION,t)},t}(s.a),S=function(){function e(e){this._zeroStart=e||!1}return e.prototype.getConfig=function(e,t){var i={};if(0!==t.from||-1!==t.to){var n;n=-1!==t.to?"bytes="+t.from.toString()+"-"+t.to.toString():"bytes="+t.from.toString()+"-",i.Range=n}else this._zeroStart&&(i.Range="bytes=0-");return{url:e,headers:i}},e.prototype.removeURLParameters=function(e){return e},e}(),w=function(){function e(e,t){this._startName=e,this._endName=t}return e.prototype.getConfig=function(e,t){var i=e;if(0!==t.from||-1!==t.to){var n=!0;-1===i.indexOf("?")&&(i+="?",n=!1),n&&(i+="&"),i+=this._startName+"="+t.from.toString(),-1!==t.to&&(i+="&"+this._endName+"="+t.to.toString())}return{url:i,headers:{}}},e.prototype.removeURLParameters=function(e){var t=e.split("?")[0],i=void 0,n=e.indexOf("?");-1!==n&&(i=e.substring(n+1));var a="";if(null!=i&&i.length>0)for(var o=i.split("&"),s=0;s<o.length;s++){var r=o[s].split("="),l=s>0;r[0]!==this._startName&&r[0]!==this._endName&&(l&&(a+="&"),a+=o[s])}return 0===a.length?t:t+"?"+a},e}(),C=function(){function e(e,t,i){this.TAG="IOController",this._config=t,this._extraData=i,this._stashInitialSize=65536,null!=t.stashInitialSize&&t.stashInitialSize>0&&(this._stashInitialSize=t.stashInitialSize),this._stashUsed=0,this._stashSize=this._stashInitialSize,this._bufferSize=3145728,this._stashBuffer=new ArrayBuffer(this._bufferSize),this._stashByteStart=0,this._enableStash=!0,!1===t.enableStashBuffer&&(this._enableStash=!1),this._loader=null,this._loaderClass=null,this._seekHandler=null,this._dataSource=e,this._isWebSocketURL=/wss?:\/\/(.+?)/.test(e.url),this._refTotalLength=e.filesize?e.filesize:null,this._totalLength=this._refTotalLength,this._fullRequestFlag=!1,this._currentRange=null,this._redirectedURL=null,this._speedNormalized=0,this._speedSampler=new o,this._speedNormalizeList=[32,64,96,128,192,256,384,512,768,1024,1536,2048,3072,4096],this._isEarlyEofReconnecting=!1,this._paused=!1,this._resumeFrom=0,this._onDataArrival=null,this._onSeeked=null,this._onError=null,this._onComplete=null,this._onRedirect=null,this._onRecoveredEarlyEof=null,this._selectSeekHandler(),this._selectLoader(),this._createLoader()}return e.prototype.destroy=function(){this._loader.isWorking()&&this._loader.abort(),this._loader.destroy(),this._loader=null,this._loaderClass=null,this._dataSource=null,this._stashBuffer=null,this._stashUsed=this._stashSize=this._bufferSize=this._stashByteStart=0,this._currentRange=null,this._speedSampler=null,this._isEarlyEofReconnecting=!1,this._onDataArrival=null,this._onSeeked=null,this._onError=null,this._onComplete=null,this._onRedirect=null,this._onRecoveredEarlyEof=null,this._extraData=null},e.prototype.isWorking=function(){return this._loader&&this._loader.isWorking()&&!this._paused},e.prototype.isPaused=function(){return this._paused},Object.defineProperty(e.prototype,"status",{get:function(){return this._loader.status},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"extraData",{get:function(){return this._extraData},set:function(e){this._extraData=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"onDataArrival",{get:function(){return this._onDataArrival},set:function(e){this._onDataArrival=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"onSeeked",{get:function(){return this._onSeeked},set:function(e){this._onSeeked=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"onError",{get:function(){return this._onError},set:function(e){this._onError=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"onComplete",{get:function(){return this._onComplete},set:function(e){this._onComplete=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"onRedirect",{get:function(){return this._onRedirect},set:function(e){this._onRedirect=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"onRecoveredEarlyEof",{get:function(){return this._onRecoveredEarlyEof},set:function(e){this._onRecoveredEarlyEof=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"currentURL",{get:function(){return this._dataSource.url},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"hasRedirect",{get:function(){return null!=this._redirectedURL||null!=this._dataSource.redirectedURL},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"currentRedirectedURL",{get:function(){return this._redirectedURL||this._dataSource.redirectedURL},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"currentSpeed",{get:function(){return this._loaderClass===g?this._loader.currentSpeed:this._speedSampler.lastSecondKBps},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"loaderType",{get:function(){return this._loader.type},enumerable:!1,configurable:!0}),e.prototype._selectSeekHandler=function(){var e=this._config;if("range"===e.seekType)this._seekHandler=new S(this._config.rangeLoadZeroStart);else if("param"===e.seekType){var t=e.seekParamStart||"bstart",i=e.seekParamEnd||"bend";this._seekHandler=new w(t,i)}else{if("custom"!==e.seekType)throw new l.b("Invalid seekType in config: "+e.seekType);if("function"!=typeof e.customSeekHandler)throw new l.b("Custom seekType specified in config but invalid customSeekHandler!");this._seekHandler=new e.customSeekHandler}},e.prototype._selectLoader=function(){if(null!=this._config.customLoader)this._loaderClass=this._config.customLoader;else if(this._isWebSocketURL)this._loaderClass=y;else if(c.isSupported())this._loaderClass=c;else if(h.isSupported())this._loaderClass=h;else{if(!g.isSupported())throw new l.d("Your browser doesn't support xhr with arraybuffer responseType!");this._loaderClass=g}},e.prototype._createLoader=function(){this._loader=new this._loaderClass(this._seekHandler,this._config),!1===this._loader.needStashBuffer&&(this._enableStash=!1),this._loader.onContentLengthKnown=this._onContentLengthKnown.bind(this),this._loader.onURLRedirect=this._onURLRedirect.bind(this),this._loader.onDataArrival=this._onLoaderChunkArrival.bind(this),this._loader.onComplete=this._onLoaderComplete.bind(this),this._loader.onError=this._onLoaderError.bind(this)},e.prototype.open=function(e){this._currentRange={from:0,to:-1},e&&(this._currentRange.from=e),this._speedSampler.reset(),e||(this._fullRequestFlag=!0),this._loader.open(this._dataSource,(0,f.default)({},this._currentRange))},e.prototype.abort=function(){this._loader.abort(),this._paused&&(this._paused=!1,this._resumeFrom=0)},e.prototype.pause=function(){this.isWorking()&&(this._loader.abort(),0!==this._stashUsed?(this._resumeFrom=this._stashByteStart,this._currentRange.to=this._stashByteStart-1):this._resumeFrom=this._currentRange.to+1,this._stashUsed=0,this._stashByteStart=0,this._paused=!0)},e.prototype.resume=function(){if(this._paused){this._paused=!1;var e=this._resumeFrom;this._resumeFrom=0,this._internalSeek(e,!0)}},e.prototype.seek=function(e){this._paused=!1,this._stashUsed=0,this._stashByteStart=0,this._internalSeek(e,!0)},e.prototype._internalSeek=function(e,t){this._loader.isWorking()&&this._loader.abort(),this._flushStashBuffer(t),this._loader.destroy(),this._loader=null;var i={from:e,to:-1};this._currentRange={from:i.from,to:-1},this._speedSampler.reset(),this._stashSize=this._stashInitialSize,this._createLoader(),this._loader.open(this._dataSource,i),this._onSeeked&&this._onSeeked()},e.prototype.updateUrl=function(e){if(!e||"string"!=typeof e||0===e.length)throw new l.b("Url must be a non-empty string!");this._dataSource.url=e},e.prototype._expandBuffer=function(e){for(var t=this._stashSize;t+1048576<e;)t*=2;if((t+=1048576)!==this._bufferSize){var i=new ArrayBuffer(t);if(this._stashUsed>0){var n=new Uint8Array(this._stashBuffer,0,this._stashUsed);new Uint8Array(i,0,t).set(n,0)}this._stashBuffer=i,this._bufferSize=t}},e.prototype._normalizeSpeed=function(e){var t=this._speedNormalizeList,i=t.length-1,n=0,a=0,o=i;if(e<t[0])return t[0];for(;a<=o;){if((n=a+Math.floor((o-a)/2))===i||e>=t[n]&&e<t[n+1])return t[n];t[n]<e?a=n+1:o=n-1}},e.prototype._adjustStashSize=function(e){var t=0;(t=this._config.isLive?e/8:e<512?e:e>=512&&e<=1024?Math.floor(1.5*e):2*e)>8192&&(t=8192);var i=1024*t+1048576;this._bufferSize<i&&this._expandBuffer(i),this._stashSize=1024*t},e.prototype._dispatchChunks=function(e,t){return this._currentRange.to=t+e.byteLength-1,this._onDataArrival(e,t)},e.prototype._onURLRedirect=function(e){this._redirectedURL=e,this._onRedirect&&this._onRedirect(e)},e.prototype._onContentLengthKnown=function(e){e&&this._fullRequestFlag&&(this._totalLength=e,this._fullRequestFlag=!1)},e.prototype._onLoaderChunkArrival=function(e,t,i){if(!this._onDataArrival)throw new l.a("IOController: No existing consumer (onDataArrival) callback!");if(!this._paused){this._isEarlyEofReconnecting&&(this._isEarlyEofReconnecting=!1,this._onRecoveredEarlyEof&&this._onRecoveredEarlyEof()),this._speedSampler.addBytes(e.byteLength);var n=this._speedSampler.lastSecondKBps;if(0!==n){var a=this._normalizeSpeed(n);this._speedNormalized!==a&&(this._speedNormalized=a,this._adjustStashSize(a))}if(this._enableStash)if(0===this._stashUsed&&0===this._stashByteStart&&(this._stashByteStart=t),this._stashUsed+e.byteLength<=this._stashSize)(r=new Uint8Array(this._stashBuffer,0,this._stashSize)).set(new Uint8Array(e),this._stashUsed),this._stashUsed+=e.byteLength;else if(r=new Uint8Array(this._stashBuffer,0,this._bufferSize),this._stashUsed>0){var o=this._stashBuffer.slice(0,this._stashUsed);(d=this._dispatchChunks(o,this._stashByteStart))<o.byteLength?d>0&&(c=new Uint8Array(o,d),r.set(c,0),this._stashUsed=c.byteLength,this._stashByteStart+=d):(this._stashUsed=0,this._stashByteStart+=d),this._stashUsed+e.byteLength>this._bufferSize&&(this._expandBuffer(this._stashUsed+e.byteLength),r=new Uint8Array(this._stashBuffer,0,this._bufferSize)),r.set(new Uint8Array(e),this._stashUsed),this._stashUsed+=e.byteLength}else(d=this._dispatchChunks(e,t))<e.byteLength&&((s=e.byteLength-d)>this._bufferSize&&(this._expandBuffer(s),r=new Uint8Array(this._stashBuffer,0,this._bufferSize)),r.set(new Uint8Array(e,d),0),this._stashUsed+=s,this._stashByteStart=t+d);else if(0===this._stashUsed){var s;(d=this._dispatchChunks(e,t))<e.byteLength&&((s=e.byteLength-d)>this._bufferSize&&this._expandBuffer(s),(r=new Uint8Array(this._stashBuffer,0,this._bufferSize)).set(new Uint8Array(e,d),0),this._stashUsed+=s,this._stashByteStart=t+d)}else{var r,d;if(this._stashUsed+e.byteLength>this._bufferSize&&this._expandBuffer(this._stashUsed+e.byteLength),(r=new Uint8Array(this._stashBuffer,0,this._bufferSize)).set(new Uint8Array(e),this._stashUsed),this._stashUsed+=e.byteLength,(d=this._dispatchChunks(this._stashBuffer.slice(0,this._stashUsed),this._stashByteStart))<this._stashUsed&&d>0){var c=new Uint8Array(this._stashBuffer,d);r.set(c,0)}this._stashUsed-=d,this._stashByteStart+=d}}},e.prototype._flushStashBuffer=function(e){if(this._stashUsed>0){var t=this._stashBuffer.slice(0,this._stashUsed),i=this._dispatchChunks(t,this._stashByteStart),n=t.byteLength-i;if(i<t.byteLength){if(!e){if(i>0){var o=new Uint8Array(this._stashBuffer,0,this._bufferSize),s=new Uint8Array(t,i);o.set(s,0),this._stashUsed=s.byteLength,this._stashByteStart+=i}return 0}a.a.w(this.TAG,n+" bytes unconsumed data remain when flush buffer, dropped")}return this._stashUsed=0,this._stashByteStart=0,n}return 0},e.prototype._onLoaderComplete=function(e,t){this._flushStashBuffer(!0),this._onComplete&&this._onComplete(this._extraData)},e.prototype._onLoaderError=function(e,t){switch(a.a.e(this.TAG,"Loader error, code = "+t.code+", msg = "+t.msg),this._flushStashBuffer(!1),this._isEarlyEofReconnecting&&(this._isEarlyEofReconnecting=!1,e=s.b.UNRECOVERABLE_EARLY_EOF),e){case s.b.EARLY_EOF:if(!this._config.isLive&&this._totalLength){var i=this._currentRange.to+1;return void(i<this._totalLength&&(a.a.w(this.TAG,"Connection lost, trying reconnect..."),this._isEarlyEofReconnecting=!0,this._internalSeek(i,!1)))}e=s.b.UNRECOVERABLE_EARLY_EOF;break;case s.b.UNRECOVERABLE_EARLY_EOF:case s.b.CONNECTING_TIMEOUT:case s.b.HTTP_STATUS_CODE_INVALID:case s.b.EXCEPTION:}if(!this._onError)throw new l.d("IOException: "+t.msg);this._onError(e,t)},e}();t.a=C},function(e,t,i){var n=function(){function e(){}return e.install=function(){Object.setPrototypeOf=p.default||function(e,t){return e.__proto__=t,e},Object.assign=f.default||function(e){if(null==e)throw new TypeError("Cannot convert undefined or null to object");for(var t=Object(e),i=1;i<arguments.length;i++){var n=arguments[i];if(null!=n)for(var a in n)n.hasOwnProperty(a)&&(t[a]=n[a])}return t},"function"!=typeof self.Promise&&i(15).polyfill()},e}();n.install(),t.a=n},function(e,t,i){function n(e){var t={};function i(n){if(t[n])return t[n].exports;var a=t[n]={i:n,l:!1,exports:{}};return e[n].call(a.exports,a,a.exports,i),a.l=!0,a.exports}i.m=e,i.c=t,i.i=function(e){return e},i.d=function(e,t,n){i.o(e,t)||(0,y.default)(e,t,{configurable:!1,enumerable:!0,get:n})},i.r=function(e){Object.defineProperty(e,"__esModule",{value:!0})},i.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return i.d(t,"a",t),t},i.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},i.p="/",i.oe=function(e){throw console.error(e),e};var n=i(i.s=ENTRY_MODULE);return n.default||n}function a(e){return(e+"").replace(/[.?*+^$[\]\\(){}|-]/g,"\\$&")}function o(e,t,n){var o={};o[n]=[];var s=t.toString(),r=s.match(/^function\s?\w*\(\w+,\s*\w+,\s*(\w+)\)/);if(!r)return o;for(var d,c=r[1],u=new RegExp("(\\\\n|\\W)"+a(c)+"\\(\\s*(/\\*.*?\\*/)?\\s*.*?([\\.|\\-|\\+|\\w|/|@]+).*?\\)","g");d=u.exec(s);)"dll-reference"!==d[3]&&o[n].push(d[3]);for(u=new RegExp("\\("+a(c)+'\\("(dll-reference\\s([\\.|\\-|\\+|\\w|/|@]+))"\\)\\)\\(\\s*(/\\*.*?\\*/)?\\s*.*?([\\.|\\-|\\+|\\w|/|@]+).*?\\)',"g");d=u.exec(s);)e[d[2]]||(o[n].push(d[1]),e[d[2]]=i(d[1]).m),o[d[2]]=o[d[2]]||[],o[d[2]].push(d[4]);for(var h,_=(0,l.default)(o),f=0;f<_.length;f++)for(var p=0;p<o[_[f]].length;p++)h=o[_[f]][p],isNaN(1*h)||(o[_[f]][p]=1*o[_[f]][p]);return o}function s(e){return(0,l.default)(e).reduce(function(t,i){return t||e[i].length>0},!1)}e.exports=function(e,t){t=t||{};var a={main:i.m},d=t.all?{main:(0,l.default)(a.main)}:function(e,t){for(var i={main:[t]},n={main:[]},a={main:{}};s(i);)for(var r=(0,l.default)(i),d=0;d<r.length;d++){var c=r[d],u=i[c].pop();if(a[c]=a[c]||{},!a[c][u]&&e[c][u]){a[c][u]=!0,n[c]=n[c]||[],n[c].push(u);for(var h=o(e,e[c][u],c),_=(0,l.default)(h),f=0;f<_.length;f++)i[_[f]]=i[_[f]]||[],i[_[f]]=i[_[f]].concat(h[_[f]])}}return n}(a,e),c="";(0,l.default)(d).filter(function(e){return"main"!==e}).forEach(function(e){for(var t=0;d[e][t];)t++;d[e].push(t),a[e][t]="(function(module, exports, __webpack_require__) { module.exports = __webpack_require__; })",c=c+"var "+e+" = ("+n.toString().replace("ENTRY_MODULE",(0,r.default)(t))+")({"+d[e].map(function(t){return(0,r.default)(t)+": "+a[e][t].toString()}).join(",")+"});\n"}),c=c+"new (("+n.toString().replace("ENTRY_MODULE",(0,r.default)(e))+")({"+d.main.map(function(e){return(0,r.default)(e)+": "+a.main[e].toString()}).join(",")+"}))(self);";var u=new window.Blob([c],{type:"text/javascript"});if(t.bare)return u;var h=(window.URL||window.webkitURL||window.mozURL||window.msURL).createObjectURL(u),_=new window.Worker(h);return _.objectURL=h,_}},function(e,t,i){e.exports=i(19).default},function(e,t,i){(function(t,i){
/*!
       * @overview es6-promise - a tiny implementation of Promises/A+.
       * @copyright Copyright (c) 2014 Yehuda Katz, Tom Dale, Stefan Penner and contributors (Conversion to ES6 API by Jake Archibald)
       * @license   Licensed under MIT license
       *            See https://raw.githubusercontent.com/stefanpenner/es6-promise/master/LICENSE
       * @version   v4.2.8+1e68dce6
       */
var n;n=function(){function e(e){return"function"==typeof e}var n=Array.isArray?Array.isArray:function(e){return"[object Array]"===Object.prototype.toString.call(e)},a=0,o=void 0,s=void 0,r=function(e,t){f[a]=e,f[a+1]=t,2===(a+=2)&&(s?s(p):S())},l="undefined"!=typeof window?window:void 0,d=l||{},c=d.MutationObserver||d.WebKitMutationObserver,u="undefined"==typeof self&&void 0!==t&&"[object process]"==={}.toString.call(t),h="undefined"!=typeof Uint8ClampedArray&&"undefined"!=typeof importScripts&&"undefined"!=typeof MessageChannel;function _(){var e=setTimeout;return function(){return e(p,1)}}var f=new Array(1e3);function p(){for(var e=0;e<a;e+=2)(0,f[e])(f[e+1]),f[e]=void 0,f[e+1]=void 0;a=0}var m,g,v,y,S=void 0;function w(e,t){var i=this,n=new this.constructor(k);void 0===n[E]&&B(n);var a=i._state;if(a){var o=arguments[a-1];r(function(){return I(a,n,o,i._result)})}else P(i,n,e,t);return n}function C(e){if(e&&"object"==(void 0===e?"undefined":(0,b.default)(e))&&e.constructor===this)return e;var t=new this(k);return A(t,e),t}u?S=function(){return t.nextTick(p)}:c?(g=0,v=new c(p),y=document.createTextNode(""),v.observe(y,{characterData:!0}),S=function(){y.data=g=++g%2}):h?((m=new MessageChannel).port1.onmessage=p,S=function(){return m.port2.postMessage(0)}):S=void 0===l?function(){try{var e=Function("return this")().require("vertx");return void 0!==(o=e.runOnLoop||e.runOnContext)?function(){o(p)}:_()}catch(e){return _()}}():_();var E=Math.random().toString(36).substring(2);function k(){}function T(t,i,n){i.constructor===t.constructor&&n===w&&i.constructor.resolve===C?function(e,t){1===t._state?L(e,t._result):2===t._state?R(e,t._result):P(t,void 0,function(t){return A(e,t)},function(t){return R(e,t)})}(t,i):void 0===n?L(t,i):e(n)?function(e,t,i){r(function(e){var n=!1,a=function(e,t,i,n){try{e.call(t,i,n)}catch(e){return e}}(i,t,function(i){n||(n=!0,t!==i?A(e,i):L(e,i))},function(t){n||(n=!0,R(e,t))},e._label);!n&&a&&(n=!0,R(e,a))},e)}(t,i,n):L(t,i)}function A(e,t){if(e===t)R(e,new TypeError("You cannot resolve a promise with itself"));else if(a=(0,b.default)(n=t),null===n||"object"!==a&&"function"!==a)L(e,t);else{var i=void 0;try{i=t.then}catch(t){return void R(e,t)}T(e,t,i)}var n,a}function D(e){e._onerror&&e._onerror(e._result),x(e)}function L(e,t){void 0===e._state&&(e._result=t,e._state=1,0!==e._subscribers.length&&r(x,e))}function R(e,t){void 0===e._state&&(e._state=2,e._result=t,r(D,e))}function P(e,t,i,n){var a=e._subscribers,o=a.length;e._onerror=null,a[o]=t,a[o+1]=i,a[o+2]=n,0===o&&e._state&&r(x,e)}function x(e){var t=e._subscribers,i=e._state;if(0!==t.length){for(var n=void 0,a=void 0,o=e._result,s=0;s<t.length;s+=3)n=t[s],a=t[s+i],n?I(i,n,a,o):a(o);e._subscribers.length=0}}function I(t,i,n,a){var o=e(n),s=void 0,r=void 0,l=!0;if(o){try{s=n(a)}catch(e){l=!1,r=e}if(i===s)return void R(i,new TypeError("A promises callback cannot return that same promise."))}else s=a;void 0!==i._state||(o&&l?A(i,s):!1===l?R(i,r):1===t?L(i,s):2===t&&R(i,s))}var M=0;function B(e){e[E]=M++,e._state=void 0,e._result=void 0,e._subscribers=[]}var O=function(){function e(e,t){this._instanceConstructor=e,this.promise=new e(k),this.promise[E]||B(this.promise),n(t)?(this.length=t.length,this._remaining=t.length,this._result=new Array(this.length),0===this.length?L(this.promise,this._result):(this.length=this.length||0,this._enumerate(t),0===this._remaining&&L(this.promise,this._result))):R(this.promise,new Error("Array Methods must be provided an Array"))}return e.prototype._enumerate=function(e){for(var t=0;void 0===this._state&&t<e.length;t++)this._eachEntry(e[t],t)},e.prototype._eachEntry=function(e,t){var i=this._instanceConstructor,n=i.resolve;if(n===C){var a=void 0,o=void 0,s=!1;try{a=e.then}catch(e){s=!0,o=e}if(a===w&&void 0!==e._state)this._settledAt(e._state,t,e._result);else if("function"!=typeof a)this._remaining--,this._result[t]=e;else if(i===U){var r=new i(k);s?R(r,o):T(r,e,a),this._willSettleAt(r,t)}else this._willSettleAt(new i(function(t){return t(e)}),t)}else this._willSettleAt(n(e),t)},e.prototype._settledAt=function(e,t,i){var n=this.promise;void 0===n._state&&(this._remaining--,2===e?R(n,i):this._result[t]=i),0===this._remaining&&L(n,this._result)},e.prototype._willSettleAt=function(e,t){var i=this;P(e,void 0,function(e){return i._settledAt(1,t,e)},function(e){return i._settledAt(2,t,e)})},e}(),U=function(){function t(e){this[E]=M++,this._result=this._state=void 0,this._subscribers=[],k!==e&&("function"!=typeof e&&function(){throw new TypeError("You must pass a resolver function as the first argument to the promise constructor")}(),this instanceof t?function(e,t){try{t(function(t){A(e,t)},function(t){R(e,t)})}catch(t){R(e,t)}}(this,e):function(){throw new TypeError("Failed to construct 'Promise': Please use the 'new' operator, this object constructor cannot be called as a function.")}())}return t.prototype.catch=function(e){return this.then(null,e)},t.prototype.finally=function(t){var i=this.constructor;return e(t)?this.then(function(e){return i.resolve(t()).then(function(){return e})},function(e){return i.resolve(t()).then(function(){throw e})}):this.then(t,t)},t}();return U.prototype.then=w,U.all=function(e){return new O(this,e).promise},U.race=function(e){var t=this;return n(e)?new t(function(i,n){for(var a=e.length,o=0;o<a;o++)t.resolve(e[o]).then(i,n)}):new t(function(e,t){return t(new TypeError("You must pass an array to race."))})},U.resolve=C,U.reject=function(e){var t=new this(k);return R(t,e),t},U._setScheduler=function(e){s=e},U._setAsap=function(e){r=e},U._asap=r,U.polyfill=function(){var e=void 0;if(void 0!==i)e=i;else if("undefined"!=typeof self)e=self;else try{e=Function("return this")()}catch(e){throw new Error("polyfill failed because global object is unavailable in this environment")}var t=e.Promise;if(t){var n=null;try{n=Object.prototype.toString.call(t.resolve())}catch(e){}if("[object Promise]"===n&&!t.cast)return}e.Promise=U},U.Promise=U,U},e.exports=n()}).call(this,i(16),i(17))},function(e,t){var i,n,a=e.exports={};function o(){throw new Error("setTimeout has not been defined")}function s(){throw new Error("clearTimeout has not been defined")}function r(e){if(i===setTimeout)return setTimeout(e,0);if((i===o||!i)&&setTimeout)return i=setTimeout,setTimeout(e,0);try{return i(e,0)}catch(t){try{return i.call(null,e,0)}catch(t){return i.call(this,e,0)}}}!function(){try{i="function"==typeof setTimeout?setTimeout:o}catch(e){i=o}try{n="function"==typeof clearTimeout?clearTimeout:s}catch(e){n=s}}();var l,d=[],c=!1,u=-1;function h(){c&&l&&(c=!1,l.length?d=l.concat(d):u=-1,d.length&&_())}function _(){if(!c){var e=r(h);c=!0;for(var t=d.length;t;){for(l=d,d=[];++u<t;)l&&l[u].run();u=-1,t=d.length}l=null,c=!1,function(e){if(n===clearTimeout)return clearTimeout(e);if((n===s||!n)&&clearTimeout)return n=clearTimeout,clearTimeout(e);try{n(e)}catch(t){try{return n.call(null,e)}catch(t){return n.call(this,e)}}}(e)}}function f(e,t){this.fun=e,this.array=t}function p(){}a.nextTick=function(e){var t=new Array(arguments.length-1);if(arguments.length>1)for(var i=1;i<arguments.length;i++)t[i-1]=arguments[i];d.push(new f(e,t)),1!==d.length||c||r(_)},f.prototype.run=function(){this.fun.apply(null,this.array)},a.title="browser",a.browser=!0,a.env={},a.argv=[],a.version="",a.versions={},a.on=p,a.addListener=p,a.once=p,a.off=p,a.removeListener=p,a.removeAllListeners=p,a.emit=p,a.prependListener=p,a.prependOnceListener=p,a.listeners=function(e){return[]},a.binding=function(e){throw new Error("process.binding is not supported")},a.cwd=function(){return"/"},a.chdir=function(e){throw new Error("process.chdir is not supported")},a.umask=function(){return 0}},function(e,t){var i;i=function(){return this}();try{i=i||new Function("return this")()}catch(e){"object"==("undefined"==typeof window?"undefined":(0,b.default)(window))&&(i=window)}e.exports=i},function(e,t,i){i.r(t);var n=i(9),a=i(12),o=i(10),s=i(1);t.default=function(e){var t=null,i=function(t,i){e.postMessage({msg:"logcat_callback",data:{type:t,logcat:i}})}.bind(this);a.a.install(),e.addEventListener("message",function(a){switch(a.data.cmd){case"init":(t=new o.a(a.data.param[0],a.data.param[1])).on(s.a.IO_ERROR,function(t,i){e.postMessage({msg:s.a.IO_ERROR,data:{type:t,info:i}})}.bind(this)),t.on(s.a.DEMUX_ERROR,function(t,i){e.postMessage({msg:s.a.DEMUX_ERROR,data:{type:t,info:i}})}.bind(this)),t.on(s.a.INIT_SEGMENT,function(t,i){var n={msg:s.a.INIT_SEGMENT,data:{type:t,data:i}};e.postMessage(n,[i.data])}.bind(this)),t.on(s.a.MEDIA_SEGMENT,function(t,i){var n={msg:s.a.MEDIA_SEGMENT,data:{type:t,data:i}};e.postMessage(n,[i.data])}.bind(this)),t.on(s.a.LOADING_COMPLETE,function(){var t={msg:s.a.LOADING_COMPLETE};e.postMessage(t)}.bind(this)),t.on(s.a.RECOVERED_EARLY_EOF,function(){var t={msg:s.a.RECOVERED_EARLY_EOF};e.postMessage(t)}.bind(this)),t.on(s.a.MEDIA_INFO,function(t){var i={msg:s.a.MEDIA_INFO,data:t};e.postMessage(i)}.bind(this)),t.on(s.a.METADATA_ARRIVED,function(t){var i={msg:s.a.METADATA_ARRIVED,data:t};e.postMessage(i)}.bind(this)),t.on(s.a.SCRIPTDATA_ARRIVED,function(t){var i={msg:s.a.SCRIPTDATA_ARRIVED,data:t};e.postMessage(i)}.bind(this)),t.on(s.a.TIMED_ID3_METADATA_ARRIVED,function(t){var i={msg:s.a.TIMED_ID3_METADATA_ARRIVED,data:t};e.postMessage(i)}.bind(this)),t.on(s.a.SMPTE2038_METADATA_ARRIVED,function(t){var i={msg:s.a.SMPTE2038_METADATA_ARRIVED,data:t};e.postMessage(i)}.bind(this)),t.on(s.a.SCTE35_METADATA_ARRIVED,function(t){var i={msg:s.a.SCTE35_METADATA_ARRIVED,data:t};e.postMessage(i)}.bind(this)),t.on(s.a.PES_PRIVATE_DATA_DESCRIPTOR,function(t){var i={msg:s.a.PES_PRIVATE_DATA_DESCRIPTOR,data:t};e.postMessage(i)}.bind(this)),t.on(s.a.PES_PRIVATE_DATA_ARRIVED,function(t){var i={msg:s.a.PES_PRIVATE_DATA_ARRIVED,data:t};e.postMessage(i)}.bind(this)),t.on(s.a.STATISTICS_INFO,function(t){var i={msg:s.a.STATISTICS_INFO,data:t};e.postMessage(i)}.bind(this)),t.on(s.a.RECOMMEND_SEEKPOINT,function(t){e.postMessage({msg:s.a.RECOMMEND_SEEKPOINT,data:t})}.bind(this));break;case"destroy":t&&(t.destroy(),t=null),e.postMessage({msg:"destroyed"});break;case"start":t.start();break;case"stop":t.stop();break;case"seek":t.seek(a.data.param);break;case"pause":t.pause();break;case"resume":t.resume();break;case"logging_config":var r=a.data.param;n.a.applyConfig(r),!0===r.enableCallback?n.a.addLogListener(i):n.a.removeLogListener(i)}})}},function(e,t,i){i.r(t);var n=i(12),a=i(11),o={enableWorker:!1,enableStashBuffer:!0,stashInitialSize:void 0,isLive:!1,liveBufferLatencyChasing:!1,liveBufferLatencyMaxLatency:1.5,liveBufferLatencyMinRemain:.5,lazyLoad:!0,lazyLoadMaxDuration:180,lazyLoadRecoverDuration:30,deferLoadAfterSourceOpen:!0,autoCleanupMaxBackwardDuration:180,autoCleanupMinBackwardDuration:120,statisticsInfoReportInterval:600,fixAudioTimestampGap:!0,accurateSeek:!1,seekType:"range",seekParamStart:"bstart",seekParamEnd:"bend",rangeLoadZeroStart:!1,customSeekHandler:void 0,reuseRedirectedURL:!1,headers:void 0,customLoader:void 0};function s(){return(0,f.default)({},o)}var r=function(){function e(){}return e.supportMSEH264Playback=function(){return window.MediaSource&&window.MediaSource.isTypeSupported('video/mp4; codecs="avc1.42E01E,mp4a.40.2"')},e.supportMSEH265Playback=function(){return window.MediaSource&&window.MediaSource.isTypeSupported('video/mp4; codecs="hvc1.1.6.L93.B0"')},e.supportNetworkStreamIO=function(){var e=new a.a({},s()),t=e.loaderType;return e.destroy(),"fetch-stream-loader"==t||"xhr-moz-chunked-loader"==t},e.getNetworkLoaderTypeName=function(){var e=new a.a({},s()),t=e.loaderType;return e.destroy(),t},e.supportNativeMediaPlayback=function(t){null==e.videoElement&&(e.videoElement=window.document.createElement("video"));var i=e.videoElement.canPlayType(t);return"probably"===i||"maybe"==i},e.getFeatureList=function(){var t={msePlayback:!1,mseLivePlayback:!1,mseH265Playback:!1,networkStreamIO:!1,networkLoaderName:"",nativeMP4H264Playback:!1,nativeMP4H265Playback:!1,nativeWebmVP8Playback:!1,nativeWebmVP9Playback:!1};return t.msePlayback=e.supportMSEH264Playback(),t.networkStreamIO=e.supportNetworkStreamIO(),t.networkLoaderName=e.getNetworkLoaderTypeName(),t.mseLivePlayback=t.msePlayback&&t.networkStreamIO,t.mseH265Playback=e.supportMSEH265Playback(),t.nativeMP4H264Playback=e.supportNativeMediaPlayback('video/mp4; codecs="avc1.42001E, mp4a.40.2"'),t.nativeMP4H265Playback=e.supportNativeMediaPlayback('video/mp4; codecs="hvc1.1.6.L93.B0"'),t.nativeWebmVP8Playback=e.supportNativeMediaPlayback('video/webm; codecs="vp8.0, vorbis"'),t.nativeWebmVP9Playback=e.supportNativeMediaPlayback('video/webm; codecs="vp9"'),t},e}(),l=i(2),d=i(6),u=i.n(d),h=i(0),_=i(4),m={ERROR:"error",LOADING_COMPLETE:"loading_complete",RECOVERED_EARLY_EOF:"recovered_early_eof",MEDIA_INFO:"media_info",METADATA_ARRIVED:"metadata_arrived",SCRIPTDATA_ARRIVED:"scriptdata_arrived",TIMED_ID3_METADATA_ARRIVED:"timed_id3_metadata_arrived",SMPTE2038_METADATA_ARRIVED:"smpte2038_metadata_arrived",SCTE35_METADATA_ARRIVED:"scte35_metadata_arrived",PES_PRIVATE_DATA_DESCRIPTOR:"pes_private_data_descriptor",PES_PRIVATE_DATA_ARRIVED:"pes_private_data_arrived",STATISTICS_INFO:"statistics_info"},g=i(13),v=i.n(g),y=i(9),S=i(10),w=i(1),C=i(8),E=function(){function e(e,t){if(this.TAG="Transmuxer",this._emitter=new u.a,t.enableWorker&&"undefined"!=typeof Worker)try{this._worker=v()(18),this._workerDestroying=!1,this._worker.addEventListener("message",this._onWorkerMessage.bind(this)),this._worker.postMessage({cmd:"init",param:[e,t]}),this.e={onLoggingConfigChanged:this._onLoggingConfigChanged.bind(this)},y.a.registerListener(this.e.onLoggingConfigChanged),this._worker.postMessage({cmd:"logging_config",param:y.a.getConfig()})}catch(i){h.a.e(this.TAG,"Error while initialize transmuxing worker, fallback to inline transmuxing"),this._worker=null,this._controller=new S.a(e,t)}else this._controller=new S.a(e,t);if(this._controller){var i=this._controller;i.on(w.a.IO_ERROR,this._onIOError.bind(this)),i.on(w.a.DEMUX_ERROR,this._onDemuxError.bind(this)),i.on(w.a.INIT_SEGMENT,this._onInitSegment.bind(this)),i.on(w.a.MEDIA_SEGMENT,this._onMediaSegment.bind(this)),i.on(w.a.LOADING_COMPLETE,this._onLoadingComplete.bind(this)),i.on(w.a.RECOVERED_EARLY_EOF,this._onRecoveredEarlyEof.bind(this)),i.on(w.a.MEDIA_INFO,this._onMediaInfo.bind(this)),i.on(w.a.METADATA_ARRIVED,this._onMetaDataArrived.bind(this)),i.on(w.a.SCRIPTDATA_ARRIVED,this._onScriptDataArrived.bind(this)),i.on(w.a.TIMED_ID3_METADATA_ARRIVED,this._onTimedID3MetadataArrived.bind(this)),i.on(w.a.SMPTE2038_METADATA_ARRIVED,this._onSMPTE2038MetadataArrived.bind(this)),i.on(w.a.SCTE35_METADATA_ARRIVED,this._onSCTE35MetadataArrived.bind(this)),i.on(w.a.PES_PRIVATE_DATA_DESCRIPTOR,this._onPESPrivateDataDescriptor.bind(this)),i.on(w.a.PES_PRIVATE_DATA_ARRIVED,this._onPESPrivateDataArrived.bind(this)),i.on(w.a.STATISTICS_INFO,this._onStatisticsInfo.bind(this)),i.on(w.a.RECOMMEND_SEEKPOINT,this._onRecommendSeekpoint.bind(this))}}return e.prototype.destroy=function(){this._worker?this._workerDestroying||(this._workerDestroying=!0,this._worker.postMessage({cmd:"destroy"}),y.a.removeListener(this.e.onLoggingConfigChanged),this.e=null):(this._controller.destroy(),this._controller=null),this._emitter.removeAllListeners(),this._emitter=null},e.prototype.on=function(e,t){this._emitter.addListener(e,t)},e.prototype.off=function(e,t){this._emitter.removeListener(e,t)},e.prototype.hasWorker=function(){return null!=this._worker},e.prototype.open=function(){this._worker?this._worker.postMessage({cmd:"start"}):this._controller.start()},e.prototype.close=function(){this._worker?this._worker.postMessage({cmd:"stop"}):this._controller.stop()},e.prototype.seek=function(e){this._worker?this._worker.postMessage({cmd:"seek",param:e}):this._controller.seek(e)},e.prototype.pause=function(){this._worker?this._worker.postMessage({cmd:"pause"}):this._controller.pause()},e.prototype.resume=function(){this._worker?this._worker.postMessage({cmd:"resume"}):this._controller.resume()},e.prototype._onInitSegment=function(e,t){var i=this;c.default.resolve().then(function(){i._emitter.emit(w.a.INIT_SEGMENT,e,t)})},e.prototype._onMediaSegment=function(e,t){var i=this;c.default.resolve().then(function(){i._emitter.emit(w.a.MEDIA_SEGMENT,e,t)})},e.prototype._onLoadingComplete=function(){var e=this;c.default.resolve().then(function(){e._emitter.emit(w.a.LOADING_COMPLETE)})},e.prototype._onRecoveredEarlyEof=function(){var e=this;c.default.resolve().then(function(){e._emitter.emit(w.a.RECOVERED_EARLY_EOF)})},e.prototype._onMediaInfo=function(e){var t=this;c.default.resolve().then(function(){t._emitter.emit(w.a.MEDIA_INFO,e)})},e.prototype._onMetaDataArrived=function(e){var t=this;c.default.resolve().then(function(){t._emitter.emit(w.a.METADATA_ARRIVED,e)})},e.prototype._onScriptDataArrived=function(e){var t=this;c.default.resolve().then(function(){t._emitter.emit(w.a.SCRIPTDATA_ARRIVED,e)})},e.prototype._onTimedID3MetadataArrived=function(e){var t=this;c.default.resolve().then(function(){t._emitter.emit(w.a.TIMED_ID3_METADATA_ARRIVED,e)})},e.prototype._onSMPTE2038MetadataArrived=function(e){var t=this;c.default.resolve().then(function(){t._emitter.emit(w.a.SMPTE2038_METADATA_ARRIVED,e)})},e.prototype._onSCTE35MetadataArrived=function(e){var t=this;c.default.resolve().then(function(){t._emitter.emit(w.a.SCTE35_METADATA_ARRIVED,e)})},e.prototype._onPESPrivateDataDescriptor=function(e){var t=this;c.default.resolve().then(function(){t._emitter.emit(w.a.PES_PRIVATE_DATA_DESCRIPTOR,e)})},e.prototype._onPESPrivateDataArrived=function(e){var t=this;c.default.resolve().then(function(){t._emitter.emit(w.a.PES_PRIVATE_DATA_ARRIVED,e)})},e.prototype._onStatisticsInfo=function(e){var t=this;c.default.resolve().then(function(){t._emitter.emit(w.a.STATISTICS_INFO,e)})},e.prototype._onIOError=function(e,t){var i=this;c.default.resolve().then(function(){i._emitter.emit(w.a.IO_ERROR,e,t)})},e.prototype._onDemuxError=function(e,t){var i=this;c.default.resolve().then(function(){i._emitter.emit(w.a.DEMUX_ERROR,e,t)})},e.prototype._onRecommendSeekpoint=function(e){var t=this;c.default.resolve().then(function(){t._emitter.emit(w.a.RECOMMEND_SEEKPOINT,e)})},e.prototype._onLoggingConfigChanged=function(e){this._worker&&this._worker.postMessage({cmd:"logging_config",param:e})},e.prototype._onWorkerMessage=function(e){var t=e.data,i=t.data;if("destroyed"===t.msg||this._workerDestroying)return this._workerDestroying=!1,this._worker.terminate(),void(this._worker=null);switch(t.msg){case w.a.INIT_SEGMENT:case w.a.MEDIA_SEGMENT:this._emitter.emit(t.msg,i.type,i.data);break;case w.a.LOADING_COMPLETE:case w.a.RECOVERED_EARLY_EOF:this._emitter.emit(t.msg);break;case w.a.MEDIA_INFO:(0,p.default)(i,C.a.prototype),this._emitter.emit(t.msg,i);break;case w.a.METADATA_ARRIVED:case w.a.SCRIPTDATA_ARRIVED:case w.a.TIMED_ID3_METADATA_ARRIVED:case w.a.SMPTE2038_METADATA_ARRIVED:case w.a.SCTE35_METADATA_ARRIVED:case w.a.PES_PRIVATE_DATA_DESCRIPTOR:case w.a.PES_PRIVATE_DATA_ARRIVED:case w.a.STATISTICS_INFO:this._emitter.emit(t.msg,i);break;case w.a.IO_ERROR:case w.a.DEMUX_ERROR:this._emitter.emit(t.msg,i.type,i.info);break;case w.a.RECOMMEND_SEEKPOINT:this._emitter.emit(t.msg,i);break;case"logcat_callback":h.a.emitter.emit("log",i.type,i.logcat)}},e}(),k="error",T="source_open",A="update_end",D="buffer_full",L=i(7),R=i(3),P=function(){function e(e){this.TAG="MSEController",this._config=e,this._emitter=new u.a,this._config.isLive&&null==this._config.autoCleanupSourceBuffer&&(this._config.autoCleanupSourceBuffer=!0),this.e={onSourceOpen:this._onSourceOpen.bind(this),onSourceEnded:this._onSourceEnded.bind(this),onSourceClose:this._onSourceClose.bind(this),onSourceBufferError:this._onSourceBufferError.bind(this),onSourceBufferUpdateEnd:this._onSourceBufferUpdateEnd.bind(this)},this._mediaSource=null,this._mediaSourceObjectURL=null,this._mediaElement=null,this._isBufferFull=!1,this._hasPendingEos=!1,this._requireSetMediaDuration=!1,this._pendingMediaDuration=0,this._pendingSourceBufferInit=[],this._mimeTypes={video:null,audio:null},this._sourceBuffers={video:null,audio:null},this._lastInitSegments={video:null,audio:null},this._pendingSegments={video:[],audio:[]},this._pendingRemoveRanges={video:[],audio:[]},this._idrList=new L.a}return e.prototype.destroy=function(){(this._mediaElement||this._mediaSource)&&this.detachMediaElement(),this.e=null,this._emitter.removeAllListeners(),this._emitter=null},e.prototype.on=function(e,t){this._emitter.addListener(e,t)},e.prototype.off=function(e,t){this._emitter.removeListener(e,t)},e.prototype.attachMediaElement=function(e){if(this._mediaSource)throw new R.a("MediaSource has been attached to an HTMLMediaElement!");var t=this._mediaSource=new window.MediaSource;t.addEventListener("sourceopen",this.e.onSourceOpen),t.addEventListener("sourceended",this.e.onSourceEnded),t.addEventListener("sourceclose",this.e.onSourceClose),this._mediaElement=e,this._mediaSourceObjectURL=window.URL.createObjectURL(this._mediaSource),e.src=this._mediaSourceObjectURL},e.prototype.detachMediaElement=function(){if(this._mediaSource){var e=this._mediaSource;for(var t in this._sourceBuffers){var i=this._pendingSegments[t];i.splice(0,i.length),this._pendingSegments[t]=null,this._pendingRemoveRanges[t]=null,this._lastInitSegments[t]=null;var n=this._sourceBuffers[t];if(n){if("closed"!==e.readyState){try{e.removeSourceBuffer(n)}catch(e){h.a.e(this.TAG,e.message)}n.removeEventListener("error",this.e.onSourceBufferError),n.removeEventListener("updateend",this.e.onSourceBufferUpdateEnd)}this._mimeTypes[t]=null,this._sourceBuffers[t]=null}}if("open"===e.readyState)try{e.endOfStream()}catch(e){h.a.e(this.TAG,e.message)}e.removeEventListener("sourceopen",this.e.onSourceOpen),e.removeEventListener("sourceended",this.e.onSourceEnded),e.removeEventListener("sourceclose",this.e.onSourceClose),this._pendingSourceBufferInit=[],this._isBufferFull=!1,this._idrList.clear(),this._mediaSource=null}this._mediaElement&&(this._mediaElement.src="",this._mediaElement.removeAttribute("src"),this._mediaElement=null),this._mediaSourceObjectURL&&(window.URL.revokeObjectURL(this._mediaSourceObjectURL),this._mediaSourceObjectURL=null)},e.prototype.appendInitSegment=function(e,t){if(!this._mediaSource||"open"!==this._mediaSource.readyState)return this._pendingSourceBufferInit.push(e),void this._pendingSegments[e.type].push(e);var i=e,n=""+i.container;i.codec&&i.codec.length>0&&(n+=";codecs="+i.codec);var a=!1;if(h.a.v(this.TAG,"Received Initialization Segment, mimeType: "+n),this._lastInitSegments[i.type]=i,n!==this._mimeTypes[i.type]){if(this._mimeTypes[i.type])h.a.v(this.TAG,"Notice: "+i.type+" mimeType changed, origin: "+this._mimeTypes[i.type]+", target: "+n);else{a=!0;try{var o=this._sourceBuffers[i.type]=this._mediaSource.addSourceBuffer(n);o.addEventListener("error",this.e.onSourceBufferError),o.addEventListener("updateend",this.e.onSourceBufferUpdateEnd)}catch(e){return h.a.e(this.TAG,e.message),void this._emitter.emit(k,{code:e.code,msg:e.message})}}this._mimeTypes[i.type]=n}t||this._pendingSegments[i.type].push(i),a||this._sourceBuffers[i.type]&&!this._sourceBuffers[i.type].updating&&this._doAppendSegments(),_.a.safari&&"audio/mpeg"===i.container&&i.mediaDuration>0&&(this._requireSetMediaDuration=!0,this._pendingMediaDuration=i.mediaDuration/1e3,this._updateMediaSourceDuration())},e.prototype.appendMediaSegment=function(e){var t=e;this._pendingSegments[t.type].push(t),this._config.autoCleanupSourceBuffer&&this._needCleanupSourceBuffer()&&this._doCleanupSourceBuffer();var i=this._sourceBuffers[t.type];!i||i.updating||this._hasPendingRemoveRanges()||this._doAppendSegments()},e.prototype.seek=function(e){for(var t in this._sourceBuffers)if(this._sourceBuffers[t]){var i=this._sourceBuffers[t];if("open"===this._mediaSource.readyState)try{i.abort()}catch(e){h.a.e(this.TAG,e.message)}this._idrList.clear();var n=this._pendingSegments[t];if(n.splice(0,n.length),"closed"!==this._mediaSource.readyState){for(var a=0;a<i.buffered.length;a++){var o=i.buffered.start(a),s=i.buffered.end(a);this._pendingRemoveRanges[t].push({start:o,end:s})}if(i.updating||this._doRemoveRanges(),_.a.safari){var r=this._lastInitSegments[t];r&&(this._pendingSegments[t].push(r),i.updating||this._doAppendSegments())}}}},e.prototype.endOfStream=function(){var e=this._mediaSource,t=this._sourceBuffers;e&&"open"===e.readyState?t.video&&t.video.updating||t.audio&&t.audio.updating?this._hasPendingEos=!0:(this._hasPendingEos=!1,e.endOfStream()):e&&"closed"===e.readyState&&this._hasPendingSegments()&&(this._hasPendingEos=!0)},e.prototype.getNearestKeyframe=function(e){return this._idrList.getLastSyncPointBeforeDts(e)},e.prototype._needCleanupSourceBuffer=function(){if(!this._config.autoCleanupSourceBuffer)return!1;var e=this._mediaElement.currentTime;for(var t in this._sourceBuffers){var i=this._sourceBuffers[t];if(i){var n=i.buffered;if(n.length>=1&&e-n.start(0)>=this._config.autoCleanupMaxBackwardDuration)return!0}}return!1},e.prototype._doCleanupSourceBuffer=function(){var e=this._mediaElement.currentTime;for(var t in this._sourceBuffers){var i=this._sourceBuffers[t];if(i){for(var n=i.buffered,a=!1,o=0;o<n.length;o++){var s=n.start(o),r=n.end(o);if(s<=e&&e<r+3){if(e-s>=this._config.autoCleanupMaxBackwardDuration){a=!0;var l=e-this._config.autoCleanupMinBackwardDuration;this._pendingRemoveRanges[t].push({start:s,end:l})}}else r<e&&(a=!0,this._pendingRemoveRanges[t].push({start:s,end:r}))}a&&!i.updating&&this._doRemoveRanges()}}},e.prototype._updateMediaSourceDuration=function(){var e=this._sourceBuffers;if(0!==this._mediaElement.readyState&&"open"===this._mediaSource.readyState&&!(e.video&&e.video.updating||e.audio&&e.audio.updating)){var t=this._mediaSource.duration,i=this._pendingMediaDuration;i>0&&(isNaN(t)||i>t)&&(h.a.v(this.TAG,"Update MediaSource duration from "+t+" to "+i),this._mediaSource.duration=i),this._requireSetMediaDuration=!1,this._pendingMediaDuration=0}},e.prototype._doRemoveRanges=function(){for(var e in this._pendingRemoveRanges)if(this._sourceBuffers[e]&&!this._sourceBuffers[e].updating)for(var t=this._sourceBuffers[e],i=this._pendingRemoveRanges[e];i.length&&!t.updating;){var n=i.shift();t.remove(n.start,n.end)}},e.prototype._doAppendSegments=function(){var e=this._pendingSegments;for(var t in e)if(this._sourceBuffers[t]&&!this._sourceBuffers[t].updating&&e[t].length>0){var i=e[t].shift();if(i.timestampOffset){var n=this._sourceBuffers[t].timestampOffset,a=i.timestampOffset/1e3;Math.abs(n-a)>.1&&(h.a.v(this.TAG,"Update MPEG audio timestampOffset from "+n+" to "+a),this._sourceBuffers[t].timestampOffset=a),delete i.timestampOffset}if(!i.data||0===i.data.byteLength)continue;try{this._sourceBuffers[t].appendBuffer(i.data),this._isBufferFull=!1,"video"===t&&i.hasOwnProperty("info")&&this._idrList.appendArray(i.info.syncPoints)}catch(e){this._pendingSegments[t].unshift(i),22===e.code?(this._isBufferFull||this._emitter.emit(D),this._isBufferFull=!0):(h.a.e(this.TAG,e.message),this._emitter.emit(k,{code:e.code,msg:e.message}))}}},e.prototype._onSourceOpen=function(){if(h.a.v(this.TAG,"MediaSource onSourceOpen"),this._mediaSource.removeEventListener("sourceopen",this.e.onSourceOpen),this._pendingSourceBufferInit.length>0)for(var e=this._pendingSourceBufferInit;e.length;){var t=e.shift();this.appendInitSegment(t,!0)}this._hasPendingSegments()&&this._doAppendSegments(),this._emitter.emit(T)},e.prototype._onSourceEnded=function(){h.a.v(this.TAG,"MediaSource onSourceEnded")},e.prototype._onSourceClose=function(){h.a.v(this.TAG,"MediaSource onSourceClose"),this._mediaSource&&null!=this.e&&(this._mediaSource.removeEventListener("sourceopen",this.e.onSourceOpen),this._mediaSource.removeEventListener("sourceended",this.e.onSourceEnded),this._mediaSource.removeEventListener("sourceclose",this.e.onSourceClose))},e.prototype._hasPendingSegments=function(){var e=this._pendingSegments;return e.video.length>0||e.audio.length>0},e.prototype._hasPendingRemoveRanges=function(){var e=this._pendingRemoveRanges;return e.video.length>0||e.audio.length>0},e.prototype._onSourceBufferUpdateEnd=function(){this._requireSetMediaDuration?this._updateMediaSourceDuration():this._hasPendingRemoveRanges()?this._doRemoveRanges():this._hasPendingSegments()?this._doAppendSegments():this._hasPendingEos&&this.endOfStream(),this._emitter.emit(A)},e.prototype._onSourceBufferError=function(e){h.a.e(this.TAG,"SourceBuffer Error: "+e)},e}(),x=i(5),I={NETWORK_ERROR:"NetworkError",MEDIA_ERROR:"MediaError",OTHER_ERROR:"OtherError"},M={NETWORK_EXCEPTION:l.b.EXCEPTION,NETWORK_STATUS_CODE_INVALID:l.b.HTTP_STATUS_CODE_INVALID,NETWORK_TIMEOUT:l.b.CONNECTING_TIMEOUT,NETWORK_UNRECOVERABLE_EARLY_EOF:l.b.UNRECOVERABLE_EARLY_EOF,MEDIA_MSE_ERROR:"MediaMSEError",MEDIA_FORMAT_ERROR:x.a.FORMAT_ERROR,MEDIA_FORMAT_UNSUPPORTED:x.a.FORMAT_UNSUPPORTED,MEDIA_CODEC_UNSUPPORTED:x.a.CODEC_UNSUPPORTED},B=function(){function e(e,t){this.TAG="MSEPlayer",this._type="MSEPlayer",this._emitter=new u.a,this._config=s(),"object"==(void 0===t?"undefined":(0,b.default)(t))&&(0,f.default)(this._config,t);var i=e.type.toLowerCase();if("mse"!==i&&"mpegts"!==i&&"m2ts"!==i&&"flv"!==i)throw new R.b("MSEPlayer requires an mpegts/m2ts/flv MediaDataSource input!");!0===e.isLive&&(this._config.isLive=!0),this.e={onvLoadedMetadata:this._onvLoadedMetadata.bind(this),onvSeeking:this._onvSeeking.bind(this),onvCanPlay:this._onvCanPlay.bind(this),onvStalled:this._onvStalled.bind(this),onvProgress:this._onvProgress.bind(this)},self.performance&&self.performance.now?this._now=self.performance.now.bind(self.performance):this._now=Date.now,this._pendingSeekTime=null,this._requestSetTime=!1,this._seekpointRecord=null,this._progressChecker=null,this._mediaDataSource=e,this._mediaElement=null,this._msectl=null,this._transmuxer=null,this._mseSourceOpened=!1,this._hasPendingLoad=!1,this._receivedCanPlay=!1,this._mediaInfo=null,this._statisticsInfo=null;var n=_.a.chrome&&(_.a.version.major<50||50===_.a.version.major&&_.a.version.build<2661);this._alwaysSeekKeyframe=!!(n||_.a.msedge||_.a.msie),this._alwaysSeekKeyframe&&(this._config.accurateSeek=!1)}return e.prototype.destroy=function(){null!=this._progressChecker&&(window.clearInterval(this._progressChecker),this._progressChecker=null),this._transmuxer&&this.unload(),this._mediaElement&&this.detachMediaElement(),this.e=null,this._mediaDataSource=null,this._emitter.removeAllListeners(),this._emitter=null},e.prototype.on=function(e,t){var i=this;e===m.MEDIA_INFO?null!=this._mediaInfo&&c.default.resolve().then(function(){i._emitter.emit(m.MEDIA_INFO,i.mediaInfo)}):e===m.STATISTICS_INFO&&null!=this._statisticsInfo&&c.default.resolve().then(function(){i._emitter.emit(m.STATISTICS_INFO,i.statisticsInfo)}),this._emitter.addListener(e,t)},e.prototype.off=function(e,t){this._emitter.removeListener(e,t)},e.prototype.attachMediaElement=function(e){var t=this;if(this._mediaElement=e,e.addEventListener("loadedmetadata",this.e.onvLoadedMetadata),e.addEventListener("seeking",this.e.onvSeeking),e.addEventListener("canplay",this.e.onvCanPlay),e.addEventListener("stalled",this.e.onvStalled),e.addEventListener("progress",this.e.onvProgress),this._msectl=new P(this._config),this._msectl.on(A,this._onmseUpdateEnd.bind(this)),this._msectl.on(D,this._onmseBufferFull.bind(this)),this._msectl.on(T,function(){t._mseSourceOpened=!0,t._hasPendingLoad&&(t._hasPendingLoad=!1,t.load())}),this._msectl.on(k,function(e){t._emitter.emit(m.ERROR,I.MEDIA_ERROR,M.MEDIA_MSE_ERROR,e)}),this._msectl.attachMediaElement(e),null!=this._pendingSeekTime)try{e.currentTime=this._pendingSeekTime,this._pendingSeekTime=null}catch(e){}},e.prototype.detachMediaElement=function(){this._mediaElement&&(this._msectl.detachMediaElement(),this._mediaElement.removeEventListener("loadedmetadata",this.e.onvLoadedMetadata),this._mediaElement.removeEventListener("seeking",this.e.onvSeeking),this._mediaElement.removeEventListener("canplay",this.e.onvCanPlay),this._mediaElement.removeEventListener("stalled",this.e.onvStalled),this._mediaElement.removeEventListener("progress",this.e.onvProgress),this._mediaElement=null),this._msectl&&(this._msectl.destroy(),this._msectl=null)},e.prototype.load=function(){var e=this;if(!this._mediaElement)throw new R.a("HTMLMediaElement must be attached before load()!");if(this._transmuxer)throw new R.a("MSEPlayer.load() has been called, please call unload() first!");this._hasPendingLoad||(this._config.deferLoadAfterSourceOpen&&!1===this._mseSourceOpened?this._hasPendingLoad=!0:(this._mediaElement.readyState>0&&(this._requestSetTime=!0,this._mediaElement.currentTime=0),this._transmuxer=new E(this._mediaDataSource,this._config),this._transmuxer.on(w.a.INIT_SEGMENT,function(t,i){e._msectl.appendInitSegment(i)}),this._transmuxer.on(w.a.MEDIA_SEGMENT,function(t,i){if(e._msectl.appendMediaSegment(i),e._config.lazyLoad&&!e._config.isLive){var n=e._mediaElement.currentTime;i.info.endDts>=1e3*(n+e._config.lazyLoadMaxDuration)&&null==e._progressChecker&&(h.a.v(e.TAG,"Maximum buffering duration exceeded, suspend transmuxing task"),e._suspendTransmuxer())}}),this._transmuxer.on(w.a.LOADING_COMPLETE,function(){e._msectl.endOfStream(),e._emitter.emit(m.LOADING_COMPLETE)}),this._transmuxer.on(w.a.RECOVERED_EARLY_EOF,function(){e._emitter.emit(m.RECOVERED_EARLY_EOF)}),this._transmuxer.on(w.a.IO_ERROR,function(t,i){e._emitter.emit(m.ERROR,I.NETWORK_ERROR,t,i)}),this._transmuxer.on(w.a.DEMUX_ERROR,function(t,i){e._emitter.emit(m.ERROR,I.MEDIA_ERROR,t,{code:-1,msg:i})}),this._transmuxer.on(w.a.MEDIA_INFO,function(t){e._mediaInfo=t,e._emitter.emit(m.MEDIA_INFO,(0,f.default)({},t))}),this._transmuxer.on(w.a.METADATA_ARRIVED,function(t){e._emitter.emit(m.METADATA_ARRIVED,t)}),this._transmuxer.on(w.a.SCRIPTDATA_ARRIVED,function(t){e._emitter.emit(m.SCRIPTDATA_ARRIVED,t)}),this._transmuxer.on(w.a.TIMED_ID3_METADATA_ARRIVED,function(t){e._emitter.emit(m.TIMED_ID3_METADATA_ARRIVED,t)}),this._transmuxer.on(w.a.SMPTE2038_METADATA_ARRIVED,function(t){e._emitter.emit(m.SMPTE2038_METADATA_ARRIVED,t)}),this._transmuxer.on(w.a.SCTE35_METADATA_ARRIVED,function(t){e._emitter.emit(m.SCTE35_METADATA_ARRIVED,t)}),this._transmuxer.on(w.a.PES_PRIVATE_DATA_DESCRIPTOR,function(t){e._emitter.emit(m.PES_PRIVATE_DATA_DESCRIPTOR,t)}),this._transmuxer.on(w.a.PES_PRIVATE_DATA_ARRIVED,function(t){e._emitter.emit(m.PES_PRIVATE_DATA_ARRIVED,t)}),this._transmuxer.on(w.a.STATISTICS_INFO,function(t){e._statisticsInfo=e._fillStatisticsInfo(t),e._emitter.emit(m.STATISTICS_INFO,(0,f.default)({},e._statisticsInfo))}),this._transmuxer.on(w.a.RECOMMEND_SEEKPOINT,function(t){e._mediaElement&&!e._config.accurateSeek&&(e._requestSetTime=!0,e._mediaElement.currentTime=t/1e3)}),this._transmuxer.open()))},e.prototype.unload=function(){this._mediaElement&&this._mediaElement.pause(),this._msectl&&this._msectl.seek(0),this._transmuxer&&(this._transmuxer.close(),this._transmuxer.destroy(),this._transmuxer=null)},e.prototype.play=function(){return this._mediaElement.play()},e.prototype.pause=function(){this._mediaElement.pause()},Object.defineProperty(e.prototype,"type",{get:function(){return this._type},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"buffered",{get:function(){return this._mediaElement.buffered},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"duration",{get:function(){return this._mediaElement.duration},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"volume",{get:function(){return this._mediaElement.volume},set:function(e){this._mediaElement.volume=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"muted",{get:function(){return this._mediaElement.muted},set:function(e){this._mediaElement.muted=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"currentTime",{get:function(){return this._mediaElement?this._mediaElement.currentTime:0},set:function(e){this._mediaElement?this._internalSeek(e):this._pendingSeekTime=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"mediaInfo",{get:function(){return(0,f.default)({},this._mediaInfo)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"statisticsInfo",{get:function(){return null==this._statisticsInfo&&(this._statisticsInfo={}),this._statisticsInfo=this._fillStatisticsInfo(this._statisticsInfo),(0,f.default)({},this._statisticsInfo)},enumerable:!1,configurable:!0}),e.prototype._fillStatisticsInfo=function(e){if(e.playerType=this._type,!(this._mediaElement instanceof HTMLVideoElement))return e;var t=!0,i=0,n=0;if(this._mediaElement.getVideoPlaybackQuality){var a=this._mediaElement.getVideoPlaybackQuality();i=a.totalVideoFrames,n=a.droppedVideoFrames}else null!=this._mediaElement.webkitDecodedFrameCount?(i=this._mediaElement.webkitDecodedFrameCount,n=this._mediaElement.webkitDroppedFrameCount):t=!1;return t&&(e.decodedFrames=i,e.droppedFrames=n),e},e.prototype._onmseUpdateEnd=function(){var e=this._mediaElement.buffered,t=this._mediaElement.currentTime;if(this._config.isLive&&this._config.liveBufferLatencyChasing&&e.length>0&&!this._mediaElement.paused){var i=e.end(e.length-1);if(i>this._config.liveBufferLatencyMaxLatency&&i-t>this._config.liveBufferLatencyMaxLatency){var n=i-this._config.liveBufferLatencyMinRemain;this.currentTime=n}}if(this._config.lazyLoad&&!this._config.isLive){for(var a=0,o=0;o<e.length;o++){var s=e.start(o),r=e.end(o);if(s<=t&&t<r){a=r;break}}a>=t+this._config.lazyLoadMaxDuration&&null==this._progressChecker&&(h.a.v(this.TAG,"Maximum buffering duration exceeded, suspend transmuxing task"),this._suspendTransmuxer())}},e.prototype._onmseBufferFull=function(){h.a.v(this.TAG,"MSE SourceBuffer is full, suspend transmuxing task"),null==this._progressChecker&&this._suspendTransmuxer()},e.prototype._suspendTransmuxer=function(){this._transmuxer&&(this._transmuxer.pause(),null==this._progressChecker&&(this._progressChecker=window.setInterval(this._checkProgressAndResume.bind(this),1e3)))},e.prototype._checkProgressAndResume=function(){for(var e=this._mediaElement.currentTime,t=this._mediaElement.buffered,i=!1,n=0;n<t.length;n++){var a=t.start(n),o=t.end(n);if(e>=a&&e<o){e>=o-this._config.lazyLoadRecoverDuration&&(i=!0);break}}i&&(window.clearInterval(this._progressChecker),this._progressChecker=null,i&&(h.a.v(this.TAG,"Continue loading from paused position"),this._transmuxer.resume()))},e.prototype._isTimepointBuffered=function(e){for(var t=this._mediaElement.buffered,i=0;i<t.length;i++){var n=t.start(i),a=t.end(i);if(e>=n&&e<a)return!0}return!1},e.prototype._internalSeek=function(e){var t=this._isTimepointBuffered(e),i=!1,n=0;if(e<1&&this._mediaElement.buffered.length>0){var a=this._mediaElement.buffered.start(0);(a<1&&e<a||_.a.safari)&&(i=!0,n=_.a.safari?.1:a)}if(i)this._requestSetTime=!0,this._mediaElement.currentTime=n;else if(t){if(this._alwaysSeekKeyframe){var o=this._msectl.getNearestKeyframe(Math.floor(1e3*e));this._requestSetTime=!0,this._mediaElement.currentTime=null!=o?o.dts/1e3:e}else this._requestSetTime=!0,this._mediaElement.currentTime=e;null!=this._progressChecker&&this._checkProgressAndResume()}else null!=this._progressChecker&&(window.clearInterval(this._progressChecker),this._progressChecker=null),this._msectl.seek(e),this._transmuxer.seek(Math.floor(1e3*e)),this._config.accurateSeek&&(this._requestSetTime=!0,this._mediaElement.currentTime=e)},e.prototype._checkAndApplyUnbufferedSeekpoint=function(){if(this._seekpointRecord)if(this._seekpointRecord.recordTime<=this._now()-100){var e=this._mediaElement.currentTime;this._seekpointRecord=null,this._isTimepointBuffered(e)||(null!=this._progressChecker&&(window.clearTimeout(this._progressChecker),this._progressChecker=null),this._msectl.seek(e),this._transmuxer.seek(Math.floor(1e3*e)),this._config.accurateSeek&&(this._requestSetTime=!0,this._mediaElement.currentTime=e))}else window.setTimeout(this._checkAndApplyUnbufferedSeekpoint.bind(this),50)},e.prototype._checkAndResumeStuckPlayback=function(e){var t=this._mediaElement;if(e||!this._receivedCanPlay||t.readyState<2){var i=t.buffered;i.length>0&&t.currentTime<i.start(0)&&(h.a.w(this.TAG,"Playback seems stuck at "+t.currentTime+", seek to "+i.start(0)),this._requestSetTime=!0,this._mediaElement.currentTime=i.start(0),this._mediaElement.removeEventListener("progress",this.e.onvProgress))}else this._mediaElement.removeEventListener("progress",this.e.onvProgress)},e.prototype._onvLoadedMetadata=function(e){null!=this._pendingSeekTime&&(this._mediaElement.currentTime=this._pendingSeekTime,this._pendingSeekTime=null)},e.prototype._onvSeeking=function(e){var t=this._mediaElement.currentTime,i=this._mediaElement.buffered;if(this._requestSetTime)this._requestSetTime=!1;else{if(t<1&&i.length>0){var n=i.start(0);if(n<1&&t<n||_.a.safari)return this._requestSetTime=!0,void(this._mediaElement.currentTime=_.a.safari?.1:n)}if(this._isTimepointBuffered(t)){if(this._alwaysSeekKeyframe){var a=this._msectl.getNearestKeyframe(Math.floor(1e3*t));null!=a&&(this._requestSetTime=!0,this._mediaElement.currentTime=a.dts/1e3)}null!=this._progressChecker&&this._checkProgressAndResume()}else this._seekpointRecord={seekPoint:t,recordTime:this._now()},window.setTimeout(this._checkAndApplyUnbufferedSeekpoint.bind(this),50)}},e.prototype._onvCanPlay=function(e){this._receivedCanPlay=!0,this._mediaElement.removeEventListener("canplay",this.e.onvCanPlay)},e.prototype._onvStalled=function(e){this._checkAndResumeStuckPlayback(!0)},e.prototype._onvProgress=function(e){this._checkAndResumeStuckPlayback()},e}(),O=function(){function e(e,t){this.TAG="NativePlayer",this._type="NativePlayer",this._emitter=new u.a,this._config=s(),"object"==(void 0===t?"undefined":(0,b.default)(t))&&(0,f.default)(this._config,t);var i=e.type.toLowerCase();if("mse"===i||"mpegts"===i||"m2ts"===i||"flv"===i)throw new R.b("NativePlayer does't support mse/mpegts/m2ts/flv MediaDataSource input!");if(e.hasOwnProperty("segments"))throw new R.b("NativePlayer("+e.type+") doesn't support multipart playback!");this.e={onvLoadedMetadata:this._onvLoadedMetadata.bind(this)},this._pendingSeekTime=null,this._statisticsReporter=null,this._mediaDataSource=e,this._mediaElement=null}return e.prototype.destroy=function(){this._mediaElement&&(this.unload(),this.detachMediaElement()),this.e=null,this._mediaDataSource=null,this._emitter.removeAllListeners(),this._emitter=null},e.prototype.on=function(e,t){var i=this;e===m.MEDIA_INFO?null!=this._mediaElement&&0!==this._mediaElement.readyState&&c.default.resolve().then(function(){i._emitter.emit(m.MEDIA_INFO,i.mediaInfo)}):e===m.STATISTICS_INFO&&null!=this._mediaElement&&0!==this._mediaElement.readyState&&c.default.resolve().then(function(){i._emitter.emit(m.STATISTICS_INFO,i.statisticsInfo)}),this._emitter.addListener(e,t)},e.prototype.off=function(e,t){this._emitter.removeListener(e,t)},e.prototype.attachMediaElement=function(e){if(this._mediaElement=e,e.addEventListener("loadedmetadata",this.e.onvLoadedMetadata),null!=this._pendingSeekTime)try{e.currentTime=this._pendingSeekTime,this._pendingSeekTime=null}catch(e){}},e.prototype.detachMediaElement=function(){this._mediaElement&&(this._mediaElement.src="",this._mediaElement.removeAttribute("src"),this._mediaElement.removeEventListener("loadedmetadata",this.e.onvLoadedMetadata),this._mediaElement=null),null!=this._statisticsReporter&&(window.clearInterval(this._statisticsReporter),this._statisticsReporter=null)},e.prototype.load=function(){if(!this._mediaElement)throw new R.a("HTMLMediaElement must be attached before load()!");this._mediaElement.src=this._mediaDataSource.url,this._mediaElement.readyState>0&&(this._mediaElement.currentTime=0),this._mediaElement.preload="auto",this._mediaElement.load(),this._statisticsReporter=window.setInterval(this._reportStatisticsInfo.bind(this),this._config.statisticsInfoReportInterval)},e.prototype.unload=function(){this._mediaElement&&(this._mediaElement.src="",this._mediaElement.removeAttribute("src")),null!=this._statisticsReporter&&(window.clearInterval(this._statisticsReporter),this._statisticsReporter=null)},e.prototype.play=function(){return this._mediaElement.play()},e.prototype.pause=function(){this._mediaElement.pause()},Object.defineProperty(e.prototype,"type",{get:function(){return this._type},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"buffered",{get:function(){return this._mediaElement.buffered},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"duration",{get:function(){return this._mediaElement.duration},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"volume",{get:function(){return this._mediaElement.volume},set:function(e){this._mediaElement.volume=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"muted",{get:function(){return this._mediaElement.muted},set:function(e){this._mediaElement.muted=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"currentTime",{get:function(){return this._mediaElement?this._mediaElement.currentTime:0},set:function(e){this._mediaElement?this._mediaElement.currentTime=e:this._pendingSeekTime=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"mediaInfo",{get:function(){var e={mimeType:(this._mediaElement instanceof HTMLAudioElement?"audio/":"video/")+this._mediaDataSource.type};return this._mediaElement&&(e.duration=Math.floor(1e3*this._mediaElement.duration),this._mediaElement instanceof HTMLVideoElement&&(e.width=this._mediaElement.videoWidth,e.height=this._mediaElement.videoHeight)),e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"statisticsInfo",{get:function(){var e={playerType:this._type,url:this._mediaDataSource.url};if(!(this._mediaElement instanceof HTMLVideoElement))return e;var t=!0,i=0,n=0;if(this._mediaElement.getVideoPlaybackQuality){var a=this._mediaElement.getVideoPlaybackQuality();i=a.totalVideoFrames,n=a.droppedVideoFrames}else null!=this._mediaElement.webkitDecodedFrameCount?(i=this._mediaElement.webkitDecodedFrameCount,n=this._mediaElement.webkitDroppedFrameCount):t=!1;return t&&(e.decodedFrames=i,e.droppedFrames=n),e},enumerable:!1,configurable:!0}),e.prototype._onvLoadedMetadata=function(e){null!=this._pendingSeekTime&&(this._mediaElement.currentTime=this._pendingSeekTime,this._pendingSeekTime=null),this._emitter.emit(m.MEDIA_INFO,this.mediaInfo)},e.prototype._reportStatisticsInfo=function(){this._emitter.emit(m.STATISTICS_INFO,this.statisticsInfo)},e}();n.a.install();var U={createPlayer:function(e,t){var i=e;if(null==i||"object"!=(void 0===i?"undefined":(0,b.default)(i)))throw new R.b("MediaDataSource must be an javascript object!");if(!i.hasOwnProperty("type"))throw new R.b("MediaDataSource must has type field to indicate video file type!");switch(i.type){case"mse":case"mpegts":case"m2ts":case"flv":return new B(i,t);default:return new O(i,t)}},isSupported:function(){return r.supportMSEH264Playback()},getFeatureList:function(){return r.getFeatureList()}};U.BaseLoader=l.a,U.LoaderStatus=l.c,U.LoaderErrors=l.b,U.Events=m,U.ErrorTypes=I,U.ErrorDetails=M,U.MSEPlayer=B,U.NativePlayer=O,U.LoggingControl=y.a,Object.defineProperty(U,"version",{enumerable:!0,get:function(){return"1.7.3"}}),t.default=U}])})}).call(this,i("YuTi")(e))},YeFu:function(e,t,i){"use strict";i.r(t);var n=i("4BeY"),a=i.n(n),o=i("IaFt"),s=i.n(o),r=new a.a({id:"icon-cloud",use:"icon-cloud-usage",viewBox:"0 0 36 36",content:'<symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 36 36" id="icon-cloud"><defs><style>#icon-cloud .a{fill:#fff;stroke:#707070;opacity:0;}#icon-cloud .b,#icon-cloud .e{fill:none;}#icon-cloud .b{stroke:#8a9097;stroke-linecap:round;stroke-linejoin:round;stroke-width:1.6px;}#icon-cloud .c{fill:#8a9097;}#icon-cloud .d{stroke:none;}</style></defs><g transform="translate(-25 -488)"><g class="a" transform="translate(25 488)"><rect class="d" width="36" height="36" /><rect class="e" x="0.5" y="0.5" width="35" height="35" /></g><g transform="translate(0.742 -32.333)"><path class="b" d="M227.048,392.645a5.75,5.75,0,0,1,11.5,0l.026.13a4.792,4.792,0,0,1-1.113,9.453h0a4.821,4.821,0,0,1-1.309-.179s-6.889.014-6.915.021a4.793,4.793,0,1,1-2.342-9.295Z" transform="translate(-190.255 143.438)" /><path class="c" d="M242.725,388.749l-3.416-3.393a.712.712,0,0,0-1.006,0l-3.321,3.334a.712.712,0,0,0,1.008,1l2.108-2.117v5.342a.711.711,0,1,0,1.422,0v-5.35l2.2,2.185a.711.711,0,1,0,1-1.01Z" transform="translate(-196.267 149.924)" /></g></g></symbol>'});s.a.add(r);t.default=r},Yfch:function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.isvalidUsername=function(e){return!!/^\d{7,11}$/g.test(e)},t.validAZNumber=function(e){return!!/^[A-Za-z0-9]+$/.test(e)},t.validPwdKo=function(e){return!!/^[a-zA-Z0-9,.;:'?!@]+$/.test(e)},t.validPwd=function(e){return!!/^.{6,18}$/g.test(e)},t.validPwdCylan=function(e){return!!/^.{6,128}$/g.test(e)},t.validSN=function(e){return!!/^\d{16}$/g.test(e)},t.validMoney=function(e){return/(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/.test(e)},t.validateURL=function(e){return/^(https?|ftp):\/\/([a-zA-Z0-9.-]+(:[a-zA-Z0-9.&%$-]+)*@)*((25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]?)(\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){3}|([a-zA-Z0-9-]+\.)*[a-zA-Z0-9-]+\.(com|edu|gov|int|mil|net|org|biz|arpa|info|name|pro|aero|coop|museum|[a-zA-Z]{2}))(:[0-9]+)*(\/($|[a-zA-Z0-9.,?'\\+&%$#=~_-]+))*$/.test(e)},t.validateEmail=function(e){return/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/.test(e)}},YgiJ:function(e,t,i){},Yu0S:function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getLogs=function(e){return(0,n.default)({url:"v1/miru/log/list",method:"post",data:e})};var n=function(e){return e&&e.__esModule?e:{default:e}}(i("t3Un"))},ZDNR:function(e,t,i){"use strict";var n=i("LWV3");i.n(n).a},ZJrb:function(e,t,i){"use strict";i.r(t);var n=i("vpN3"),a=i.n(n);for(var o in n)"default"!==o&&function(e){i.d(t,e,function(){return n[e]})}(o);t.default=a.a},Zj4A:function(e,t,i){"use strict";var n=i("w1r0");i.n(n).a},Zt36:function(e,t,i){"use strict";var n=i("O8dt");i.n(n).a},a14t:function(e,t,i){"use strict";var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"wscn-http404-container"},[i("div",{staticClass:"wscn-http404"},[e._m(0),e._v(" "),i("div",{staticClass:"bullshit"},[i("div",{staticClass:"bullshit__oops"},[e._v("OOPS!")]),e._v(" "),i("div",{staticClass:"bullshit__headline"},[e._v(e._s(e.message))]),e._v(" "),i("div",{staticClass:"bullshit__info"},[e._v("请检查您输入的网址是否正确，请点击以下按钮返回主页")]),e._v(" "),"admin"===e.$store.getters.role?i("router-link",{staticClass:"bullshit__return-home",attrs:{to:"/"}},[e._v("返回首页")]):i("router-link",{staticClass:"bullshit__return-home",attrs:{to:"/home"}},[e._v("返回首页")])],1)])])},a=[function(){var e=this.$createElement,t=this._self._c||e;return t("div",{staticClass:"pic-404"},[t("img",{staticClass:"pic-404__parent",attrs:{src:i("o2sD"),alt:"404"}}),this._v(" "),t("img",{staticClass:"pic-404__child left",attrs:{src:i("Jvyq"),alt:"404"}}),this._v(" "),t("img",{staticClass:"pic-404__child mid",attrs:{src:i("Jvyq"),alt:"404"}}),this._v(" "),t("img",{staticClass:"pic-404__child right",attrs:{src:i("Jvyq"),alt:"404"}})])}];n._withStripped=!0,i.d(t,"a",function(){return n}),i.d(t,"b",function(){return a})},bRjK:function(e,t,i){"use strict";var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"errPage-container"},[i("el-button",{staticClass:"pan-back-btn",attrs:{icon:"arrow-left"},on:{click:e.back}},[e._v("返回")]),e._v(" "),i("el-row",[i("el-col",{attrs:{span:12}},[i("h1",{staticClass:"text-jumbo text-ginormous"},[e._v("Oops!")]),e._v(" "),i("h2",[e._v("你没有权限去该页面")]),e._v(" "),i("ul",{staticClass:"list-unstyled"},[i("li",{staticClass:"link-type"},[i("a",{attrs:{href:"#"},on:{click:e.back}},[e._v("返回")])]),e._v(" "),i("li",{staticClass:"link-type"},[i("a",{attrs:{href:"https://www.taobao.com/"}},[e._v("随便看看")])]),e._v(" "),i("li",[i("a",{attrs:{href:"#"},on:{click:function(t){t.preventDefault(),e.dialogVisible=!0}}},[e._v("点我看图")])])])]),e._v(" "),i("el-col",{attrs:{span:12}},[i("img",{attrs:{src:e.errGif,width:"313",height:"428",alt:"Girl has dropped her ice cream."}})])],1),e._v(" "),i("el-dialog",{attrs:{visible:e.dialogVisible,title:"随便看"},on:{"update:visible":function(t){e.dialogVisible=t}}},[i("img",{staticClass:"pan-img",attrs:{src:e.ewizardClap}})])],1)},a=[];n._withStripped=!0,i.d(t,"a",function(){return n}),i.d(t,"b",function(){return a})},"c5/p":function(e,t,i){"use strict";i.r(t);var n=i("iIxO"),a=i.n(n);for(var o in n)"default"!==o&&function(e){i.d(t,e,function(){return n[e]})}(o);t.default=a.a},c8V3:function(e,t,i){"use strict";var n=function(){var e=this.$createElement,t=this._self._c||e;return t("section",{staticClass:"app-main"},[t("transition",{attrs:{name:"fade-transform"}},[t("keep-alive",[this.isKeepAlive?t("router-view",{key:this.key}):this._e()],1)],1),this._v(" "),this.isKeepAlive?this._e():t("router-view",{key:this.key})],1)},a=[];n._withStripped=!0,i.d(t,"a",function(){return n}),i.d(t,"b",function(){return a})},cnwH:function(e,t,i){"use strict";i.r(t);var n=i("iz5v"),a=i.n(n);for(var o in n)"default"!==o&&function(e){i.d(t,e,function(){return n[e]})}(o);t.default=a.a},dZcW:function(e,t,i){e.exports=i.p+"static/img/deviceIcon.df82f9b.png"},eOPU:function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=l(i("gDS+")),a=l(i("14Xm")),o=l(i("D3Ub")),s=l(i("iCc5")),r=l(i("V7oC"));function l(e){return e&&e.__esModule?e:{default:e}}var d=1,c=2,u=200,h=201,_=202,f={offerToReceiveAudio:1,offerToReceiveVideo:1},p=function(){function e(){(0,s.default)(this,e),this.pc=null,this.peerId=null,this.localStream=null,this.offer="",this.permitCode="",this.gRemoteVideo,this.gLocalVideo,this.gWSConn,this.gUserId,this.gStunAddr,this.gTurnAddr,this.gTurnUser,this.gTurnPass,this.gHBTimer=null,this.gClearTimer=null,this.gReloginTimer=null,this.gServer,this.gKey,this.gSecret,this.gAuthToken="",this.gAuthType=0,this.isClear=!1,this.isAnswer=!1,this.candidates=[],this.gStateCB=null,this.gContext,this.gMid=0,this.gMsg="",this.isHangup=!1}return(0,r.default)(e,[{key:"SetRemoteVideo",value:function(){var e=(0,o.default)(a.default.mark(function e(t){return a.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:console.log("----set video handle",t),this.gRemoteVideo=t;case 2:case"end":return e.stop()}},e,this)}));return function(t){return e.apply(this,arguments)}}()},{key:"Connect",value:function(){var e=(0,o.default)(a.default.mark(function e(t,i,n){return a.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:this.gServer=t,this.gKey=i,this.gSecret=n,this.gAuthType=0,this.gAuthToken="",this.isHangup=!1,m(this);case 7:case"end":return e.stop()}},e,this)}));return function(t,i,n){return e.apply(this,arguments)}}()},{key:"ApiConnect",value:function(){var e=(0,o.default)(a.default.mark(function e(t,i){return a.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:this.gServer=t,this.gKey="",this.gSecret="",this.gAuthType=2,this.gAuthToken=i,m(this);case 6:case"end":return e.stop()}},e,this)}));return function(t,i){return e.apply(this,arguments)}}()},{key:"Close",value:function(){var e=(0,o.default)(a.default.mark(function e(){return a.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.Hangup();case 2:this.sendMessageToPeer("signout",{});case 3:case"end":return e.stop()}},e,this)}));return function(){return e.apply(this,arguments)}}()},{key:"Call",value:function(){var e=(0,o.default)(a.default.mark(function e(t,i,n){var o,s;return a.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(void 0!==this.gUserId){e.next=4;break}return console.log("please signIn first"),this.callStateCB(4,0,"Please login first",""),e.abrupt("return");case 4:return this.isHangup=!1,this.peerId=t,this.permitCode=i,e.next=9,this.openMediaDevice(!0,!0);case 9:return this.localStream=e.sent,o=this.createPC(),e.prev=11,console.log("createOffer start"),e.next=15,o.createOffer(f);case 15:return s=e.sent,console.log("offer created. ",s),e.next=19,this.onCreateOfferSuccess(o,s);case 19:this.offer=s,this.pc=o,this.sendMessageToPeerEx("callRequest",s,n),e.next=27;break;case 24:e.prev=24,e.t0=e.catch(11),this.onCreateSessionDescriptionError(e.t0);case 27:case"end":return e.stop()}},e,this,[[11,24]])}));return function(t,i,n){return e.apply(this,arguments)}}()},{key:"GetPeerConnectionState",value:function(){return null==this.pc?"null":(console.log("----------GetPeerConnectionState "+this.pc.connectionState),this.pc.connectionState)}},{key:"IsPeerConnected",value:function(){return null!=this.pc&&(console.log("----------GetPeerConnectionState "+this.pc.connectionState),"connected"==this.pc.connectionState)}},{key:"CloseIMWebsocket",value:function(){console.log(this.gWSConn),this.gWSConn.url&&this.gWSConn.close()}},{key:"Hangup",value:function(){var e=(0,o.default)(a.default.mark(function e(){return a.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return this.sendMessageToPeer("hangup",{}),e.next=3,this.reset();case 3:this.isHangup=!0,console.log("Ending call");case 5:case"end":return e.stop()}},e,this)}));return function(){return e.apply(this,arguments)}}()},{key:"SwitchAudio",value:function(){var e=(0,o.default)(a.default.mark(function e(t,i){return a.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:this.sendMessageToPeer("switchAudio",{act:t,type:i});case 1:case"end":return e.stop()}},e,this)}));return function(t,i){return e.apply(this,arguments)}}()},{key:"SwitchVideo",value:function(){var e=(0,o.default)(a.default.mark(function e(t,i){return a.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:this.sendMessageToPeer("switchVideo",{act:t,type:i});case 1:case"end":return e.stop()}},e,this)}));return function(t,i){return e.apply(this,arguments)}}()},{key:"History",value:function(){var e=(0,o.default)(a.default.mark(function e(t,i,n){return a.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:this.peerId=t,this.permitCode=i,this.sendMessageToPeer("historyRequest",n);case 3:case"end":return e.stop()}},e,this)}));return function(t,i,n){return e.apply(this,arguments)}}()},{key:"HistorySetup",value:function(){var e=(0,o.default)(a.default.mark(function e(t,i){return a.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:this.sendMessageToPeer("historySetupRequest",{time:t,duration:i});case 1:case"end":return e.stop()}},e,this)}));return function(t,i){return e.apply(this,arguments)}}()},{key:"Forward",value:function(){var e=(0,o.default)(a.default.mark(function e(t,i,n){return a.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:this.peerId=t,this.permitCode=i,this.sendMessageToPeer("forwardRequest",n);case 3:case"end":return e.stop()}},e,this)}));return function(t,i,n){return e.apply(this,arguments)}}()},{key:"SetLocalVideo",value:function(e){this.gLocalVideo=e,this.gLocalVideo.addEventListener("loadedmetadata",function(){console.log("Local video videoWidth: "+this.videoWidth+"px,  videoHeight: "+this.videoHeight+"px")})}},{key:"SetStateCB",value:function(e,t){this.gStateCB=e,this.gContext=t}}]),e}();function m(e){console.log("signIn(session) ",e);var t=new WebSocket(e.gServer);e.isClear=!1,e.gWSConn=t,t.onopen=function(i){e.callStateCB(d,0,"Connect to server success",""),console.log("Connection open"),t.send((0,n.default)({opcode:"signin",authType:e.gAuthType,authCode:e.gAuthToken,key:e.gKey,secret:e.gSecret,loginType:0,reUseSid:e.gUserId})),null==e.gHBTimer&&(e.gHBTimer=setInterval(function(){console.log("------heartbeat 60 per second"),t.send((0,n.default)({opcode:"heartbeat"}))},6e4)),null==e.gClearTimer&&(e.gClearTimer=setInterval(function(){e.gMid=0,e.gMsg=""},3e3)),null!=e.gReloginTimer&&(clearInterval(e.gReloginTimer),e.gReloginTimer=null)},t.onmessage=function(t){e.onMessage(t.data)},t.onclose=function(t){console.log("ws.onclose isClear = ",e.isClear),g(e),null==e.gReloginTimer&&(console.log("setInterval relogin time isClear = ",e.isClear),e.callStateCB(c,0,"Connect to server failed",""),e.gReloginTimer=setInterval(function(){m(e)},1e4))},t.onerror=function(t){console.log("ws.onerror isClear = ",e.isClear),g(e),null==e.gReloginTimer&&(console.log("setInterval relogin time isClear = ",e.isClear),e.callStateCB(c,0,"Connect to server failed",""),e.gReloginTimer=setInterval(function(){m(e)},1e4))}}function g(e){!0!==e.isClear&&(e.isClear=!0,null!=e.gHBTimer&&(clearInterval(e.gHBTimer),e.gHBTimer=null),null!=e.gClearTimer&&(clearInterval(e.gClearTimer),e.gClearTimer=null))}function v(e){console.log(" failed to add ICE Candidate: "+e.toString())}p.prototype.callStateCB=function(e,t,i,n){console.log("callStateCB"),console.log("---gMid:"+this.gMid+"mid:"+e),console.log("---gMsg:"+this.gMsg+"msg:"+i),this.gMid!=e||this.gMsg!=i||7==e?(this.gMid=e,this.gMsg=i,null!=this.gStateCB&&this.gStateCB(e,t,i,n,this.gContext)):console.log("---gMid:"+this.gMid+"mid:"+e)},p.prototype.reset=(0,o.default)(a.default.mark(function e(){return a.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(this.gMid=0,this.gMsg="",this.isAnswer=!1,!this.pc){e.next=6;break}return e.next=6,this.pc.close();case 6:if(!this.localStream){e.next=9;break}return e.next=9,this.localStream.getTracks().forEach(function(e){e.stop()});case 9:case"end":return e.stop()}},e,this)})),p.prototype.openMediaDevice=function(){var e=(0,o.default)(a.default.mark(function e(t,i){var n;return a.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(t||i){e.next=2;break}return e.abrupt("return");case 2:return console.log("openMediaDevice"),e.prev=3,e.next=6,navigator.mediaDevices.getUserMedia({audio:t,video:i});case 6:return n=e.sent,console.log("---------Received local stream"),i&&(this.gLocalVideo.srcObject=n),e.abrupt("return",n);case 12:e.prev=12,e.t0=e.catch(3),console.log("----------getUserMedia() error: "+e.t0.name);case 15:case"end":return e.stop()}},e,this,[[3,12]])}));return function(t,i){return e.apply(this,arguments)}}(),p.prototype.createPC=function(){var e=this,t=this.localStream;if(t){var i=t.getVideoTracks(),n=t.getAudioTracks();i.length>0&&console.log("Using video device: "+i[0].label),n.length>0&&console.log("Using audio device: "+n[0].label)}var a={iceServers:[]};if(this.gStunAddr.length>0)for(var o=0;o<this.gStunAddr.length;o++){var s={urls:this.gStunAddr[o]};a.iceServers.push(s)}if(this.gTurnAddr.length>0)for(var r=0;r<this.gTurnAddr.length;r++){var l={urls:this.gTurnAddr[r],username:this.gTurnUser[r],credential:this.gTurnPass[r]};a.iceServers.push(l)}a.iceTransportPolicy="all",this.relayOnly&&(a.iceTransportPolicy="relay",console.log("relay only enabled"));var d=new RTCPeerConnection(a);return console.log("Created local peer connection"),d.addEventListener("icecandidate",function(t){return e.onIceCandidate(d,t)}),d.addEventListener("iceconnectionstatechange",function(t){return function(e,t){if(e.pc){console.log("ICE state: "+e.pc.iceConnectionState);var i=e.pc.iceConnectionState;"connected"==i?e.callStateCB(h,e.gUserId,e.pc.iceConnectionState,""):"disconnected"!=i&&"failed"!=i&&"closed"!=i||e.callStateCB(_,e.gUserId,e.pc.iceConnectionState,"")}}(e)}),d.addEventListener("track",function(t){return function(e,t){e.gRemoteVideo.srcObject!==t.streams[0]&&(e.gRemoteVideo.srcObject=t.streams[0],console.log("received remote stream"),e.callStateCB(u,e.gUserId,"received remote stream",""))}(e,t)}),t&&(t.getTracks().forEach(function(e){return d.addTrack(e,t)}),console.log("Added local stream to pc")),d},p.prototype.onCreateSessionDescriptionError=function(e){console.log("Failed to create session description: "+e.toString())},p.prototype.onCreateOfferSuccess=function(){var e=(0,o.default)(a.default.mark(function e(t,i){return a.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,t.setLocalDescription(i);case 3:console.log("setLocalDescription complete"),e.next=9;break;case 6:e.prev=6,e.t0=e.catch(0),this.onSetSessionDescriptionError(e.t0);case 9:case"end":return e.stop()}},e,this,[[0,6]])}));return function(t,i){return e.apply(this,arguments)}}(),p.prototype.onSetSessionDescriptionError=function(e){console.log("Failed to set session description: "+e.toString())},p.prototype.onRemoteCandidatesIncoming=function(){var e=(0,o.default)(a.default.mark(function e(t){return a.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return console.log("onRemoteCandidatesIncoming "),e.next=3,this.addIceCandidate(this.pc,t.candidate);case 3:case"end":return e.stop()}},e,this)}));return function(t){return e.apply(this,arguments)}}(),p.prototype.onRemoteCandidatesIncoming2=function(){var e=(0,o.default)(a.default.mark(function e(t){return a.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(console.log("onRemoteCandidatesIncoming2 isAnswer:",this.isAnswer),0!=this.isAnswer){e.next=4;break}return this.candidates.push(t),e.abrupt("return");case 4:return e.next=6,this.addIceCandidate(this.pc,t);case 6:case"end":return e.stop()}},e,this)}));return function(t){return e.apply(this,arguments)}}(),p.prototype.addIceCandidate=function(){var e=(0,o.default)(a.default.mark(function e(t,i){return a.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,t.addIceCandidate(i);case 3:e.next=8;break;case 5:e.prev=5,e.t0=e.catch(0),v(e.t0);case 8:case"end":return e.stop()}},e,this,[[0,5]])}));return function(t,i){return e.apply(this,arguments)}}(),p.prototype.onRemoteAnswerIncoming=function(){var e=(0,o.default)(a.default.mark(function e(t){var i;return a.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,this.pc.setRemoteDescription(t);case 3:i=0;case 4:if(!(i<this.candidates.length)){e.next=10;break}return e.next=7,this.onRemoteCandidatesIncoming2(this.candidates[i]);case 7:i++,e.next=4;break;case 10:this.candidates=[],console.log("onRemoteAnswerIncoming complete"),e.next=17;break;case 14:e.prev=14,e.t0=e.catch(0),this.onSetSessionDescriptionError(e.t0);case 17:case"end":return e.stop()}},e,this,[[0,14]])}));return function(t){return e.apply(this,arguments)}}(),p.prototype.handleLocalCandidate=function(e){e&&e.candidate.search("host")>=0&&e.candidate.search("tcp")>=0?console.log("drop host tcp candidate"):this.sendMessageToPeer("remoteCandidate",{type:"candidate",candidate:e})},p.prototype.onIceCandidate=function(){var e=(0,o.default)(a.default.mark(function e(t,i){return a.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:null===i.candidate?console.log("ice condidate gathered done"):this.handleLocalCandidate(i.candidate);case 1:case"end":return e.stop()}},e,this)}));return function(t,i){return e.apply(this,arguments)}}(),p.prototype.onMessage=function(){var e=(0,o.default)(a.default.mark(function e(t){var i,n,o;return a.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:i=JSON.parse(t),e.t0=i.opcode,e.next="signin"===e.t0?4:"message"===e.t0?15:"heartbeat"===e.t0?63:"error"===e.t0?65:68;break;case 4:if(0==i.code){e.next=7;break}return this.callStateCB(4,0,i.msg,""),e.abrupt("return");case 7:return this.gUserId=i.sid,this.gStunAddr=i.stunAddr,this.gTurnAddr=i.turnAddr,this.gTurnUser=i.turnUser,this.gTurnPass=i.turnPass,console.log("signIn got Sid "+this.gUserId),this.callStateCB(3,this.gUserId,"Login success",""),e.abrupt("break",68);case 15:if(n=i.messageType,0==i.code){e.next=19;break}return"historyResponse"==n?this.callStateCB(8,0,i.code,""):"forwardResponse"==n?this.callStateCB(10,0,i.code,""):"callResponse"==n?this.callStateCB(6,0,i.code,""):"switchAudio"==n?this.callStateCB(14,0,i.code,""):"switchVideo"==n?this.callStateCB(12,0,i.code,""):"hangup"==n&&this.callStateCB(16,0,i.code,""),e.abrupt("return");case 19:if(o=""===i.message?"":JSON.parse(i.message),"callResponse"!=n){e.next=31;break}if(console.log("incoming answer",o),"exceed"!=o.type){e.next=25;break}return this.callStateCB(6,0,"exceed",""),e.abrupt("break",68);case 25:return this.isAnswer=!0,this.callStateCB(5,0,"Call success",""),e.next=29,this.onRemoteAnswerIncoming(o);case 29:e.next=62;break;case 31:if("historyResponse"!=n){e.next=36;break}console.log("incoming history",o),this.callStateCB(7,0,"Get history success",i.message),e.next=62;break;case 36:if("forwardResponse"!=n){e.next=41;break}console.log("incoming forward",o),this.callStateCB(9,0,"Get forward success",i.message),e.next=62;break;case 41:if("remoteCandidate"!=n){e.next=47;break}return console.log("incoming candidates "),e.next=45,this.onRemoteCandidatesIncoming2(o);case 45:e.next=62;break;case 47:if("switchAudio"!=n){e.next=51;break}this.callStateCB(13,0,i.code,i.message),e.next=62;break;case 51:if("switchVideo"!=n){e.next=55;break}this.callStateCB(11,0,i.code,i.message),e.next=62;break;case 55:if("hangup"!=n){e.next=59;break}this.callStateCB(15,0,i.code,""),e.next=62;break;case 59:return this.callStateCB(-1,0,"Unknow",""),console.log("unknow message "+o),e.abrupt("return");case 62:return e.abrupt("break",68);case 63:return console.log("recv heartbeat respone"),e.abrupt("break",68);case 65:return this.reset(),console.log("recv error from server"),e.abrupt("break",68);case 68:case"end":return e.stop()}},e,this)}));return function(t){return e.apply(this,arguments)}}(),p.prototype.sendMessageToPeer=function(){var e=(0,o.default)(a.default.mark(function e(t,i){return a.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:this.sendMessageToPeerEx(t,i,!1);case 1:case"end":return e.stop()}},e,this)}));return function(t,i){return e.apply(this,arguments)}}(),p.prototype.sendMessageToPeerEx=function(){var e=(0,o.default)(a.default.mark(function e(t,i,o){var s;return a.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(!this.isHangup){e.next=2;break}return e.abrupt("return");case 2:if(void 0!==this.peerId&&""!=this.peerId){e.next=5;break}return console.log("no peerid"),e.abrupt("return");case 5:return s={opcode:"message",messageType:t,sid:this.gUserId,serialCode:"",toSid:0,toSerialCode:this.peerId,permitCode:this.permitCode,message:(0,n.default)(i)},o&&(s.isHistory=!0),e.next=9,this.gWSConn.send((0,n.default)(s));case 9:console.log("sendMessage ",s);case 10:case"end":return e.stop()}},e,this)}));return function(t,i,n){return e.apply(this,arguments)}}(),t.default=p},evNe:function(e,t,i){"use strict";var n=i("WBkj");i.n(n).a},exBM:function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=function(e){return e&&e.__esModule?e:{default:e}}(i("zGwZ"));t.default={name:"Page401",data:function(){return{errGif:n.default+"?"+ +new Date,ewizardClap:"https://wpimg.wallstcn.com/007ef517-bafd-4066-aae4-6883632d9646",dialogVisible:!1}},methods:{back:function(){this.$route.query.noGoBack?this.$router.push({path:"/home"}):this.$router.go(-1)}}}},f1Gg:function(e,t,i){"use strict";i.r(t);var n=i("gDd4"),a=i.n(n);for(var o in n)"default"!==o&&function(e){i.d(t,e,function(){return n[e]})}(o);t.default=a.a},fY3g:function(e,t,i){"use strict";i.r(t);var n=i("UMfv"),a=i.n(n);for(var o in n)"default"!==o&&function(e){i.d(t,e,function(){return n[e]})}(o);t.default=a.a},fe1z:function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.loginAddr=function(e){return(0,n.default)({url:"v1/miru/account/password/change",method:"post",data:e})};var n=function(e){return e&&e.__esModule?e:{default:e}}(i("t3Un"))},gDd4:function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=c(i("14Xm")),a=c(i("4d7F")),o=c(i("D3Ub")),s=i("LhkO"),r=i("7Qib"),l=c(i("Mz3J")),d=c(i("CMXa"));function c(e){return e&&e.__esModule?e:{default:e}}t.default={name:"UserMgt",mixins:[d.default],components:{Pagination:l.default},data:function(){return{list_loading:!1,user_list:[],multipleSelection:[],listTitle:[{title:this.$t("account"),name:"account",width:"260px"},{title:this.$t("nickName"),name:"nickName",width:"260px"},{title:this.$t("mark"),name:"mark",width:"260px"}],dialogVisible:!1,dialogTitle:"提示",userForm:{data:{account:"",password:"",nickName:"",mark:"",account_type:3},rules:{account:[{required:!0,message:this.$t("inputLengthLimit",{limit:"6-20"}),trigger:"blur",max:20,min:6}],password:[{required:!0,message:this.$t("pleaseEnterCorrectPwd18"),trigger:"blur",min:6,max:18}]}},isSubmitting:!1,deleteUserAccount:"0",resetPwdUserAccount:"0",total:0,listQuery:{limit:10,page:1},accountTypeRange:[{value:3,label:this.$t("manager")},{value:4,label:this.$t("guest")}]}},created:function(){this.handleAutoRelogin()},methods:{handleColumnShow:r.handleColumnShow,onReloginSuccess:function(e){console.log("userMgt页面重登成功"),this.getUsers()},onWebsocketReady:function(){console.log("userMgt页面websocket准备就绪"),this.getUsers()},getUsers:function(){var e=this;this.list_loading=!0,(0,s.getUserList)({offset:(this.listQuery.page-1)*this.listQuery.limit,limit:this.listQuery.limit}).then(function(t){if(200===t.code){var i=t.datas.map(function(e){return{account:e.account,nickName:e.name,mark:e.remark,account_type:e.account_type}});e.user_list=i,e.total=t.count}}).catch(function(e){console.log(e)}).finally(function(){e.list_loading=!1})},addUser:function(){this.dialogTitle=this.$t("addUser"),this.showDialog()},delBulkUser:function(){this.dialogTitle=this.$t("bulkDelete"),this.multipleSelection.length<=0?this.$message.info(this.$t("bulkDelUserTip")):(this.showDialog(),this.deleteUserAccount=this.multipleSelection.map(function(e){return e.account}).join(","))},showDialog:function(){this.dialogVisible=!0},hideDialog:function(){var e=this;this.dialogVisible=!1,setTimeout(function(){e.clearFormData()},100)},clearFormData:function(){this.userForm.data={account:"",password:"",nickName:"",mark:"",account_type:3}},addEditUser:function(e){this.userForm.data={account:e.account,password:"",nickName:e.nickName,mark:e.mark},e&&(this.dialogTitle=this.$t("editUser"),this.showDialog())},delUser:function(e){e&&(this.dialogTitle=this.$t("deleteUser"),this.showDialog(),this.deleteUserAccount=e.account)},resetPasswordUser:function(e){e&&(this.dialogTitle=this.$t("resetPassword"),this.showDialog(),this.resetPwdUserAccount=e.account)},submit:function(){var e=this;if(this.dialogTitle===this.$t("editUser")||this.dialogTitle===this.$t("addUser"))this.$refs.userForm.validate(function(t){if(t){var i=e.userForm.data;e.isSubmitting=!0;var r={account:i.account,name:i.nickName,remark:i.mark,account_type:i.account_type};if(e.dialogTitle===e.$t("addUser")&&(r.password=e.$md5(i.password),e.user_list.length>4))return e.isSubmitting=!1,e.$message.warning(e.$t("userLimitTip"));(function(){var t=(0,o.default)(n.default.mark(function t(){return n.default.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.abrupt("return",new a.default(function(t){e.dialogTitle===e.$t("editUser")?t((0,s.editUser)(r)):t((0,s.addUser)(r))}));case 1:case"end":return t.stop()}},t,e)}));return function(){return t.apply(this,arguments)}})()(r).then(function(t){200===t.code&&(e.getUsers(),e.hideDialog(),e.$message.success(e.$t(e.dialogTitle===e.$t("editUser")?"editSuccess":"addSuccess")))}).catch(function(e){console.log(e)}).finally(function(){e.isSubmitting=!1})}});else if(this.isSubmitting=!0,this.dialogTitle===this.$t("deleteUser")||this.dialogTitle===this.$t("bulkDelete")){var t={account:this.deleteUserAccount};(0,s.deleteUser)(t).then(function(t){200===t.code&&(e.getUsers(),e.hideDialog(),e.$message.success(e.$t("deleteSuccess")))}).finally(function(){e.isSubmitting=!1,e.multipleSelection=[]})}else{var i={account:this.resetPwdUserAccount};(0,s.resetPassword)(i).then(function(t){200===t.code&&(e.hideDialog(),e.$message.success(e.$t("modifySuccess")))}).finally(function(){e.isSubmitting=!1})}},onAccountInput:function(e){var t=e.replace(/[^A-Z0-9]/gi,"");this.userForm.data.account=t},handleSelectionChange:function(e){this.multipleSelection=e,console.log(this.multipleSelection)},handlePaginationChange:function(e){this.listQuery={page:e.page,limit:e.limit},this.getUsers()}}}},gXRY:function(e,t,i){"use strict";i.r(t);var n=i("BnOw"),a=i("tfGf");for(var o in a)"default"!==o&&function(e){i.d(t,e,function(){return a[e]})}(o);i("3KQq");var s=i("KHd+"),r=Object(s.a)(a.default,n.a,n.b,!1,null,"3d8e62e2",null);r.options.__file="src\\views\\operationLogs\\index.vue",t.default=r.exports},glkp:function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=s(i("ETGp")),a=i("X4fA"),o=s(i("EKno"));function s(e){return e&&e.__esModule?e:{default:e}}t.default={components:{LangSelect:n.default,TipDialog:o.default},inject:["reload"],data:function(){return{language:(0,a.getLanguage)(),dialogTitle:"",dialogTip:"",tipRequest:"",tipParams:{},selected_menu_flag:"home",isSuperAdmin:!1,isNornalAdmin:!1,username:sessionStorage.account_alias?sessionStorage.account_alias:sessionStorage.username}},computed:{},created:function(){var e=this;this.selected_menu_flag=this.$route.name,this.isSuperAdmin="1"==sessionStorage.account_type,this.isNornalAdmin="3"==sessionStorage.account_type,this.$bus.$on("refreshUserInfo",function(t){e.username=t})},methods:{handleSetLanguage:function(e){this.language=e},toggleMenu:function(e){this.selected_menu_flag=e,this.$router.push({path:e})},logoutDialog:function(){this.dialogTitle=this.$t("quitTitle"),this.dialogTip=this.$t("quitTip"),this.tipRequest="cli_logout",this.tipParams={},this.$refs.tipDialog.show("logout")},backTip:function(e){}}}},h3eN:function(e,t,i){"use strict";var n=function(){var e=this,t=e.$createElement;return(e._self._c||t)("canvas",{attrs:{id:"captchaCanvas",width:e.width,height:e.height},on:{click:function(t){t.stopPropagation(),e.generateCaptcha()}}})},a=[];n._withStripped=!0,i.d(t,"a",function(){return n}),i.d(t,"b",function(){return a})},hZMg:function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.changePassword=function(e){return(0,n.default)({url:"v1/miru/account/password/change",method:"post",data:e})},t.changeName=function(e){return(0,n.default)({url:"v1/miru/account/rename",method:"post",data:e})};var n=function(e){return e&&e.__esModule?e:{default:e}}(i("t3Un"))},iIxO:function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default={name:"Captcha",props:{length:{type:Number,default:6},width:{type:Number,default:120},height:{type:Number,default:40}},data:function(){return{}},mounted:function(){this.generateCaptcha()},methods:{generateCaptcha:function(){var e=document.getElementById("captchaCanvas"),t=e.getContext("2d");t.clearRect(0,0,e.width,e.height);var i=this.generateRandomText(this.length);this.$emit("captchaText",i),this.drawCaptcha(t,e,i)},generateRandomText:function(e){for(var t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",i="",n=0;n<e;n++)i+=t.charAt(Math.floor(Math.random()*t.length));return i},drawCaptcha:function(e,t,i){e.font="20px Arial",e.fillStyle="#333",e.textBaseline="middle",e.textAlign="center";var n=t.width/2,a=t.height/2;e.fillStyle="#f0f0f0",e.fillRect(0,0,t.width,t.height),e.fillStyle="#333",e.fillText(i,n,a);for(var o=0;o<5;o++)e.beginPath(),e.moveTo(Math.random()*t.width,Math.random()*t.height),e.lineTo(Math.random()*t.width,Math.random()*t.height),e.strokeStyle="#ccc",e.stroke()}}}},iWp0:function(e,t,i){e.exports=i.p+"static/img/deleteIcon.b9b09c2.png"},iqJH:function(e,t,i){"use strict";var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"systemSettingBox"},[i("div",{staticClass:"systemSettingLayout"},[i("div",{staticClass:"settingMain"},[i("div",{staticClass:"settingLine"},[i("span",{staticClass:"lineTitle"},[e._v(e._s(e.$t("account")))]),e._v(" "),i("span",{staticClass:"lineContent"},[e._v(e._s(e.account))])]),e._v(" "),i("div",{staticClass:"settingLine"},[i("span",{staticClass:"lineTitle"},[e._v(e._s(e.$t("nickName")))]),e._v(" "),i("el-input",{staticClass:"lineContent",attrs:{maxlength:"30",placeholder:e.$t("inputLengthLimit",{limit:30})},model:{value:e.nickName,callback:function(t){e.nickName=t},expression:"nickName"}})],1)]),e._v(" "),i("div",{staticClass:"settingFooter"},[i("el-button",{staticClass:"tipSubmitBtn",attrs:{type:"primary",loading:e.isSaving},on:{click:function(t){e.save()}}},[e._v(e._s(e.$t("save")))]),e._v(" "),i("el-button",{staticClass:"tipCancelBtn",attrs:{type:"info"},on:{click:function(t){e.modifyPassword()}}},[e._v(e._s(e.$t("modifyPassword")))])],1)]),e._v(" "),i("el-dialog",{attrs:{title:e.dialogTitle,visible:e.dialogVisible,width:"600px","before-close":e.hideDialog},on:{"update:visible":function(t){e.dialogVisible=t}}},[e.dialogTitle===e.$t("modifyPassword")?[i("el-form",{ref:"modifyPasswordDialogForm",attrs:{rules:e.modifyPasswordDialogForm.rules,model:e.modifyPasswordDialogForm.data,"label-width":"240px","label-position":"left"}},[i("el-form-item",{attrs:{label:e.$t("oldPassword"),prop:"oldPassword"}},[i("el-input",{attrs:{placeholder:e.$t("passwordTip1"),maxlength:"18",type:"password","show-password":""},model:{value:e.modifyPasswordDialogForm.data.oldPassword,callback:function(t){e.$set(e.modifyPasswordDialogForm.data,"oldPassword",t)},expression:"modifyPasswordDialogForm.data.oldPassword"}})],1),e._v(" "),i("el-form-item",{attrs:{label:e.$t("newPassword"),prop:"newPassword"}},[i("el-input",{attrs:{placeholder:e.$t("inputLengthLimit",{limit:"6-18"}),maxlength:"18",type:"password","show-password":""},model:{value:e.modifyPasswordDialogForm.data.newPassword,callback:function(t){e.$set(e.modifyPasswordDialogForm.data,"newPassword",t)},expression:"modifyPasswordDialogForm.data.newPassword"}})],1),e._v(" "),i("el-form-item",{attrs:{label:e.$t("checkPassword"),prop:"confirmPassword"}},[i("el-input",{attrs:{placeholder:e.$t("inputLengthLimit",{limit:"6-18"}),maxlength:"18",type:"password","show-password":""},model:{value:e.modifyPasswordDialogForm.data.confirmPassword,callback:function(t){e.$set(e.modifyPasswordDialogForm.data,"confirmPassword",t)},expression:"modifyPasswordDialogForm.data.confirmPassword"}})],1)],1)]:e._e(),e._v(" "),i("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{staticClass:"tipSubmitBtn",attrs:{loading:e.isSubmitting},on:{click:function(t){e.submit()}}},[e._v(e._s(e.$t("sure")))]),e._v(" "),i("el-button",{staticClass:"tipCancelBtn",attrs:{type:"info"},on:{click:function(t){e.hideDialog()}}},[e._v(e._s(e.$t("cancel")))])],1)],2)],1)},a=[];n._withStripped=!0,i.d(t,"a",function(){return n}),i.d(t,"b",function(){return a})},iz5v:function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=h(i("14Xm")),a=h(i("4d7F")),o=h(i("D3Ub")),s=h(i("Mz3J")),r=(i("7Qib"),i("5Hul")),l=i("OU4T"),d=i("IJUe"),c=h(i("EKno")),u=h(i("CMXa"));function h(e){return e&&e.__esModule?e:{default:e}}t.default={name:"VideoMgt",mixins:[u.default],components:{Pagination:s.default,TipDialog:c.default},data:function(){return{groupList:[{value:"-1",label:this.$t("all")}],group:"-1",searchText:"",operationTime:[],allChecked:!1,isHasSomeChecked:!1,checkedList:[],rowList:[],total:0,listQuery:{page:1,limit:10},showVideoDialog:!1,currentVideo:{name:"",group:"",timeText:"",videoUrl:"",videoId:""},videoLoading:!1,dialogTitle:"",dialogTip:"",tipRequest:"",tipParams:{},isLoading:!1}},created:function(){var e=new Date,t=new Date(e);t.setDate(e.getDate()-6),this.operationTime=[t,e],this.handleAutoRelogin()},mounted:function(){document.addEventListener("keydown",this.handleKeyDown)},beforeDestroy:function(){document.removeEventListener("keydown",this.handleKeyDown)},methods:{onReloginSuccess:function(e){console.log("videoMgt页面重登成功"),this.getGroup(),this.search()},onWebsocketReady:function(){console.log("videoMgt页面websocket准备就绪"),this.getGroup(),this.search()},getGroup:function(){var e=this;(0,l.getDeviceGroupList)().then(function(t){200===t.code&&t.datas&&t.datas.map(function(t,i){e.groupList.push({value:t.group_id,label:t.name})})})},handleKeyDown:function(e){"Escape"===e.key&&this.showVideoDialog&&this.closeVideoDialog()},onChangeSelect:function(e){this.searchText="",this.search()},onTimeChange:function(e){},search:function(){var e=this,t=void 0,i=void 0;try{(t=new Date(this.operationTime[0])).setHours(0,0,0,0),(i=new Date(this.operationTime[1])).setHours(0,0,0,0),i.setDate(i.getDate()+1)}catch(e){i=new Date,(t=new Date(i)).setDate(i.getDate()-6),this.operationTime=[t,i]}var n={begin_time:Math.round(t.valueOf()/1e3),end_time:Math.round(i.valueOf()/1e3),offset:(this.listQuery.page-1)*this.listQuery.limit,limit:this.listQuery.limit,group_id:"-1"!=this.group?this.group:"",text:this.searchText};this.isLoading=!0,this.checkedList=[],this.allChecked=!1,(0,r.getVideoList)(n).then(function(t){if(200===t.code){e.total=t.count;var i=t.datas.map(function(e){return{checked:!1,name:e.camera_name,group:e.group_name,timeText:(0,d.formatTime)(e.begin_time)+" ~ "+(0,d.formatTime)(e.end_time),imageUrl:e.image_url,videoUrl:e.file_url,videoId:e.video_id}});e.rowList=i}}).catch(function(e){console.error("获取视频列表失败:",e)}).finally(function(){e.isLoading=!1})},handleAllChecked:function(e){this.allChecked=e},handleRowChecked:function(e){var t=this.rowList.some(function(e){return e.checked}),i=this.rowList.some(function(e){return!e.checked});this.isHasSomeChecked=t&&i,this.checkedList=this.rowList.filter(function(e){return e.checked}),this.allChecked=t&&!i},handlePaginationChange:function(e){this.listQuery={page:e.page,limit:e.limit},this.search()},handlePlayVideo:function(e){this.currentVideo={name:e.name,group:e.group,timeText:e.timeText,videoUrl:e.videoUrl,videoId:e.videoId},this.showVideoDialog=!0,this.videoLoading=!0,this.$nextTick(function(){var e=document.querySelector(".video-dialog-overlay");e&&e.focus()})},closeVideoDialog:function(){this.showVideoDialog=!1,this.videoLoading=!1,this.$refs.videoPlayer&&(this.$refs.videoPlayer.pause(),this.$refs.videoPlayer.currentTime=0),this.currentVideo={name:"",group:"",timeText:"",videoUrl:"",videoId:""}},onVideoLoadStart:function(){this.videoLoading=!0},onVideoCanPlay:function(){this.videoLoading=!1},onVideoError:function(e){this.videoLoading=!1},downloadVideos:function(e){var t=this;return(0,o.default)(n.default.mark(function i(){var o,s,r;return n.default.wrap(function(i){for(;;)switch(i.prev=i.next){case 0:if(!(t.checkedList.length<=0)||e){i.next=3;break}return t.$message.error(t.$t("pleaseSelect")),i.abrupt("return");case 3:return o=e?[e.videoUrl]:t.checkedList.map(function(e){return e.videoUrl}),s=e?[e.name]:t.checkedList.map(function(e){return e.name}),r=t.$message({message:t.$t("downloading")||"正在下载...",type:"info",duration:0}),i.prev=6,i.next=9,a.default.all(o.map(function(e,i){return t.downloadSingleVideo(e,s[i])}));case 9:r.close(),t.$message.success(t.$t("downloadSuccess")||"下载成功"),i.next=18;break;case 13:i.prev=13,i.t0=i.catch(6),r.close(),console.error("下载失败:",i.t0),t.$message.error(t.$t("downloadFailed")||"下载失败");case 18:case"end":return i.stop()}},i,t,[[6,13]])}))()},downloadSingleVideo:function(e,t){var i=this;return(0,o.default)(n.default.mark(function t(){var a,o,s,r,l;return n.default.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,fetch(e);case 3:if((a=t.sent).ok){t.next=6;break}throw new Error("HTTP error! status: "+a.status);case 6:return t.next=8,a.blob();case 8:o=t.sent,s=window.URL.createObjectURL(o),(r=document.createElement("a")).href=s,(l=e.split("/").pop()).includes(".")||(l+=".mp4"),r.download=l,document.body.appendChild(r),r.click(),document.body.removeChild(r),window.URL.revokeObjectURL(s),t.next=25;break;case 21:throw t.prev=21,t.t0=t.catch(0),console.error("下载单个视频失败:",t.t0),t.t0;case 25:case"end":return t.stop()}},t,i,[[0,21]])}))()},deleteRows:function(e){var t=this;this.checkedList.length<=0&&!e?this.$message.error(this.$t("pleaseSelect")):(this.dialogTitle=this.$t("delete"),this.dialogTip=this.$t("sureToDelete"),this.tipRequest="deleteVideos",this.tipParams={video_id:e?[e.videoId]:this.checkedList.map(function(e){return e.videoId})},this.$refs.tipDialog.show("deleteRows").then(function(e){e&&(0,r.deleteVideos)(t.tipParams).then(function(e){200===e.code&&setTimeout(function(){t.$refs.tipDialog.hideDialog(),t.$message.success(t.$t("deleteSuccess")),t.search()},500)})}))}},computed:{checkedAll:{get:function(){return this.allChecked},set:function(e){this.allChecked=e,this.rowList.map(function(t){t.checked=e}),this.isHasSomeChecked=!1,this.checkedList=e?this.rowList:[]}}}}},"jF/l":function(e,t,i){"use strict";i.r(t);var n=i("R7BX"),a=i.n(n);for(var o in n)"default"!==o&&function(e){i.d(t,e,function(){return n[e]})}(o);t.default=a.a},jJqW:function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=i("OOIL");t.default={name:"Layout",components:{Navbar:n.Navbar,AppMain:n.AppMain}}},jYtb:function(e,t,i){e.exports=i.p+"static/img/arrow_next.e7083f4.png"},jhCA:function(e,t,i){e.exports=i.p+"static/img/playIcon.05a4bf2.png"},"k/VT":function(e,t,i){"use strict";var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-dialog",{attrs:{title:e.title,visible:e.dialogVisible,"close-on-click-modal":!1,"append-to-body":"","before-close":e.handleCancelIcon},on:{"update:visible":function(t){e.dialogVisible=t},close:e.hideDialog}},[i("section",[i("div",{staticClass:"content text-center marBtm20"},[e._v("\n      "+e._s(e.tip)+"\n    ")])]),e._v(" "),i("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{staticClass:"tipSubmitBtn noFlex",attrs:{loading:e.loading,type:"primary"},on:{click:e.submit}},[e._v(e._s(e.one_btn_text))])],1)])},a=[];n._withStripped=!0,i.d(t,"a",function(){return n}),i.d(t,"b",function(){return a})},k2lJ:function(e,t,i){"use strict";var n=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"login-container"},[n("div",{staticClass:"headBox"},[n("div",{staticClass:"headLeftBox"},[n("img",{staticClass:"logo",attrs:{src:i("FkO0")}}),e._v(" "),n("div",[n("div",{staticClass:"platName"},[e._v(e._s(e.$t("mgtPlat")))]),e._v(" "),n("div",{staticClass:"platIntro"},[e._v(e._s(e.$t("mgtPlatTip")))])])]),e._v(" "),n("div",{staticClass:"headRight"},[n("lang-select",{staticClass:"set-language",on:{handleSetLanguage:e.handleSetLanguage}})],1)]),e._v(" "),n("div",{staticClass:"contentBox"},[n("el-form",{ref:"loginForm",staticClass:"formBox",attrs:{model:e.loginForm,rules:e.loginRules}},[n("div",{staticClass:"mainLogoBox marginAuto marBtm20"},[n("img",{attrs:{src:i("FkO0")}})]),e._v(" "),n("el-form-item",{staticClass:"marBtm20",attrs:{prop:"username"}},[n("el-input",{attrs:{placeholder:e.$t("account"),maxlength:20,name:"username",type:"text","auto-complete":"on"},model:{value:e.loginForm.username,callback:function(t){e.$set(e.loginForm,"username",t)},expression:"loginForm.username"}})],1),e._v(" "),n("el-form-item",{staticClass:"marBtm20",attrs:{prop:"password"}},[e.showPwd?n("el-input",{attrs:{placeholder:e.$t("password"),maxlength:18,type:"text",name:"password","auto-complete":"on"},nativeOn:{keyup:function(t){return"button"in t||!e._k(t.keyCode,"enter",13,t.key,"Enter")?e.handleLogin(t):null}},model:{value:e.loginForm.password,callback:function(t){e.$set(e.loginForm,"password",t)},expression:"loginForm.password"}},[n("i",{staticClass:"eyeStyle viewIcon",attrs:{slot:"suffix"},on:{click:function(t){e.changeEye("close")}},slot:"suffix"})]):n("el-input",{attrs:{placeholder:e.$t("password"),maxlength:18,type:"password",name:"password","auto-complete":"on"},nativeOn:{keyup:function(t){return"button"in t||!e._k(t.keyCode,"enter",13,t.key,"Enter")?e.handleLogin(t):null}},model:{value:e.loginForm.password,callback:function(t){e.$set(e.loginForm,"password",t)},expression:"loginForm.password"}},[n("i",{staticClass:"eyeStyle eyeIcon",attrs:{slot:"suffix"},on:{click:function(t){e.changeEye("open")}},slot:"suffix"})])],1),e._v(" "),n("el-form-item",{staticClass:"marBtm20",attrs:{prop:"verification_code"}},[n("el-input",{attrs:{maxlength:4,placeholder:e.$t("verificationCode"),type:"text"},nativeOn:{keyup:function(t){return"button"in t||!e._k(t.keyCode,"enter",13,t.key,"Enter")?e.handleLogin(t):null}},model:{value:e.loginForm.verification_code,callback:function(t){e.$set(e.loginForm,"verification_code",t)},expression:"loginForm.verification_code"}},[n("i",{attrs:{slot:"suffix"},slot:"suffix"},[n("captcha",{ref:"captchaImg",staticClass:"captchaBox pointer",attrs:{length:4,width:87,height:36},on:{captchaText:e.getCaptchaText}})],1)])],1),e._v(" "),n("el-form-item",{staticClass:"formItemBox marBtm20"},[n("el-checkbox",{model:{value:e.loginForm.saveUserInfoEnable,callback:function(t){e.$set(e.loginForm,"saveUserInfoEnable",t)},expression:"loginForm.saveUserInfoEnable"}},[e._v(e._s(e.$t("rememberPwd")))])],1),e._v(" "),n("el-button",{staticClass:"loginBtn primaryBtn",class:{primaryDisabledBtn:""===e.loginForm.username.trim()||""===e.loginForm.password},attrs:{loading:e.loading,disabled:""===e.loginForm.username.trim()||""===e.loginForm.password,type:"primary"},nativeOn:{click:function(t){return t.preventDefault(),e.handleLogin(t)}}},[e._v(e._s(e.$t("logIn")))])],1)],1)])},a=[];n._withStripped=!0,i.d(t,"a",function(){return n}),i.d(t,"b",function(){return a})},kQ8I:function(e,t,i){"use strict";i.r(t);var n=i("78Ql"),a=i("97gW");for(var o in a)"default"!==o&&function(e){i.d(t,e,function(){return a[e]})}(o);i("PYSj");var s=i("KHd+"),r=Object(s.a)(a.default,n.a,n.b,!1,null,"71a4aabe",null);r.options.__file="src\\views\\deviceMgt\\deviceAddDialog.vue",t.default=r.exports},kl8X:function(e,t,i){e.exports=i.p+"static/img/topArrow.9a2279e.png"},lNWa:function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0});t.default={language:function(e){return e.app.language},size:function(e){return e.app.size},token:function(e){return e.user.token},role:function(e){return e.user.role},roles:function(e){return e.user.roles},permission_routers:function(e){return e.permission.routers},addRouters:function(e){return e.permission.addRouters}}},lTTe:function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=i("7Qib"),a=i("Yu0S"),o=i("IJUe"),s=l(i("Mz3J")),r=l(i("CMXa"));function l(e){return e&&e.__esModule?e:{default:e}}t.default={name:"logs",mixins:[r.default],components:{Pagination:s.default},data:function(){return{operationTime:[],operationType:0,operationTypeRange:[{value:0,label:this.$t("all")},{value:1,label:this.$t("logIn")},{value:2,label:this.$t("editDeviceGroup")},{value:3,label:this.$t("addUser")},{value:4,label:this.$t("editUser")},{value:5,label:this.$t("deleteUser")},{value:6,label:this.$t("resetPassword")},{value:7,label:this.$t("modifyPassword")}],list_loading:!1,listTitle:[{title:this.$t("operationTime"),name:"operationTime",width:240},{title:this.$t("account"),name:"account",width:200},{title:this.$t("operationType"),name:"operationType",width:240},{title:this.$t("operationContent"),name:"operationContent"}],logsList:[],total:0,listQuery:{limit:20,page:1}}},created:function(){var e=new Date,t=new Date(e);t.setDate(e.getDate()-6),this.operationTime=[t,e],this.handleAutoRelogin()},methods:{handleColumnShow:n.handleColumnShow,onReloginSuccess:function(e){console.log("operationLogs页面重登成功"),this.search()},onWebsocketReady:function(){console.log("operationLogs页面websocket准备就绪"),this.search()},search:function(){var e=this,t=void 0,i=void 0;try{(t=new Date(this.operationTime[0])).setHours(0,0,0,0),(i=new Date(this.operationTime[1])).setHours(0,0,0,0),i.setDate(i.getDate()+1)}catch(e){i=new Date,(t=new Date(i)).setDate(i.getDate()-6),this.operationTime=[t,i]}var n={begin_time:Math.round(t.valueOf()/1e3),end_time:Math.round(i.valueOf()/1e3),operate_type:this.operationType,offset:(this.listQuery.page-1)*this.listQuery.limit,limit:this.listQuery.limit};this.list_loading=!0,(0,a.getLogs)(n).then(function(t){if(200===t.code){var i=t.datas;e.logsList=i.map(function(t){var i=(0,o.formatTime)(t.time,1),n="",a=t.operate_type;1===a?n=e.$t("logIn"):2===a?n=e.$t("editDeviceGroup"):3===a?n=e.$t("addUser"):4===a?n=e.$t("editUser"):5===a?n=e.$t("deleteUser"):6===a?n=e.$t("resetPassword"):7===a&&(n=e.$t("modifyPassword"));var s="",r=JSON.parse(t.operate_content);for(var l in r)"account"===l?s+=e.$t("account")+": "+r[l]+" ":"alias"===l?s+=e.$t("nickName")+": "+r[l]+" ":"group_name"===l&&(s+=e.$t("groupName")+": "+r[l]+" ");return{account:t.account,operationTime:i,operationType:n,operationContent:s}}),e.total=t.count,console.log(e.total,e.listQuery)}}).catch(function(e){}).finally(function(){e.list_loading=!1})},handlePaginationChange:function(e){this.listQuery={page:e.page,limit:e.limit},this.search()},onChangeSelect:function(e){this.listQuery={page:1,limit:20},this.search()}}}},mF2h:function(e,t,i){"use strict";var n=o(i("Kw5r")),a=o(i("wAo7"));function o(e){return e&&e.__esModule?e:{default:e}}n.default.component("svg-icon",a.default);!function(e){e.keys().map(e)}(i("Uf/o"))},mSNy:function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=f(i("QbLZ")),a=f(i("Kw5r")),o=f(i("qSUR")),s=f(i("p46w")),r=f(i("stYL")),l=f(i("8NkQ")),d=f(i("3M0M")),c=f(i("P6u4")),u=f(i("nfZ2")),h=f(i("tiNe")),_=f(i("zfL3"));function f(e){return e&&e.__esModule?e:{default:e}}a.default.use(o.default);var p={en:(0,n.default)({},c.default,r.default),zh:(0,n.default)({},u.default,l.default),ky:(0,n.default)({},h.default,r.default),ru:(0,n.default)({},_.default,d.default)},m=(navigator.language||navigator.browserLanguage).toLowerCase(),g=new o.default({locale:s.default.get("language")||(m.includes("ru")?"ru":m.includes("en")?"en":"ky"),messages:p});s.default.set("language",g.locale),t.default=g},mYxE:function(e,t,i){"use strict";i.r(t);var n=i("upXb"),a=i.n(n);for(var o in n)"default"!==o&&function(e){i.d(t,e,function(){return n[e]})}(o);t.default=a.a},nfZ2:function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default={mgtPlat:"MIRU",mgtPlatTip:"实时视频监控系统",scanCodeToLogIn:"扫码登录",scanCodeTip:"使用绑定了设备的APP进行扫码登录",qrcodeExpired:"二维码已失效",username:"用户名",password:"密码",passwordTip1:"请输入密码",rememberPwd:"记住密码",pleaseEnterAccount:"请输入账号",pleaseEnterCorrectPwd:"密码必须为6~128位字符！",logIn:"登录",account:"账号",accountTip:"账号限制输入6-20位的数字或大小写字母",pleaseEnterCorrectPwdLen:"密码长度必须≥6位",pleaseEnterCorrectPwd18:"密码必须为6~18位字符",verificationCode:"验证码",verificationCodeTip:"请输入验证码",verificationCodeError:"验证码错误",home:"首页",deviceManagement:"设备管理",userManagement:"用户管理",systemSetting:"系统设置",operationLogs:"操作日志",quitTitle:"退出登录",quitTip:"确定要退出吗？",submit:"提 交",confirm:"确 认",sure:"确 定",cancel:"取 消",search:"搜 索",inquire:"查 询",nodata:"无数据",cannotEmpty:"不能为空",operate:"操作",modify:"修改",export:"导出",edit:"编辑",delete:"删除",sureToDelete:"确定要删除吗？",deleteSuccess:"删除成功",addSuccess:"添加成功",addFail:"添加失败",remove:"移除",sureToRemove:"确定要移除吗？",removeSuccess:"移除成功",editSuccess:"编辑成功",tip:"提示",noMore:"没有更多了",getLogSuccess:"获取日志成功",all:"全部",modifySuccess:"修改成功",noData:"暂无数据",refresh:"刷新",save:"保存",to:"至",startDate:"开始日期",endDate:"结束日期",pleaseSelect:"请选择",errCode120:"未知错误",errCode121:"数据库错误",errCode122:"会话超时",errCode123:"消息格式错误",errCode124:"消息速率超过限制，请控制合理流速（100个每秒）",errCode125:"参数不对",errCode126:"调用robot API出现错误",errCode127:"vid vkey 错误",errCode128:"调用robot API返回数据错误",errCode129:"未登录，需要登录才能进行此操作",errCode161:"帐号或密码错误",errCode165:"账号已在其他设备登录",errCode200:"CID不存在",errCode202:"设备别名已存在",errCode203:"设备未绑定",errCode204:"设备已经被其他账号绑定",errCode205:"设备验证码不匹配",errCode206:"设备被自己绑定",errCode214:"设备已经被其他APP端执行绑定操作",errCode215:"设备不在线",errCode290:"随机码不存在",errCode291:"随机码已失效",errCode292:"状态不正确",errCode401:"请重新登录",errCode500:"服务器错误",errCode1000:"请求错误",errCode1001:"请求错误",errCode1002:"没有权限",errCode1004:"连接服务器失败",errCode1100:"名称重复",errCode1101:"账号已存在",errCode1102:"当前密码不正确",errCode2008:"SD卡格式化过程中",errCode2011:"SD卡读取失败",errCode2030:"SD卡历史录像已读完",errCode2031:"SD卡历史录像读取失败",errCode2032:"SD卡历史录像卡读取失败",timeout:"网络异常",networkError:"网络异常",allGroup:"全部分组",noDevice:"暂无设备",online:"在线",offline:"离线",deviceLoading:"加载设备列表···",videoLoading:"视频加载中···",failPlay:"播放失败",deviceOffline:"设备离线",noHistoricalVideo:"设备暂无历史视频",noOssVideo:"设备暂无云存储",retry:"重试",incall:"正在通话中···",screen1:"1路",screens4:"4路",screens9:"9路",screens16:"16路",screens25:"25路",liveVideo:"实时视频",cloudStorage:"云存储",playback:"回放",todayVideoOver:"当天视频已经播完",movementDetected:"检测到移动",soundDetected:"检测到声音",humanDetected:"检测到人形",noAudioEnabled:"未开启麦克风权限",alarmMessages:"告警消息",noMessages:"暂无消息",chooseDevice:"请选择设备",exceedMax:"当前观看人数已达上限",previousPage:"前一页",nextPage:"后一页",carousel:"轮播",refreshList:"刷新列表",refreshListTipCon:"确认要刷新列表吗？",refreshListTipCon1:"注：刷新列表后，将播放第一页设备画面。",refreshSuccess:"刷新成功",rebootDevice:"重启设备",rebootDeviceTipCon:"确认要重启设备吗？",scrolledToEnd:"已经转到底了",clickSingleDeviceTip:"轮播状态不能点击设备",device:"设备",deviceSearchTip:"设备CID/设备昵称",addGroup:"添加分组",deviceGroupLoading:"加载设备组列表···",addDevice:"添加设备",strategicAttributes:"策略属性",bulkDelete:"批量删除",status:"状态",cid:"设备CID",versionNumber:"版本号",deviceNickname:"设备昵称",group:"分组",strategy:"策略",editGroup:"编辑分组",groupName:"分组名称",groupNumber:"分组编号",authorizedUser:"授权用户",groupNameLimit:"长度限制20个字符",delGroup:"删除分组",editDevice:"编辑设备",sn:"设备CID",snEnter:"请输入CID",snEnterTip:"请输入有效的12位SN码",deviceVerificationCode:"设备验证码",deviceCodeEnter:"请输入验证码",deviceNameEnter:"请输入设备昵称",deleteDevice:"删除设备",bulkDelDeviceTip:"请勾选要删除的设备",delGroupHasDeviceTip:"设备组内存在设备，不能删除",nickName:"昵称",mark:"备注",addUser:"添加用户",resetPassword:"重置密码",inputLengthLimit:"长度限制 {limit} 个字符",editUser:"编辑用户",deleteUser:"删除用户",sureToReset:"确定要重置密码为“123456”吗?",bulkDelUserTip:"请勾选要删除的用户",userLimitTip:"最多只能创建5个用户",modifyPassword:"修改密码",passwordNotMatching:"两次输入密码不一致",oldPassword:"当前密码",newPassword:"新密码",checkPassword:"确认密码",passwordSame:"输入的新密码与旧密码一致",operationTime:"操作时间",operationType:"操作类型",editDeviceGroup:"编辑设备组",operationContent:"操作内容",saveVideo:"保存视频",time:"时间",startTime:"开始时间",endTime:"结束时间",empty:"清空",timerangeexceeds:"选择范围超出限制，请去视频管理删除视频或重新选择！",novideo:"当前时间范围未找到视频，请重新选择！",videomanagement:"视频管理",selectgroup:"请选择分组",allselect:"全选",download:"下载",downloading:"正在下载...",downloadSuccess:"下载成功",downloadFailed:"下载失败",saveVideoSuccess:"保存视频成功",timeLimit:"最多可保存100分钟视频",Uploading:"文件上传中",warnings:"警告！",ok:"确定",cancelVideoSave:"取消保存视频",cancelVideoSaveTip:"确定要取消保存视频吗？",loading:"加载中...",minute:"分钟",permissionLevel:"权限等级",manager:"管理员",guest:"访客"}},ntYl:function(e,t,i){"use strict";i.r(t);var n=i("k2lJ"),a=i("mYxE");for(var o in a)"default"!==o&&function(e){i.d(t,e,function(){return a[e]})}(o);i("wo/l");var s=i("KHd+"),r=Object(s.a)(a.default,n.a,n.b,!1,null,"37dfd6fc",null);r.options.__file="src\\views\\login\\index.vue",t.default=r.exports},o2sD:function(e,t,i){e.exports=i.p+"static/img/404.a57b6f3.png"},o5Jc:function(e,t,i){e.exports=i.p+"static/img/noDataLogo.b39e336.png"},"oQr/":function(e,t,i){"use strict";i.r(t);var n=i("27UU"),a=i("pviM");for(var o in a)"default"!==o&&function(e){i.d(t,e,function(){return a[e]})}(o);i("p4Kg");var s=i("KHd+"),r=Object(s.a)(a.default,n.a,n.b,!1,null,"797e31be",null);r.options.__file="src\\views\\layout\\components\\Navbar.vue",t.default=r.exports},oUrx:function(e,t,i){"use strict";i.r(t);var n=i("4BeY"),a=i.n(n),o=i("IaFt"),s=i.n(o),r=new a.a({id:"icon-404",use:"icon-404-usage",viewBox:"0 0 128 128",content:'<symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 128 128" id="icon-404"><path d="M121.718 73.272v9.953c3.957-7.584 6.199-16.05 6.199-24.995C127.917 26.079 99.273 0 63.958 0 28.644 0 0 26.079 0 58.23c0 .403.028.806.028 1.21l22.97-25.953h13.34l-19.76 27.187h6.42V53.77l13.728-19.477v49.361H22.998V73.272H2.158c5.951 20.284 23.608 36.208 45.998 41.399-1.44 3.3-5.618 11.263-12.565 12.674-8.607 1.764 23.358.428 46.163-13.178 17.519-4.611 31.938-15.849 39.77-30.513h-13.506V73.272H85.02V59.464l22.998-25.977h13.008l-19.429 27.187h6.421v-7.433l13.727-19.402v39.433h-.027zm-78.24 2.822a10.516 10.516 0 0 1-.996-4.535V44.548c0-1.613.332-3.124.996-4.535a11.66 11.66 0 0 1 2.713-3.68c1.134-1.032 2.49-1.864 4.04-2.468 1.55-.605 3.21-.908 4.982-.908h11.292c1.77 0 3.431.303 4.981.908 1.522.604 2.85 1.41 3.986 2.418l-12.26 16.303v-2.898a1.96 1.96 0 0 0-.665-1.512c-.443-.403-.996-.604-1.66-.604-.665 0-1.218.201-1.661.604a1.96 1.96 0 0 0-.664 1.512v9.071L44.364 77.606a10.556 10.556 0 0 1-.886-1.512zm35.73-4.535c0 1.613-.332 3.124-.997 4.535a11.66 11.66 0 0 1-2.712 3.68c-1.134 1.032-2.49 1.864-4.04 2.469-1.55.604-3.21.907-4.982.907H55.185c-1.77 0-3.431-.303-4.981-.907-1.55-.605-2.906-1.437-4.041-2.47a12.49 12.49 0 0 1-1.384-1.512l13.727-18.217v6.375c0 .605.222 1.109.665 1.512.442.403.996.604 1.66.604.664 0 1.218-.201 1.66-.604a1.96 1.96 0 0 0 .665-1.512V53.87L75.97 36.838c.913.932 1.66 1.99 2.214 3.175.664 1.41.996 2.922.996 4.535v27.011h.028z" /></symbol>'});s.a.add(r);t.default=r},oYx3:function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.asyncRouterMap=t.constantRouterMap=void 0;var n=r(i("4d7F")),a=r(i("Kw5r")),o=r(i("jE9Z")),s=r(i("2c6e"));function r(e){return e&&e.__esModule?e:{default:e}}function l(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i]);return t.default=e,t}a.default.use(o.default);var d=t.constantRouterMap=[{path:"/login",component:function(){return n.default.resolve().then(function(){return l(i("ntYl"))})},hidden:!0},{path:"/404",component:function(){return n.default.resolve().then(function(){return l(i("/eX4"))})},hidden:!0},{path:"/401",component:function(){return n.default.resolve().then(function(){return l(i("UUO+"))})},hidden:!0}];t.default=new o.default({scrollBehavior:function(){return{y:0}},routes:d});t.asyncRouterMap=[{path:"/home",component:s.default,children:[{path:"",component:function(){return n.default.resolve().then(function(){return l(i("4AHp"))})},name:"home"}],hidden:!0},{path:"/device",component:s.default,children:[{path:"",component:function(){return n.default.resolve().then(function(){return l(i("PWg7"))})},name:"device"}]},{path:"/user",component:s.default,children:[{path:"",component:function(){return n.default.resolve().then(function(){return l(i("G/jE"))})},name:"user"}]},{path:"/system",component:s.default,children:[{path:"",component:function(){return n.default.resolve().then(function(){return l(i("Ptsr"))})},name:"system"}]},{path:"/video_management",component:s.default,children:[{path:"",component:function(){return n.default.resolve().then(function(){return l(i("+SfS"))})},name:"video_management"}]},{path:"/logs",component:s.default,children:[{path:"",component:function(){return n.default.resolve().then(function(){return l(i("gXRY"))})},name:"logs"}]},{path:"*",redirect:"/login",hidden:!0}]},odDC:function(e,t,i){},om6w:function(e,t,i){"use strict";var n=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{ref:"deviceMgtLayout",staticClass:"deviceMgtBox"},[n("div",{staticClass:"deviceMgtLayout"},[n("div",{staticClass:"groupBox"},[n("div",{staticClass:"groupListCon placeholder"}),e._v(" "),n("div",{staticClass:"groupListCon fixed"},[e.group_loading?n("div",{staticClass:"groupLoadingBox"},[e._m(0),e._v(" "),n("div",[e._v(e._s(e.$t("deviceGroupLoading")))])]):e._l(e.group_list,function(t,a){return n("div",{key:a,staticClass:"groupItem pointer",class:{selectedGroup:t.selected_group_flag},on:{click:function(i){e.changeGroup(t)}}},[n("div",{staticClass:"groupItemLeft"},[n("span",{staticClass:"groupName",attrs:{title:t.label},domProps:{textContent:e._s(t.label)}})]),e._v(" "),"1"==e.account_type?n("div",{staticClass:"groupItemRight"},[-1!==t.value?n("img",{staticClass:"operateImg",attrs:{src:i("RoGb")},on:{click:function(i){i.stopPropagation(),e.addGroup(t)}}}):e._e(),e._v(" "),-1!==t.value?n("img",{staticClass:"operateImg",attrs:{src:i("iWp0")},on:{click:function(i){i.stopPropagation(),e.delGroup(t)}}}):e._e()]):e._e()])})],2)]),e._v(" "),n("div",{staticClass:"operateDeviceBox"},[n("div",{staticClass:"filterBox"},[n("div",{staticClass:"operationType filterItem"},[n("span",[e._v(e._s(e.$t("device")))]),e._v(" "),n("el-input",{staticStyle:{width:"240px"},attrs:{placeholder:e.$t("deviceSearchTip"),maxlength:"100"},nativeOn:{keyup:function(t){if(!("button"in t)&&e._k(t.keyCode,"enter",13,t.key,"Enter"))return null;e.search()}},model:{value:e.searchText,callback:function(t){e.searchText=t},expression:"searchText"}})],1),e._v(" "),n("el-button",{staticClass:"tipSubmitBtn",attrs:{type:"primary",icon:"el-icon-search"},on:{click:function(t){e.search()}}},[e._v("\n          "+e._s(e.$t("search"))+"\n        ")])],1),e._v(" "),n("div",{staticClass:"deviceListBox"},[n("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.list_loading,expression:"list_loading"}],attrs:{"highlight-current-row":!0,data:e.device_list,stripe:""},on:{"selection-change":e.handleSelectionChange}},[n("el-table-column",{attrs:{type:"index",label:"#",align:"center",width:"60","class-name":"infoText"}}),e._v(" "),e._l(e.listTitle,function(t,i){return n("el-table-column",{key:i,attrs:{prop:t.name,label:t.title,width:t.width,"column-key":t.name,"class-name":["version","alias","group_name"].includes(t.name)?"infoText":"",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return["net_status"===t.column.columnKey?n("section",[n("span",{staticClass:"dot marRight10",class:0===t.row.net?"offDot":"online"}),n("span",{attrs:{title:t.row[t.column.columnKey]},domProps:{innerHTML:e._s(e.handleColumnShow(t.row[t.column.columnKey]))}})]):n("section",{attrs:{title:t.row[t.column.columnKey]},domProps:{innerHTML:e._s(e.handleColumnShow(t.row[t.column.columnKey]))}})]}}])})}),e._v(" "),e.isSuperAdmin?n("el-table-column",{attrs:{label:e.$t("operate"),width:"113px",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("section",[n("el-button",{staticClass:"operateRowBtn",attrs:{title:e.$t("edit"),type:"text"},on:{click:function(i){e.addEditDevice(t.row)}}},[e._v(e._s(e.$t("edit")))]),e._v(" "),n("el-button",{staticClass:"operateRowBtn rowDeleteBtn",attrs:{title:e.$t("delete"),type:"text"},on:{click:function(i){e.delDevice(t.row)}}},[e._v(e._s(e.$t("delete")))])],1)]}}])}):e._e(),e._v(" "),n("template",{slot:"empty"},[n("el-empty",{attrs:{image:i("tFzm"),"image-size":65}})],1)],2),e._v(" "),n("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>10,expression:"total > 10"}],staticClass:"text-right mar_top_0",attrs:{total:e.total,page:e.listQuery.page,limit:e.listQuery.limit},on:{"update:page":function(t){e.$set(e.listQuery,"page",t)},"update:limit":function(t){e.$set(e.listQuery,"limit",t)},pagination:e.handlePaginationChange}})],1)])]),e._v(" "),n("group-add-dialog",{ref:"groupAddDialog",attrs:{title:e.dialogTitle,number:e.editGroupNumber}}),e._v(" "),n("device-add-dialog",{ref:"deviceAddDialog",attrs:{title:e.dialogTitle}}),e._v(" "),n("tip-dialog",{ref:"tipDialog",attrs:{title:e.dialogTitle,tip:e.dialogTip,request:e.tipRequest,params:e.tipParams,"req-type":"http"},on:{handleTip:e.backTip}})],1)},a=[function(){var e=this.$createElement,t=this._self._c||e;return t("div",[t("i",{staticClass:"el-icon-loading"})])}];n._withStripped=!0,i.d(t,"a",function(){return n}),i.d(t,"b",function(){return a})},p4Kg:function(e,t,i){"use strict";var n=i("IZcc");i.n(n).a},pviM:function(e,t,i){"use strict";i.r(t);var n=i("glkp"),a=i.n(n);for(var o in n)"default"!==o&&function(e){i.d(t,e,function(){return n[e]})}(o);t.default=a.a},"r+fU":function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=i("OU4T"),a=i("LhkO");t.default={name:"GroupAddDialog",props:{title:{type:String,default:""},number:{type:String,default:""}},data:function(){return{dialogVisible:!1,dialogForm:{rules:{group_name:[{required:!0,message:this.$t("cannotEmpty"),trigger:"blur"}]},data:{group_name:""}},group_id:-10,is_edit:!1,loading:!1,authorizedUserList:[],userList:[]}},computed:{authorizedUserAccountList:function(){var e=this;return this.userList.filter(function(t){return e.authorizedUserList.includes(t.name?t.name:t.account)}).map(function(e){return e.account})},accountType:function(){return sessionStorage.account_type}},created:function(){},methods:{show:function(e){var t=this;this.dialogVisible=!0,e?(this.is_edit=!0,this.dialogForm.data.group_name=e.label,this.group_id=e.value,"1"==sessionStorage.account_type&&(0,a.getUserList)().then(function(i){if(200===i.code){var a=i.datas.map(function(e){return{account:e.account,name:e.name,mark:e.remark}});t.userList=a,(0,n.getDeviceGroupDetail)({group_id:e.value}).then(function(e){if(200===e.code){var i=e.data.bind_account;t.authorizedUserList=a.filter(function(e){return i.includes(e.account)}).map(function(e){return e.name?e.name:e.account})}}).catch(function(e){console.log(e)})}}).catch(function(e){console.log(e)}).finally(function(){t.list_loading=!1})):this.is_edit=!1,this.$refs&&this.$refs.dialogForm&&this.$refs.dialogForm.clearValidate()},hideDialog:function(){this.dialogVisible=!1,this.dialogForm.data.group_name="",this.authorizedUserList=[]},submit:function(){var e=this,t=this;t.$refs.dialogForm.validate(function(i){if(i){t.loading=!0;var a={group_id:e.group_id,name:e.dialogForm.data.group_name,bind_account:e.authorizedUserAccountList};return console.log(a),void(0,n.editDeviceGroup)(a).then(function(e){console.log(e),200===e.code&&(t.$message.success(t.$t("editSuccess")),t.hideDialog(),t.$parent.getGroup())}).catch(function(e){console.log(e)}).finally(function(){t.loading=!1})}return console.log("Error!"),!1})}}}},rYlX:function(e,t,i){},rgce:function(e,t,i){"use strict";i.r(t);var n=i("uAsm"),a=i.n(n);for(var o in n)"default"!==o&&function(e){i.d(t,e,function(){return n[e]})}(o);t.default=a.a},rseg:function(e,t,i){"use strict";i.r(t);var n=i("k/VT"),a=i("rgce");for(var o in a)"default"!==o&&function(e){i.d(t,e,function(){return a[e]})}(o);i("wRDK");var s=i("KHd+"),r=Object(s.a)(a.default,n.a,n.b,!1,null,"0a394090",null);r.options.__file="src\\components\\OneBtnTipDialog\\index.vue",t.default=r.exports},sQSC:function(e,t,i){e.exports=i.p+"static/img/loading.cd72cda.png"},"sg+I":function(e,t,i){},sutq:function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=d(i("gDS+")),a=d(i("Kw5r")),o=i("XJYT"),s=d(i("Q2AE")),r=d(i("mSNy")),l=d(i("oYx3"));function d(e){return e&&e.__esModule?e:{default:e}}var c=sessionStorage.wsServer,u="",h={},_=1,f=null,p=!1,m=A().type,g=A().name;function v(e,t){console.log("init"),console.log(this),u=e,p=!1,t&&(c=t),(h=new WebSocket(c)).onopen=y,h.onerror=b,h.onmessage=S,h.onclose=w,console.log("socket: ",h),this&&(this.socket=h)}function y(e){(console.log("connected"),a.default.prototype.$bus.$emit("webSocketConnecteType",u),"closeReconnection"===u||"refreshReconnection"===u)&&(C("cli_miru_login",{language_type:0,account:sessionStorage.username,passwd:"",os:1,net:m,name:g,bundle_id:"**********",device_token:"token"+E,sessid:s.default.getters.token,vid:"0001",vkey:"DcWP670PNfCtPIETQk03lEzbt6qRDRDy"}),u="");f||(f=setInterval(function(){C("pub_heartbeat",{})},6e4))}function b(e){console.log("WebSocket连接error",e)}function S(e){!function(e){if(void 0===e.ret||0===e.ret)a.default.prototype.$bus.$emit(e.headers.id,e);else{var t="";if(120===e.ret)t=r.default.t("errCode120");else if(121===e.ret)t=r.default.t("errCode121");else if(122===e.ret)t=r.default.t("errCode122"),setTimeout(function(){s.default.dispatch("FedLogOut"),location.reload()},3e3);else if(123===e.ret)t=r.default.t("errCode123");else if(124===e.ret)t=r.default.t("errCode124");else if(125===e.ret)t=r.default.t("errCode125");else if(126===e.ret)t=r.default.t("errCode126");else if(127===e.ret)t=r.default.t("errCode127");else if(128===e.ret)t=r.default.t("errCode128");else if(129===e.ret)t=r.default.t("errCode129");else if(161===e.ret)t=r.default.t("errCode161");else if(165===e.ret){if(t=r.default.t("errCode165"),"/login"!==l.default.currentRoute.path&&(s.default.dispatch("FedLogOut"),l.default.replace("/login"),window.history&&window.history.pushState)){window.history.replaceState(null,null,"/login");var i=function(){window.history.replaceState(null,null,"/login")};window.addEventListener("popstate",i),setTimeout(function(){window.removeEventListener("popstate",i)},2e3)}}else t=290===e.ret?r.default.t("errCode290"):291===e.ret?r.default.t("errCode291"):292===e.ret?r.default.t("errCode292"):1002===e.ret?r.default.t("errCode1002"):1004===e.ret?r.default.t("errCode1004"):2008===e.ret?r.default.t("errCode2008"):2011===e.ret?r.default.t("errCode2011"):2030===e.ret?r.default.t("errCode2030"):2031===e.ret?r.default.t("errCode2031"):2032===e.ret?r.default.t("errCode2032"):1260===e.ret?r.default.t("timerangeexceeds"):e.msg;o.Message.error(t),a.default.prototype.$bus.$emit(e.headers.id,e.ret)}}(JSON.parse(e.data))}function w(){if(console.log("连接已关闭..."),console.log("是否有token："+s.default.getters.token),console.log("connecte_type: ",u),console.log("isManualClose: ",p),f&&(clearInterval(f),f=null),p)return console.log("主动关闭WebSocket，不进行重连"),void(p=!1);s.default.getters.token&&(console.log("断开重连"),v("closeReconnection"))}function C(e,t,i){var a="";a=i?(0,n.default)({headers:{id:e,callee:i,req_id:k(),time:Date.parse(new Date)/1e3},body:t}):(0,n.default)({headers:{id:e,req_id:k(),time:Date.parse(new Date)/1e3},body:t}),h.send(a)}var E=T();function k(){var e="web_"+E+"_"+_;return _++,e}function T(){if(localStorage&&localStorage.getItem("code_temp"))return localStorage.getItem("code_temp");for(var e=[0,1,2,3,4,5,6,7,8,9,"A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"],t="",i=0;i<6;i++){t+=e[Math.floor(36*Math.random())]}return localStorage.setItem("code_temp",t),t}function A(){var e=navigator.userAgent,t=e.match(/NetType\/\w+/)?e.match(/NetType\/\w+/)[0]:"NetType/other",i=1;switch(t=t.toLowerCase().replace("nettype/","")){case"wifi":i=1,name="wifi";break;case"5g":i=5,name="5g";break;case"4g":i=4,name="4g";break;case"3g":case"3gnet":i=3,name="3g";break;case"2g":i=2,name="2g";break;default:i=10,name="wired"}return{type:i,name:name}}t.default={initWebSocket:v,closeWebsocket:function(){console.log("closeWebsocket  socket: ",h),p=!0,h&&h.url&&h.close(),f&&(clearInterval(f),f=null)},webSocketSend:C,generatedCode:T}},svwP:function(e,t,i){"use strict";i.r(t);var n=i("r+fU"),a=i.n(n);for(var o in n)"default"!==o&&function(e){i.d(t,e,function(){return n[e]})}(o);t.default=a.a},t3Un:function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=u(i("4d7F")),a=u(i("QbLZ")),o=u(i("vDqi")),s=u(i("Q2AE")),r=i("X4fA"),l=u(i("mSNy")),d=i("XJYT"),c=u(i("65Ft"));function u(e){return e&&e.__esModule?e:{default:e}}var h=o.default.create({baseURL:"/api/web/",timeout:1e4});h.interceptors.request.use(function(e){var t=(0,a.default)({},e);return void 0===t.data&&(t.data={}),t.data.time=Math.round((new Date).valueOf()/1e3),t.data.auth_token=s.default.getters.token,t},function(e){console.log(e),n.default.reject(e)}),h.interceptors.response.use(function(e){var t=e.data;if(200!==t.code){var i="";return 401===t.code?(i=l.default.t("errCode401"),setTimeout(function(){console.log("401错误，清除所有缓存数据"),s.default.commit("SET_TOKEN",""),s.default.commit("SET_ROLES",[]),s.default.commit("SET_ROLE",""),sessionStorage.clear(),(0,r.removeToken)(),(0,r.removeRole)(),c.default.clearSessid(!1),(void 0).$router.push({path:"/login"})},3e3)):i=500===t.code?l.default.t("errCode500"):1e3===t.code||1001===t.code?l.default.t("errCode1000"):1100===t.code?l.default.t("errCode1100"):1101===t.code?l.default.t("errCode1101"):1102===t.code?l.default.t("errCode1102"):1258===t.code?l.default.t("userLimitTip"):1260===t.code?l.default.t("timerangeexceeds"):1002===t.code?l.default.t("errCode1002"):t.msg,d.Message.error(i),n.default.reject(e.data)}return e.data},function(e){console.log("err"+e);var t=/^(timeout of )(\d+)(ms exceeded)$/g.exec(e.message);return t&&t.length&&(e.message=t[2]?l.default.t("timeout"):e.message),e.message=e.message.replace(/(Network Error)/,l.default.t("networkError")),d.Message.error(e.message),n.default.reject(e)}),t.default=h},tFzm:function(e,t,i){e.exports=i.p+"static/img/empty.8d4d32f.png"},tfGf:function(e,t,i){"use strict";i.r(t);var n=i("lTTe"),a=i.n(n);for(var o in n)"default"!==o&&function(e){i.d(t,e,function(){return n[e]})}(o);t.default=a.a},tiNe:function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default={mgtPlat:"MIRU",mgtPlatTip:"Жандуу Видео Байкоо Жүйөсү",scanCodeToLogIn:"Кирүү үчүн QR кодду сканерлеңиз",scanCodeTip:"Түзмөккө туташкан тиркемени колдонуп кирүү үчүн кодду сканерлеңиз",qrcodeExpired:"QR коду мөөнөтү бүттү",username:"Колдонуучунун аты",password:"Сыр сөз",passwordTip1:"Сыр сөзүңүздү киргизиңиз",rememberPwd:"Сыр сөздү эстеп калуу",pleaseEnterAccount:"Эсеп жазуусун киргизиңиз",pleaseEnterCorrectPwd:"Сыр сөз 6дан 128 символго чейин болушу керек!",logIn:"Кирүү",account:"Эсеп",accountTip:"6-20 сандар/букваларды киргизинг.",pleaseEnterCorrectPwdLen:"Сыр сөздүн узундугу 6 символдон кем эмес болушу керек",pleaseEnterCorrectPwd18:"Сыр сөз 6дан 18 символго чейин болушу керек!",verificationCode:"Тастык коду",verificationCodeTip:"Тастык кодун киргизинг, алба.",verificationCodeError:"Текшерув коди хато бўлди.",home:"Башкы бет",deviceManagement:"Түзмөктөрдү башкаруу",userManagement:"Колдонуучуларды башкаруу",systemSetting:"Системанын жөндөөлөрү",operationLogs:"Операциялык журналдар",quitTitle:"Чыгуу",quitTip:"Чыгуу керек экенине ишенесизби?",submit:"Жөнөтүү",confirm:"Ырастоо",sure:"Ырастоо",cancel:"Жокко чыгаруу",search:"Издөө",inquire:"Сураштыруу",nodata:"Маалымат жок",cannotEmpty:"Бош калтырууга болбойт",operate:"Операция",modify:"Өзгөртүү",export:"Экспорттоо",edit:"Түзөтүү",delete:"Өчүрүү",sureToDelete:"Чынында эле өчүрүүнү каалайсызбы?",deleteSuccess:"Ийгиликтүү өчүрүлдү",addSuccess:"Ийгиликтүү кошулду",addFail:"Кошуу ишке ашкан жок",remove:"Алып салуу",sureToRemove:"Чынында эле алып салууну каалайсызбы?",removeSuccess:"Ийгиликтүү алып салынды",editSuccess:"Ийгиликтүү түзөтүлдү",tip:"Кеңеш",noMore:"Башка маалымат жок",getLogSuccess:"Журнал ийгиликтүү алынды",all:"Баары",modifySuccess:"Ийгиликтүү өзгөртүлдү",noData:"Маалымат жок",refresh:"Жаңыртуу",save:"Сактоо",to:"Батыш",startDate:"Башталыш датасы",endDate:"Аякташ датасы",pleaseSelect:"Тандаңыз",errCode120:"Белгисиз ката",errCode121:"Маалыматтар базасы катасы",errCode122:"Сеанс мөөнөтү бүттү",errCode123:"Билдирүү форматы катасы",errCode124:"Билдирүүлөрдүн ылдамдыгы чектен ашты, акылга сыярлык агымды көзөмөлдөңүз (секундуна 100 билдирүү)",errCode125:"Параметрлер катасы",errCode126:"Робот API'ин чакыруу катасы",errCode127:"vid vkey катасы",errCode128:"Робот API чакыруу маалыматтарында ката кетти",errCode129:"Кирүү керек, бул иш-аракетти аткаруу үчүн кирүү талап кылынат",errCode161:"Эсеп же сыр сөз туура эмес",errCode165:"Каттоо эсеби мурунтан эле башка түзмөккө кирген.",errCode200:"CID жок",errCode202:"Түзмөктүн лакап аты буга чейин бар",errCode203:"Түзмөк туташкан эмес",errCode204:"Түзмөк башка аккаунтка туташкан",errCode205:"Түзмөк текшерүү коддору дал келбейт",errCode206:"Түзмөк өзү тарабынан туташкан",errCode214:"Түзмөк башка тиркеме тарабынан буга чейин туташкан",errCode215:"Түзмөк оффлайнда",errCode290:"Кокус код жок",errCode291:"Кокус коддун мөөнөтү бүттү",errCode292:"Статус туура эмес",errCode401:"Кайра кирүү керек",errCode500:"Сервер катасы",errCode1000:"Суроо катасы",errCode1001:"Суроо катасы",errCode1002:"Уруксат четке кагылды",errCode1004:"Туташуу катасы",errCode1100:"Номунун дубликациясы",errCode1101:"Эсептик жазуу бар",errCode1102:"Учурдагы сырсөз туура эмес",errCode2008:"SD картасы форматталып жатат",errCode2011:"SD картаны окуу катасы",errCode2030:"SD карта тарыхый видеосу окулду",errCode2031:"SD картасынын тарыхый видеосун окуу катасы",errCode2032:"SD карта тарыхый видеосу окулбай жатат",timeout:"Тармактык катал",networkError:"Тармактык катал",allGroup:"Бардык топ",noDevice:"Түзмөк жок",online:"Онлайн",offline:"Оффлайн",deviceLoading:"Түзмөк тизмеси жүктөлүүдө···",videoLoading:"Видео жүктөлүүдө···",failPlay:"Ойнотулбай жатат",deviceOffline:"Түзмөк оффлайнда",noHistoricalVideo:"Түзмөктө тарыхый видео жок",noOssVideo:"Бул түзмөктө булут сактагычы жок",retry:"Кайра аракет кылуу",incall:"Чалууда···",screen1:"1-экран",screens4:"4-экран",screens9:"9-экран",screens16:"16-экран",screens25:"25-экран",liveVideo:"Түз эфир",cloudStorage:"Булут сактагыч",playback:"Тарых",todayVideoOver:"Бүгүнкү видео бүткөн",movementDetected:"Кыймыл аныкталды",soundDetected:"Үн аныкталды",humanDetected:"Адам табылды",noAudioEnabled:"Микрофон уруксаты күйгүзүлгөн эмес",alarmMessages:"Эскертүү билдирүүлөрү",noMessages:"Жаңылык жок",chooseDevice:"Түзмөктү тандаңыз",exceedMax:"Учурдагы максималдуу көрүүчүлөр саны жетишти",previousPage:"Акыркы бет",nextPage:"Кийинки бет",carousel:"Карусель",refreshList:"Списокни жаңылоо",refreshListTipCon:"Сиз сиздики рўйхатни жаңылашни истайсизми?",refreshListTipCon1:"Эскертма: Рўйхатни жаңылагандан кейин, биринчи бет теримлик экранлар ўйналади.",refreshSuccess:"Жаңылоо иш берди",rebootDevice:"Устройствону жаңылоо",rebootDeviceTipCon:"Устройствону жаңылоо қилишини тастыктайсизми?",scrolledToEnd:"Аягына чейин айланды",clickSingleDeviceTip:"Карусель абалында устройствага басууга болбойт.",addGroup:"Топ кошуу",deviceGroupLoading:"Топ түзмөктөр тизмеси жүктөлүүдө···",addDevice:"Түзмөк кошуу",strategicAttributes:"Стратегиялык атрибуттар",bulkDelete:"Жалпылап өчүрүү",status:"Статус",cid:"Түзмөк CID",versionNumber:"Версия номери",deviceNickname:"Түзмөктүн лакабы",group:"Топ",strategy:"Стратегия",editGroup:"Топту түзөтүү",groupName:"Топ аты",groupNumber:"Топ номери",authorizedUser:"Ыйгарым укуктуу колдонуучу",groupNameLimit:"Узундук чектөөсү 20 символ",delGroup:"Топту өчүрүү",editDevice:"Түзмөктү түзөтүү",sn:"Түзмөк CID",snEnter:"CID кодун киргизиңиз",snEnterTip:"12 сандуу CID кодун киргизиңиз",deviceVerificationCode:"Түзмөк текшерүү коду",deviceCodeEnter:"Текшерүү кодун киргизиңиз",deviceNameEnter:"Түзмөктүн лакап атын киргизиңиз",deleteDevice:"Түзмөктү өчүрүү",bulkDelDeviceTip:"Өчүрүүнү каалаган түзмөктөрдү белгилеңиз",device:"Түзмөк",deviceSearchTip:"Түзмөк CID/Түзмөктүн лакабы",delGroupHasDeviceTip:"Топтомдо түзмөктөр бар, ошондуктан аны өчүрүүгө болбойт",nickName:"Лакап аты",mark:"Белги",addUser:"Колдонуучу кошуу",resetPassword:"Сыр сөздү калыбына келтирүү",inputLengthLimit:"Узундук чеги {limit} символ",editUser:"Колдонуучуну түзөтүү",deleteUser:"Колдонуучуну өчүрүү",sureToReset:'Сыр сөздү "123456" кылып калыбына келтирүүнү каалайсызбы?',bulkDelUserTip:"Өчүрүүнү каалаган колдонуучуларды белгилеңиз",userLimitTip:"Өчүрүүнү каалаган 5 колдонуучуларды белгилеңиз",modifyPassword:"Сыр сөздү өзгөртүү",passwordNotMatching:"Сыр сөз дал келбейт",oldPassword:"Азыркы сыр сөз",newPassword:"Жаңы сыр сөз",checkPassword:"Сыр сөздү текшерүү",passwordSame:"Сыр сөздү жаңы сыр сөздүнө болуы калды",operationTime:"Операция убактысы",operationType:"Операция түрү",editDeviceGroup:"Түзмөктүн группасын түзөтүү",operationContent:"Операция мөзгө",saveVideo:"Видеону сактап койуу",time:"Убакыт",startTime:"Башталган убакыт",endTime:"Аяктоо убактысы",empty:"Тазалоо",timerangeexceeds:"Чек аранын чектөөдөн ашып кетти, видеолорду башкарууга барып өчүрүңүз же кайра тандоо кылыңыз!",novideo:"Учурдагы убакыт аралыгында видео табылган жок, кайра тандаңыз!",videomanagement:"Видео башкаруу",selectgroup:"Топчу топтомду тандаңыз",allselect:"Бардыкты тандоо",download:"Жүктөө",downloading:"Жүктөө···",downloadSuccess:"Жүктөө ийгиликтүү",downloadFailed:"Жүктөө ишке ашкан жок",saveVideoSuccess:"Видеону сактап койуу ийгиликтүү",timeLimit:"Видеону максимум 100 минутка сактап койуу мүмкүн",Uploading:"Файл жүктөө",warnings:"Эскертүү!",ok:"Макул",cancelVideoSave:"Видеону сактоодон баш тартуу",cancelVideoSaveTip:"Видеону сактоону жокко чыгаргыңыз келеби?",loading:"жүктөлүүдө...",minute:"мүнөт",permissionLevel:"Уруксат деңгээли",manager:"менеджер",guest:"Конок"}},u5wx:function(e,t,i){"use strict";i.r(t);var n=i("4BeY"),a=i.n(n),o=i("IaFt"),s=i.n(o),r=new a.a({id:"icon-view",use:"icon-view-usage",viewBox:"0 0 21.615 21.615",content:'<symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 21.615 21.615" id="icon-view"><defs><style>#icon-view .a{fill:#b8b8b8;opacity:0;}#icon-view .b{fill:#8a8a8a;}</style></defs><g transform="translate(-1268.385 -395.885)"><rect class="a" width="21.615" height="21.615" transform="translate(1268.385 395.885)" /><g transform="translate(1268.385 400.582)"><path class="b" d="M33.207,224c-6.737,0-10.807,6.093-10.807,6.093S27.1,236,33.207,236s10.807-5.907,10.807-5.907S39.944,224,33.207,224Zm6.948,8.332a11.986,11.986,0,0,1-7.018,2.736,11.59,11.59,0,0,1-7.018-2.736,20.192,20.192,0,0,1-2.175-1.927c-.14-.124-.211-.249-.351-.373.07-.124.211-.249.281-.373a14.113,14.113,0,0,1,2.035-1.99,11.455,11.455,0,0,1,7.228-2.8,11.455,11.455,0,0,1,7.228,2.8,18.959,18.959,0,0,1,2.035,1.99c.07.124.211.249.281.373a1.711,1.711,0,0,1-.351.373A20.158,20.158,0,0,1,40.155,232.332Z" transform="translate(-22.4 -224)" /><path class="b" d="M346.835,345.6c-2.51,0-4.435,1.257-4.435,2.775,0,1.571,2.008,2.775,4.435,2.775,2.51,0,4.435-1.257,4.435-2.775S349.261,345.6,346.835,345.6Zm0,4.817c-1.757,0-3.18-.89-3.18-1.99s1.422-1.99,3.18-1.99,3.18.89,3.18,1.99C350.015,349.475,348.592,350.417,346.835,350.417Z" transform="translate(-336.091 -342.783)" /></g></g></symbol>'});s.a.add(r);t.default=r},uAsm:function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default={name:"OneBtnTipDialog",props:{request:{type:Function,default:void 0},params:{type:Object,default:function(){return{}}},title:{type:String,default:""},tip:{type:String,default:""},one_btn_text:{type:String,default:""}},data:function(){return{dialogVisible:!1,name:"",cid:"",loading:!1}},methods:{show:function(e,t){this.dialogVisible=!0,this.name=e,this.cid=t,console.log(this.name,this.cid)},hideDialog:function(){this.dialogVisible=!1},submit:function(){var e=this;this.loading=!0,this.request&&"function"==typeof this.request?this.request(this.params).then(function(t){e.$emit("handleTip",e.name,e.cid,t),e.loading=!1,e.hideDialog()}).catch(function(){e.loading=!1}):(this.$emit("handleTip",this.name,this.cid),this.loading=!1,this.hideDialog())},handleCancelIcon:function(e){this.$emit("handleCancel",this.name,this.cid),e()}}}},uByC:function(e,t,i){},upXb:function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=u(i("gDS+")),a=i("Yfch"),o=u(i("84/4")),s=u(i("ETGp")),r=i("X4fA"),l=(i("fe1z"),i("vjVj")),d=u(i("p46w")),c=u(i("65Ft"));function u(e){return e&&e.__esModule?e:{default:e}}t.default={name:"Login",components:{LangSelect:s.default,Captcha:o.default},data:function(){var e=this;return{language:(0,r.getLanguage)(),loginForm:{username:"",password:"",verification_code:"",saveUserInfoEnable:!1},loginRules:{username:[{required:!0,trigger:"blur",validator:function(t,i,n){""===i.trim()?n(new Error(e.$t("pleaseEnterAccount"))):i.trim().length<6||i.trim().length>20||!(0,a.validAZNumber)(i.trim())?n(new Error(e.$t("accountTip"))):n()}}],password:[{required:!0,trigger:"blur",validator:function(t,i,n){i.trim().length<6||i.trim().length>18?n(new Error(e.$t("pleaseEnterCorrectPwd18"))):n()}}],verification_code:[{required:!0,message:this.$t("verificationCodeTip"),trigger:"blur"}]},showPwd:!1,captcha_text:"",loading:!1,code_temp:this.$websocket.generatedCode()}},mounted:function(){"true"==d.default.get("lanSavedUserInfo")&&(this.loginForm.saveUserInfoEnable=!0,localStorage&&localStorage.lanUserName&&(this.loginForm.username=(0,l.decryption)(localStorage.lanUserName),this.loginForm.password=(0,l.decryption)(localStorage.lanPassword))),this.checkAndAutoLogin(),this.setupCrossTabLoginListener()},beforeDestroy:function(){this.$bus.$off("autoLoginFromAnotherTab")},methods:{handleSetLanguage:function(e){this.language=e},checkAndAutoLogin:function(){var e=c.default.getSessid(),t=c.default.getAccount();e&&""!==e.trim()&&t&&""!==t.trim()&&(console.log("Found shared sessid and account, attempting auto login:",e,t),this.autoLoginWithSessid(e,t))},autoLoginWithSessid:function(e,t){var i=this;this.loading=!0;var n="https:"==window.location.protocol?"wss://"+window.location.hostname+(window.location.hostname.indexOf("yf")>-1?":544/miru":":443/miru"):"ws://"+window.location.hostname+":643/miru",a={language_type:0,account:t,passwd:"",os:1,net:this.getNetworkType().type,name:this.getNetworkType().name,bundle_id:"**********",device_token:"token"+this.code_temp,sessid:e,vid:"0001",vkey:"DcWP670PNfCtPIETQk03lEzbt6qRDRDy"};this.$websocket.initWebSocket("",n),this.checkWebsocket(a),this.$bus.$once("cli_miru_login_rsp",function(e){if(console.log("Auto login response:",e),sessionStorage.wsServer=n,"number"==typeof e)return console.log("Auto login failed, clearing invalid sessid"),c.default.clearSessid(),i.$websocket.closeWebsocket(),void(i.loading=!1);i.handleLoginSuccess(e)})},handleLoginSuccess:function(e){sessionStorage.account_type=e.body.account_type,sessionStorage.username=e.body.account,sessionStorage.account_alias=e.body.alias,this.$store.commit("SET_TOKEN",e.body.sessid),(0,r.setToken)(e.body.sessid),sessionStorage.videoUrl=(0,n.default)(e.body.srs_http),c.default.setSessid(e.body.sessid,e.body.account),this.saveUserInfo(),this.$router.push({path:"/home"}),this.loading=!1,history.pushState(null,null,document.URL),window.addEventListener("popstate",function(){history.pushState(null,null,document.URL)})},checkWebsocket:function(e){var t=this,i=this.$websocket.socket.readyState;console.log(i),0===i?setTimeout(function(){t.checkWebsocket(e)},300):1===i?this.$websocket.webSocketSend("cli_miru_login",e):(console.log("???"),console.log(i))},changeEye:function(e){this.showPwd="open"===e},getCaptchaText:function(e){this.captcha_text=e},saveUserInfo:function(){this.loginForm.saveUserInfoEnable?(d.default.set("lanSavedUserInfo","true",{expires:30}),localStorage.lanUserName=(0,l.encryption)(this.loginForm.username),localStorage.lanPassword=(0,l.encryption)(this.loginForm.password)):(d.default.set("lanSavedUserInfo","false",{expires:-1}),localStorage.lanUserName="",localStorage.lanPassword="")},handleLogin:function(){var e=this;this.$refs.loginForm.validate(function(t){if(!t)return console.log("error submit!!"),!1;if(e.captcha_text.toLowerCase()===e.loginForm.verification_code.toLowerCase()){e.loading=!0;var i=c.default.getSessid(),n=c.default.getAccount();if(i&&n)console.log("发现缓存的sessid和account，尝试使用缓存登录"),e.tryLoginWithCache(i,n);else{console.log("没有缓存，使用账号密码登录");var a="https:"==window.location.protocol?"wss://"+window.location.hostname+(window.location.hostname.indexOf("yf")>-1?":544/miru":":443/miru"):"ws://"+window.location.hostname+":643/miru";e.toLinkWsLogin(a)}}else e.$message.error(e.$t("verificationCodeError"))})},tryLoginWithCache:function(e,t){var i=this;console.log("使用缓存登录:",{sessid:e,account:t});var n="https:"==window.location.protocol?"wss://"+window.location.hostname+(window.location.hostname.indexOf("yf")>-1?":544/miru":":443/miru"):"ws://"+window.location.hostname+":643/miru",a={language_type:0,account:t,passwd:"",os:1,net:this.getNetworkType().type,name:this.getNetworkType().name,bundle_id:"**********",device_token:"token"+this.code_temp,sessid:e,vid:"0001",vkey:"DcWP670PNfCtPIETQk03lEzbt6qRDRDy"};this.$websocket.initWebSocket("",n),this.checkWebsocket(a),this.$bus.$once("cli_miru_login_rsp",function(e){if(console.log("缓存登录响应:",e),sessionStorage.wsServer=n,"number"==typeof e)return console.log("缓存登录失败，清除缓存并使用账号密码登录"),i.handleLoginFailure(),void i.toLinkWsLogin(n);console.log("缓存登录成功"),i.handleLoginSuccess(e)})},toLinkWsLogin:function(e){var t=this,i={language_type:0,account:this.loginForm.username.trim(),passwd:this.$md5(this.loginForm.password),os:1,net:this.getNetworkType().type,name:this.getNetworkType().name,bundle_id:"**********",device_token:"token"+this.code_temp,sessid:"",vid:"0001",vkey:"DcWP670PNfCtPIETQk03lEzbt6qRDRDy"};this.$websocket.initWebSocket("",e),this.checkWebsocket(i),this.$bus.$once("cli_miru_login_rsp",function(i){if(console.log("账号密码登录响应:",i),sessionStorage.wsServer=e,"number"==typeof i)return console.log("账号密码登录失败"),t.handleLoginFailure(),t.$websocket.closeWebsocket(),t.loading=!1,void t.$refs.captchaImg.generateCaptcha();console.log("账号密码登录成功"),t.handleLoginSuccess(i)})},handleLoginFailure:function(){console.log("登录失败，清除sessid和account缓存"),c.default.clearSessid(!1),sessionStorage.removeItem("token"),sessionStorage.removeItem("username"),sessionStorage.removeItem("account_type"),sessionStorage.removeItem("account_alias"),sessionStorage.removeItem("videoUrl"),this.$store.commit("SET_TOKEN",""),console.log("缓存清除完成")},getNetworkType:function(){var e=navigator.userAgent,t=e.match(/NetType\/\w+/)?e.match(/NetType\/\w+/)[0]:"NetType/other",i=1;switch(t=t.toLowerCase().replace("nettype/","")){case"wifi":i=1,name="wifi";break;case"5g":i=5,name="5g";break;case"4g":i=4,name="4g";break;case"3g":case"3gnet":i=3,name="3g";break;case"2g":i=2,name="2g";break;default:i=10,name="wired"}return{type:i,name:name}},setupCrossTabLoginListener:function(){var e=this;this.$bus.$on("autoLoginFromAnotherTab",function(t){if(console.log("Received auto login event from another tab, sessid:",t),e.loading)console.log("Login already in progress, ignoring auto login event");else{var i=c.default.getAccount();i?(console.log("Starting auto login with sessid from another tab"),e.autoLoginWithSessid(t,i)):console.log("No account found for auto login, ignoring event")}})}}}},vOrs:function(e,t,i){"use strict";i.r(t);var n=i("VR5q"),a=i.n(n);for(var o in n)"default"!==o&&function(e){i.d(t,e,function(){return n[e]})}(o);t.default=a.a},vjVj:function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.decryption=function(e){var t=n.default.enc.Hex.parse(e),i=n.default.enc.Base64.stringify(t);return n.default.AES.decrypt(i,a,{mode:n.default.mode.ECB,padding:n.default.pad.Pkcs7}).toString(n.default.enc.Utf8)},t.encryption=function(e){return n.default.AES.encrypt(e,a,{mode:n.default.mode.ECB,padding:n.default.pad.Pkcs7}).ciphertext.toString()},t.decryptionKey=function(e,t,i){var a=n.default.enc.Utf8.parse(t),o=function(e){var t=0,i=e.length;if(i%2!=0)return null;i/=2;for(var n=new Array,a=0;a<i;a++){var o=e.substr(t,2);n.push(String.fromCharCode(parseInt(o,16))),t+=2}return n}(n.default.AES.decrypt(e,a,{mode:n.default.mode.ECB,padding:n.default.pad.Pkcs7}).toString());return o.slice(o.length-i).join("")};var n=function(e){return e&&e.__esModule?e:{default:e}}(i("NFKh"));var a=n.default.enc.Utf8.parse("8NONwyJtHesysWpM")},vpN3:function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default={name:"Page404",computed:{message:function(){return"网管说这个页面你不能进......"}}}},w1r0:function(e,t,i){},wAo7:function(e,t,i){"use strict";i.r(t);var n=i("4pdN"),a=i("jF/l");for(var o in a)"default"!==o&&function(e){i.d(t,e,function(){return a[e]})}(o);i("AMyg");var s=i("KHd+"),r=Object(s.a)(a.default,n.a,n.b,!1,null,"c8a70580",null);r.options.__file="src\\components\\SvgIcon\\index.vue",t.default=r.exports},wRDK:function(e,t,i){"use strict";var n=i("Ktri");i.n(n).a},"wo/l":function(e,t,i){"use strict";var n=i("rYlX");i.n(n).a},y4PM:function(e,t,i){},yK6b:function(e,t,i){},ywvi:function(e,t,i){"use strict";var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-dialog",{staticClass:"tipDialog",attrs:{title:e.title,visible:e.dialogVisible,"append-to-body":"","before-close":e.handleCancelIcon},on:{"update:visible":function(t){e.dialogVisible=t},close:e.hideDialog}},[i("section",[i("div",{staticClass:"content"},[i("div",[i("p",{staticClass:"tip"},[e._v(e._s(e.tip))])])])]),e._v(" "),i("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{staticClass:"tipCancelBtn",attrs:{type:"info"},on:{click:e.hideDialog}},[e._v(e._s(e.$t("cancel")))]),e._v(" "),i("el-button",{staticClass:"tipSubmitBtn",attrs:{loading:e.loading,type:"primary"},on:{click:e.submit}},[e._v(e._s(e.$t("sure")))])],1)])},a=[];n._withStripped=!0,i.d(t,"a",function(){return n}),i.d(t,"b",function(){return a})},zGwZ:function(e,t,i){e.exports=i.p+"static/img/401.089007e.gif"},zfL3:function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default={mgtPlat:"MIRU",mgtPlatTip:"Система Видеонаблюдения в Реальном Времени",scanCodeToLogIn:"Сканируйте код для входа",scanCodeTip:"Используйте приложение, привязанное к устройству, чтобы отсканировать код для входа",qrcodeExpired:"QR-код недействителен",username:"Имя пользователя",password:"Пароль",passwordTip1:"Пожалуйста, введите ваш пароль",rememberPwd:"Запомнить пароль",pleaseEnterAccount:"Пожалуйста, введите номер вашего аккаунта",pleaseEnterCorrectPwd:"Пароль должен содержать от 6 до 128 символов!",logIn:"Войти",account:"Аккаунт",accountTip:"Вводите 6-20 цифр или букв.",pleaseEnterCorrectPwdLen:"Длина пароля должна быть не менее 6 символов",pleaseEnterCorrectPwd18:"Пароль должен содержать от 6 до 18 символов!",verificationCode:"Код подтверждения",verificationCodeTip:"Пожалуйста, введите код подтверждения.",verificationCodeError:"Ошибка проверочного кода",home:"Главная",deviceManagement:"Управление устройствами",userManagement:"Управление пользователями",systemSetting:"Настройки системы",operationLogs:"Журналы операций",quitTitle:"Выйти",quitTip:"Вы уверены, что хотите выйти?",submit:"Отправить",confirm:"Подтвердить",sure:"Подтвердить",cancel:"Отмена",search:"Поиск",inquire:"Поиск",nodata:"Нет данных",cannotEmpty:"Не может быть пустым",operate:"Действие",modify:"Редактировать",export:"Экспорт",edit:"Редактировать",delete:"Удалить",sureToDelete:"Вы уверены, что хотите удалить?",deleteSuccess:"Успешно удалено",addSuccess:"Успешно добавлено",addFail:"Не удалось добавить",remove:"Удалить",sureToRemove:"Вы уверены, что хотите удалить?",removeSuccess:"Успешно удалено",editSuccess:"Успешно отредактировано",tip:"Подсказка",noMore:"Больше нет",getLogSuccess:"Журнал успешно получен",all:"Все",modifySuccess:"Успешно изменено",noData:"Нет данных",refresh:"Обновить",save:"Сохранить",to:"До",startDate:"Дата начала",endDate:"Дата окончания",pleaseSelect:"Пожалуйста, выберите",errCode120:"Неизвестная ошибка",errCode121:"Ошибка базы данных",errCode122:"Время сеанса истекло",errCode123:"Ошибка формата сообщения",errCode124:"Превышен лимит скорости сообщений, пожалуйста, контролируйте разумную скорость потока (100 сообщений в секунду)",errCode125:"Неверные параметры",errCode126:"Произошла ошибка при вызове API робота",errCode127:"Ошибка vid vkey",errCode128:"Вызов API робота возвращает ошибку данных",errCode129:"Не выполнен вход, необходимо войти для выполнения этого действия",errCode161:"Неверный аккаунт или пароль",errCode165:"Аккаунт уже вошел в другой устройство",errCode200:"CID не существует",errCode202:"Псевдоним устройства уже существует",errCode203:"Устройство не привязано",errCode204:"Устройство уже привязано к другому аккаунту",errCode205:"Коды проверки устройства не совпадают",errCode206:"Устройство привязано к самому себе",errCode214:"Устройство уже привязано к другому приложению",errCode215:"Устройство не в сети",errCode290:"Случайный код не существует",errCode291:"Срок действия случайного кода истек",errCode292:"Неверный статус",errCode401:"Пожалуйста, войдите снова",errCode500:"Ошибка сервера",errCode1000:"Ошибка запроса",errCode1001:"Ошибка запроса",errCode1002:"Доступ запрещен",errCode1004:"Сбой подключения",errCode1100:"Дубликат названия",errCode1101:"Аккаунт уже существует",errCode1102:"Текущий пароль неверен",errCode2008:"Во время форматирования SD-карты",errCode2011:"Не удалось прочитать SD-карту",errCode2030:"Историческое видео с SD-карты было прочитано",errCode2031:"Не удалось прочитать историческое видео с SD-карты",errCode2032:"Не удалось прочитать карту исторического видео с SD-карты",timeout:"Сетевая ошибка",networkError:"Сетевая ошибка",allGroup:"Все группы",noDevice:"Нет устройств",online:"В сети",offline:"Не в сети",deviceLoading:"Загрузка списка устройств···",videoLoading:"Загрузка видео···",failPlay:"Не удалось воспроизвести",deviceOffline:"Устройство не в сети",noHistoricalVideo:"У устройства нет исторического видео",noOssVideo:"У устройства нет облачного хранилища",retry:"Повторить",incall:"В звонке···",screen1:"1-экран",screens4:"4-экран",screens9:"9-экран",screens16:"16-экран",screens25:"25-экран",liveVideo:"Прямая трансляция",cloudStorage:"Облачное хранилище",playback:"Воспроизведение",todayVideoOver:"Видео на сегодня закончилось",movementDetected:"Обнаружено движение",soundDetected:"Обнаружен звук",humanDetected:"Обнаружен человек",noAudioEnabled:"Разрешение на микрофон не включено",alarmMessages:"Сообщения тревоги",noMessages:"Нет сообщений",chooseDevice:"Пожалуйста, выберите устройство",exceedMax:"Достигнуто максимальное количество текущих зрителей",previousPage:"Предыдущая страница",nextPage:"Следующая страница",carousel:"Карусель",refreshList:"Обновить список",refreshListTipCon:"Вы уверены, что хотите обновить список?",refreshListTipCon1:"Примечание: После обновления списка будет воспроизведен первый экран устройств.",refreshSuccess:"Обновлено успешно",rebootDevice:"Перезагрузить устройство",rebootDeviceTipCon:"Вы уверены, что хотите перезагрузить устройство?",scrolledToEnd:"Уже прокручено до конца",clickSingleDeviceTip:"Состояние карусели не позволяет нажимать на устройство.",addGroup:"Добавить группу",deviceGroupLoading:"Загрузка списка групп устройств···",addDevice:"Добавить устройство",strategicAttributes:"Стратегические атрибуты",bulkDelete:"Массовое удаление",status:"Статус",cid:"CID устройства",versionNumber:"Номер версии",deviceNickname:"Псевдоним устройства",group:"Группа",strategy:"Стратегия",editGroup:"Редактировать группу",groupName:"Название группы",groupNumber:"Номер группы",authorizedUser:"Авторизованный пользователь",groupNameLimit:"Ограничение длины 20 символов",delGroup:"Удалить группу",editDevice:"Редактировать устройство",sn:"CID устройства",snEnter:"Пожалуйста, введите CID",snEnterTip:"Пожалуйста, введите действительный 12-значный код CID",deviceVerificationCode:"Код проверки устройства",deviceCodeEnter:"Пожалуйста, введите код проверки",deviceNameEnter:"Пожалуйста, введите псевдоним устройства",deleteDevice:"Удалить устройство",bulkDelDeviceTip:"Пожалуйста, отметьте устройства, которые вы хотите удалить",device:"Устройство",deviceSearchTip:"CID устройства/Псевдоним устройства",delGroupHasDeviceTip:"В группе есть устройства, поэтому её нельзя удалить",nickName:"Псевдоним",mark:"Отметка",addUser:"Добавить пользователя",resetPassword:"Сбросить пароль",inputLengthLimit:"Ограничение длины {limit} символов",editUser:"Редактировать пользователя",deleteUser:"Удалить пользователя",sureToReset:'Вы уверены, что хотите сбросить пароль на "123456"?',bulkDelUserTip:"Пожалуйста, отметьте пользователей, которых вы хотите удалить",userLimitTip:"Ограничение на 5 пользователей",modifyPassword:"Изменить пароль",passwordNotMatching:"Пароли не совпадают",oldPassword:"Текущий пароль",newPassword:"Новый пароль",checkPassword:"Подтвердите пароль",passwordSame:"Новый пароль совпадает со старым паролем",operationTime:"Время операции",operationType:"Тип операции",editDeviceGroup:"Редактировать группу устройства",operationContent:"Содержание операции",saveVideo:"Сохранить видео",time:"время",startTime:"Время начала",endTime:"Время окончания",empty:"Очистить",timerangeexceeds:"Выбранный диапазон превышает ограничение. Пожалуйста, удалите видео в управлении видео или выберите заново!",novideo:"В выбранном временном диапазоне видео не найдено, выберите заново!",videomanagement:"Управление видео",selectgroup:"Пожалуйста, выберите группу",allselect:"Выделить все",download:"Скачать",downloading:"Загрузка...",downloadSuccess:"Загрузка успешна",downloadFailed:"Загрузка не удалась",saveVideoSuccess:"Сохранить видео успешно",timeLimit:"Максимальное время сохранения видео составляет 100 минут",Uploading:"Загрузка файла",warnings:"Предупреждение!",ok:"Хорошо",cancelVideoSave:"Отменить сохранение видео",cancelVideoSaveTip:"Вы уверены, что хотите отменить сохранение видео?",loading:"загрузка...",minute:"минута",permissionLevel:"Уровень доступа",manager:"Администратор",guest:"Гость"}},zoDt:function(e,t,i){e.exports=i.p+"static/img/deviceOff.604db5c.png"}},[["Vtdi","runtime","chunk-elementUI","chunk-libs"]]]);