# 重登后存储更新测试说明

## 修改概述

在autoRelogin mixin中添加了`updateStorageAfterRelogin`方法，确保重登成功后重新设置所有必要的存储数据，防止刷新后丢失缓存导致其他tab无法自动登录。

## 新增功能

### 1. updateStorageAfterRelogin方法
```javascript
updateStorageAfterRelogin(response) {
  // 更新store和sessionStorage中的token
  this.$store.commit('SET_TOKEN', response.body.sessid)
  setToken(response.body.sessid)
  
  // 重新设置sessionStorage中的用户信息
  sessionStorage.account_type = response.body.account_type
  sessionStorage.username = response.body.account
  sessionStorage.account_alias = response.body.alias
  sessionStorage.videoUrl = JSON.stringify(response.body.srs_http)
  
  // 使用tabManager保存sessid和account到localStorage供其他Tab共享
  tabManager.setSessid(response.body.sessid, response.body.account)
}
```

### 2. 存储数据对比

#### 登录成功时设置的数据（login/index.vue）：
- `sessionStorage.account_type` = response.body.account_type
- `sessionStorage.username` = response.body.account  
- `sessionStorage.account_alias` = response.body.alias
- `this.$store.commit('SET_TOKEN', response.body.sessid)`
- `setToken(response.body.sessid)` → `sessionStorage.token`
- `sessionStorage.videoUrl` = JSON.stringify(response.body.srs_http)
- `tabManager.setSessid(response.body.sessid, response.body.account)` → localStorage

#### 重登成功时设置的数据（autoRelogin mixin）：
- ✅ `sessionStorage.account_type` = response.body.account_type
- ✅ `sessionStorage.username` = response.body.account  
- ✅ `sessionStorage.account_alias` = response.body.alias
- ✅ `this.$store.commit('SET_TOKEN', response.body.sessid)`
- ✅ `setToken(response.body.sessid)` → `sessionStorage.token`
- ✅ `sessionStorage.videoUrl` = JSON.stringify(response.body.srs_http)
- ✅ `tabManager.setSessid(response.body.sessid, response.body.account)` → localStorage

**结论：重登时的存储设置与登录时完全一致**

## 解决的问题

### 1. 防止其他Tab无法自动登录
- **问题**：刷新页面后，localStorage中的sessid和account可能不是最新的
- **解决**：重登成功后调用`tabManager.setSessid()`更新localStorage
- **效果**：其他Tab可以使用最新的sessid进行自动登录

### 2. 确保sessionStorage数据完整性
- **问题**：重登前可能只更新了token，其他用户信息可能丢失
- **解决**：重登成功后重新设置所有用户相关的sessionStorage数据
- **效果**：页面刷新后用户信息保持完整

### 3. 保持store状态同步
- **问题**：重登后store中的token可能与sessionStorage不同步
- **解决**：同时更新store和sessionStorage中的token
- **效果**：确保应用状态一致性

## 测试步骤

### 1. 单Tab测试
1. 正常登录系统
2. 访问任意页面（如设备管理）
3. 刷新页面
4. 检查Console输出，应该看到：
   ```
   更新重登后的存储数据 {sessid: "xxx", account: "xxx", ...}
   存储数据更新完成: {sessid: "xxx", account: "xxx", ...}
   ```
5. 检查开发者工具的Application面板：
   - sessionStorage应包含最新的token、username、account_type等
   - localStorage应包含最新的sessid和account

### 2. 多Tab测试
1. 打开两个Tab，都登录到系统
2. 在Tab1中刷新页面（触发重登）
3. 在Tab2中刷新页面
4. 验证Tab2能够正常自动登录（不需要重新输入密码）
5. 检查两个Tab的localStorage数据是否一致

### 3. 存储数据验证
在Console中执行以下命令验证数据：
```javascript
// 检查sessionStorage
console.log('sessionStorage:', {
  token: sessionStorage.token,
  username: sessionStorage.username,
  account_type: sessionStorage.account_type,
  account_alias: sessionStorage.account_alias,
  videoUrl: sessionStorage.videoUrl
})

// 检查localStorage
console.log('localStorage:', {
  sessid: localStorage.getItem('tab_shared_sessid'),
  account: localStorage.getItem('tab_shared_account')
})
```

## 关键改进点

1. **完整性**：重登后的存储设置与初次登录完全一致
2. **跨Tab共享**：使用tabManager确保localStorage数据在所有Tab间共享
3. **状态同步**：同时更新store和sessionStorage，保持状态一致
4. **日志记录**：添加详细的Console输出，便于调试和验证

这些改进确保了刷新后的重登机制不仅能恢复当前Tab的登录状态，还能为其他Tab提供正确的缓存数据，实现真正的多Tab自动登录功能。
