<template>
  <div class="operationLogsBox">
    <div class="operationLogsLayout">
      <div class="contentBox">
        <div class="filterBox">
          <!-- 日期筛选 -->
          <div class="timeFilter filterItem">
            <span>{{ $t("operationTime") }}</span>
            <el-date-picker
              v-model="operationTime"
              type="daterange"
              range-separator="~"
              :start-placeholder="$t('startDate')"
              :end-placeholder="$t('endDate')"
            >
            </el-date-picker>
          </div>
          <!-- 操作类型 -->
          <div class="operationType filterItem">
            <span>{{ $t("operationType") }}</span>
            <el-select
              style="width: 400px"
              v-model="operationType"
              :placeholder="$t('pleaseSelect')"
              @change="onChangeSelect"
            >
              <el-option
                v-for="item in operationTypeRange"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </div>
          <!-- 搜索按钮 -->
          <el-button
            type="primary"
            class="tipSubmitBtn"
            icon="el-icon-search"
            @click="search()"
          >
            {{ $t("search") }}
          </el-button>
        </div>

        <div class="logsBox">
          <el-table
            v-loading="list_loading"
            :data="logsList"
            :highlight-current-row="true"
            stripe
          >
            <el-table-column type="index" align="center" label="#" width="60" />
            <el-table-column
              v-for="(l, index) in listTitle"
              :key="index"
              :prop="l.name"
              :label="l.title"
              :width="l.width"
              :column-key="l.name"
            >
              <template slot-scope="scope">
                <section
                  :title="scope.row[scope.column.columnKey]"
                  v-html="handleColumnShow(scope.row[scope.column.columnKey])"
                />
              </template>
            </el-table-column>
            <template slot="empty">
              <el-empty
                :image="require('@/assets/img/empty.png')"
                :image-size="65"
              />
            </template>
          </el-table>
          <pagination
            v-show="total > 20"
            :total="total"
            :page.sync="listQuery.page"
            :limit.sync="listQuery.limit"
            class="text-right mar_top_0"
            @pagination="handlePaginationChange"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { handleColumnShow } from '@/utils/index'
import { getLogs } from '@/api/logs'
import { formatTime } from '../../utils/formatDate';
import Pagination from '@/components/Pagination'
import autoReloginMixin from '@/mixins/autoRelogin'

export default {
  name: 'logs',
  mixins: [autoReloginMixin],
  components: {
    Pagination,
  },
  data () {
    return {
      operationTime: [],
      operationType: 0,
      operationTypeRange: [
        {
          value: 0,
          label: this.$t('all')
        },
        {
          value: 1,
          label: this.$t('logIn')
        },
        {
          value: 2,
          label: this.$t('editDeviceGroup')
        },
        {
          value: 3,
          label: this.$t('addUser')
        },
        {
          value: 4,
          label: this.$t('editUser')
        },
        {
          value: 5,
          label: this.$t('deleteUser')
        },
        {
          value: 6,
          label: this.$t('resetPassword')
        },
        {
          value: 7,
          label: this.$t('modifyPassword')
        },
      ],

      list_loading: false,
      listTitle: [
        {
          title: this.$t('operationTime'),
          name: 'operationTime',
          width: 240
        },
        {
          title: this.$t('account'),
          name: 'account',
          width: 200
        },
        {
          title: this.$t('operationType'),
          name: 'operationType',
          width: 240
        }, {
          title: this.$t('operationContent'),
          name: 'operationContent',
        }],
      logsList: [],

      total: 0,
      listQuery: {
        limit: 20,
        page: 1
      },
    }
  },
  created () {
    const endTime = new Date()
    const startTime = new Date(endTime)
    startTime.setDate(endTime.getDate() - 6);

    this.operationTime = [startTime, endTime]

    // 调用mixin的自动重登逻辑
    this.handleAutoRelogin()
  },
  methods: {
    handleColumnShow,

    // 重写mixin的回调方法 - 重登成功后执行
    onReloginSuccess(response) {
      console.log('operationLogs页面重登成功')
      this.search()
    },

    // 重写mixin的回调方法 - websocket准备就绪后执行
    onWebsocketReady() {
      console.log('operationLogs页面websocket准备就绪')
      // 如果已经通过重登成功加载过数据，就不要重复加载
      if (!this.isReloginCompleted) {
        this.search()
      }
    },

    // 搜索
    search () {


      let startTime;
      let endTime;
      try {
        startTime = new Date(this.operationTime[0])
        startTime.setHours(0, 0, 0, 0)
        endTime = new Date(this.operationTime[1])
        endTime.setHours(0, 0, 0, 0)
        endTime.setDate(endTime.getDate() + 1)
      } catch (err) {
        endTime = new Date()
        startTime = new Date(endTime)
        startTime.setDate(endTime.getDate() - 6);

        this.operationTime = [startTime, endTime]
      }
      const reqData = {
        begin_time: Math.round(startTime.valueOf() / 1000),
        end_time: Math.round(endTime.valueOf() / 1000),
        operate_type: this.operationType,
        offset: (this.listQuery.page - 1) * this.listQuery.limit,
        limit: this.listQuery.limit
      }
      this.list_loading = true
      getLogs(reqData)
        .then(res => {
          if (res.code === 200) {
            const list = res.datas
            this.logsList = list.map(i => {
              const time = formatTime(i.time, 1)

              let typeText = ''
              const type = i.operate_type
              if (type === 1) typeText = this.$t('logIn')
              else if (type === 2) typeText = this.$t('editDeviceGroup')
              else if (type === 3) typeText = this.$t('addUser')
              else if (type === 4) typeText = this.$t('editUser')
              else if (type === 5) typeText = this.$t('deleteUser')
              else if (type === 6) typeText = this.$t('resetPassword')
              else if (type === 7) typeText = this.$t('modifyPassword')

              let contentText = ''
              const content = JSON.parse(i.operate_content)
              for (let i in content) {
                if (i === 'account') {
                  contentText += `${this.$t('account')}: ${content[i]} `
                } else if (i === 'alias') {
                  contentText += `${this.$t('nickName')}: ${content[i]} `
                } else if (i === 'group_name') {
                  contentText += `${this.$t('groupName')}: ${content[i]} `
                }
              }

              return {
                account: i.account,
                operationTime: time,
                operationType: typeText,
                operationContent: contentText,
              }
            })

            this.total = res.count

            console.log(this.total, this.listQuery)
          }
        }).catch(err => {

        }).finally(() => {
          this.list_loading = false
        })
    },
    // 分页变动
    handlePaginationChange (val) {
      this.listQuery = {
        page: val.page,
        limit: val.limit
      }
      this.search()
    },
    // 修改分类
    onChangeSelect (val) {
      this.listQuery = {
        page: 1,
        limit: 20
      }
      this.search()
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
div,
text {
  color: var(--color-neutral-100);
  font-size: var(--font-size-normal);
}
.logsBox {
  border-radius: 6px;
  background: var(--color-neutral-600);
}
.operationLogsBox {
  height: calc(100vh - 75px);
  background: var(--color-neutral-700);
  box-sizing: border-box;
  overflow-y: scroll;
}
.contentBox {
  max-width: 1260px;
  margin: auto;
  padding: 24px 20px 24px;
}
.filterBox {
  display: flex;
  gap: 20px;
}
.filterItem {
  display: flex;
  gap: 10px;
  align-items: center;
}
.logsBox {
  margin-top: 20px;
  border-radius: 6px;
}
</style>
