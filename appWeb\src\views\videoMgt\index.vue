<template>
  <div class="videoMgtBox">
    <div class="videoMgtLayout">
      <!-- 筛选栏 -->
      <div class="filterBox">
        <!-- 分组下拉筛选 -->
        <div class="operationType filterItem">
          <span>{{ $t("group") }}</span>
          <el-select
            v-model="group"
            :placeholder="$t('pleaseSelect')"
            style="width: 400px"
            @change="onChangeSelect"
          >
            <el-option
              v-for="item in groupList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>

        <!-- 输入框 -->
        <div class="operationType filterItem">
          <span>{{ $t("device") }}</span>
          <el-input
            v-model="searchText"
            :placeholder="$t('deviceSearchTip')"
            maxlength="100"
            style="width: 200px"
            @keyup.enter.native="search()"
          />
        </div>

        <!-- 时间选择 -->
        <div class="operationType filterItem">
          <span>{{ $t("time") }}</span>
          <el-date-picker
            v-model="operationTime"
            :start-placeholder="$t('startDate')"
            :end-placeholder="$t('endDate')"
            type="daterange"
            range-separator="~"
            @change="onTimeChange"
          />
        </div>

        <!-- 搜索按钮 -->
        <el-button
          type="primary"
          class="tipSubmitBtn"
          icon="el-icon-search"
          @click="search()"
        >
          {{ $t("search") }}
        </el-button>
      </div>
      <!-- 筛选栏 end -->

      <!-- 操作栏 -->
      <div class="operateBox">
        <!-- 全选 -->
        <el-checkbox
          v-model="checkedAll"
          :indeterminate="isHasSomeChecked"
          class="selectAll"
          @change="handleAllChecked"
        >{{ $t("allselect") }}</el-checkbox
        >
        <!-- 下载 -->
        <el-button class="tipSubmitBtn" @click="downloadVideos()">{{
          $t("download")
        }}</el-button>
        <!-- 删除 -->
        <el-button class="tipDeleteBtn" @click="deleteRows()">{{
          $t("delete")
        }}</el-button>
      </div>
      <!-- 操作栏 end -->

      <!-- 列表 -->
      <div class="rowListBox">
        <!-- 加载状态显示 -->
        <div v-if="isLoading" class="loadingStateBox">
          <div class="loadingContent">
            <img
              src="@/assets/img/loading.gif"
              alt="加载中..."
              class="loadingImage"
            >
            <p class="loadingText">{{ $t("loading") }}</p>
          </div>
        </div>
        <!-- 空状态显示 -->
        <div v-else-if="rowList.length === 0" class="emptyStateBox">
          <el-empty
            :image="require('@/assets/img/empty.png')"
            :image-size="65"
          />
        </div>
        <!-- 列表项 -->
        <div v-else class="rowListBox">
          <div v-for="item in rowList" :key="item.videoId" class="rowItem">
            <el-checkbox
              v-model="item.checked"
              @change="handleRowChecked"
            />
            <div class="rowImageBox" @click="handlePlayVideo(item)">
              <img v-if="item.imageUrl" :src="item.imageUrl" class="rowImage" >
              <img
                v-else
                class="rowIcon2"
                src="../../assets/img/noDataLogo.png"
              >
              <!-- <img class="rowIcon" src="../../assets/img/home_logo.png"/> -->
              <img
                class="rowPlayIcon"
                src="../../../src/assets/img/playIcon.png"
                alt=""
              >
            </div>
            <div class="rowInfo">
              <div class="rowDesc">
                <span class="rowName">{{ item.name }}</span>
                <span class="rowGroup">{{ item.group }}</span>
              </div>
              <div class="rowTime">{{ item.timeText }}</div>
            </div>
            <div style="flex: 1"/>
            <div class="rowOperate">
              <el-button
                type="text"
                class="rowOperateBtn download"
                @click="downloadVideos(item)"
              >{{ $t("download") }}</el-button
              ><span class="rowOperateSplit">|</span
              ><el-button
                type="text"
                class="rowOperateBtn delete"
                @click="deleteRows(item)"
              >{{ $t("delete") }}</el-button
              >
            </div>
          </div>
        </div>
      </div>
      <!-- 列表 end -->

      <!-- 分页 -->
      <div class="paginationBox">
        <pagination
          v-show="total > 10"
          :total="total"
          :page.sync="listQuery.page"
          :limit.sync="listQuery.limit"
          class="text-right mar_top_0"
          @pagination="handlePaginationChange"
        />
      </div>
      <!-- 分页 end -->
    </div>

    <!-- 视频播放弹窗 -->
    <el-dialog
      :title="currentVideo.name"
      :visible.sync="showVideoDialog"
      :before-close="closeVideoDialog"
      width="800px"
    >
      <div class="video-container">
        <video
          ref="videoPlayer"
          :src="currentVideo.videoUrl"
          controls
          preload="metadata"
          autoplay
          @loadstart="onVideoLoadStart"
          @canplay="onVideoCanPlay"
          @error="onVideoError"
          @keydown.esc="closeVideoDialog"
        >
          Your browser does not support the video tag.
        </video>
      </div>
      <div class="video-info">
        <p class="video-group">{{ currentVideo.group }}</p>
        <p class="video-time">
          {{ currentVideo.timeText }}
        </p>
      </div>
    </el-dialog>

    <tip-dialog
      ref="tipDialog"
      :title="dialogTitle"
      :tip="dialogTip"
      :request="tipRequest"
      :params="tipParams"
      req-type="http"
    />
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import { handleColumnShow } from '@/utils/index'
import { getVideoList, deleteVideos } from '@/api/videoMgt'
import { getDeviceGroupList } from '@/api/deviceMgt'
import { formatTime } from '../../utils/formatDate'
import TipDialog from '@/components/TipDialog'
import autoReloginMixin from '@/mixins/autoRelogin'

export default {
  name: 'VideoMgt',
  components: {
    Pagination,
    TipDialog
  },
  mixins: [autoReloginMixin],
  data() {
    return {
      groupList: [
        {
          value: '-1',
          label: this.$t('all')
        }
      ],
      group: '-1',
      searchText: '',
      operationTime: [],

      allChecked: false,
      isHasSomeChecked: false,
      checkedList: [],

      rowList: [],

      total: 0,
      listQuery: {
        page: 1,
        limit: 10
      },

      // 视频播放弹窗相关
      showVideoDialog: false,
      currentVideo: {
        name: '',
        group: '',
        timeText: '',
        videoUrl: '',
        videoId: ''
      },
      videoLoading: false,

      dialogTitle: '',
      dialogTip: '',
      tipRequest: '',
      tipParams: {},

      isLoading: false
    }
  },
  computed: {
    checkedAll: {
      get() {
        return this.allChecked
      },
      set(val) {
        this.allChecked = val
        this.rowList.map(item => {
          item.checked = val
        })
        this.isHasSomeChecked = false
        this.checkedList = val ? this.rowList : []
      }
    }
  },
  created() {
    const endTime = new Date()
    const startTime = new Date(endTime)
    startTime.setDate(endTime.getDate() - 6)

    this.operationTime = [startTime, endTime]

    // 调用mixin的自动重登逻辑
    this.handleAutoRelogin()
  },
  mounted() {
    // 添加全局键盘事件监听
    document.addEventListener('keydown', this.handleKeyDown)
  },
  beforeDestroy() {
    // 移除全局键盘事件监听
    document.removeEventListener('keydown', this.handleKeyDown)
  },
  methods: {
    // 重写mixin的回调方法 - 重登成功后执行
    onReloginSuccess(response) {
      console.log('videoMgt页面重登成功')
      this.getGroup()
      this.search()
    },

    // 重写mixin的回调方法 - websocket准备就绪后执行
    onWebsocketReady() {
      console.log('videoMgt页面websocket准备就绪')
      this.getGroup()
      this.search()
    },

    getGroup() {
      getDeviceGroupList().then(res => {
        if (res.code === 200) {
          const gList = [
            {
              value: '-1',
              label: this.$t('all')
            }
          ]

          res.datas && res.datas.map((item, index) => {
            gList.push({
              value: item.group_id,
              label: item.name
            })
          })

          this.groupList = gList
        }
      })
    },
    handleKeyDown(e) {
      // ESC键关闭弹窗
      if (e.key === 'Escape' && this.showVideoDialog) {
        this.closeVideoDialog()
      }
    },
    onChangeSelect(val) {
      this.searchText = ''
      this.search()
    },
    onTimeChange(val) {
    },
    search() {
      let startTime
      let endTime
      try {
        startTime = new Date(this.operationTime[0])
        startTime.setHours(0, 0, 0, 0)
        endTime = new Date(this.operationTime[1])
        endTime.setHours(0, 0, 0, 0)
        endTime.setDate(endTime.getDate() + 1)
      } catch (err) {
        endTime = new Date()
        startTime = new Date(endTime)
        startTime.setDate(endTime.getDate() - 6)

        this.operationTime = [startTime, endTime]
      }
      const reqData = {
        begin_time: Math.round(startTime.valueOf() / 1000),
        end_time: Math.round(endTime.valueOf() / 1000),
        offset: (this.listQuery.page - 1) * this.listQuery.limit,
        limit: this.listQuery.limit,
        group_id: this.group != '-1' ? this.group : '',
        text: this.searchText
      }
      this.isLoading = true
      this.checkedList = []
      this.allChecked = false
      getVideoList(reqData).then(res => {
        if (res.code === 200) {
          this.total = res.count
          const list = res.datas.map(i => {
            return {
              checked: false,
              name: i.camera_name,
              group: i.group_name,
              timeText: formatTime(i.begin_time) + ' ~ ' + formatTime(i.end_time),
              imageUrl: i.image_url,
              videoUrl: i.file_url,
              videoId: i.video_id
            }
          })
          this.rowList = list
        }
      }).catch(error => {
        console.error('获取视频列表失败:', error)
      }).finally(() => {
        this.isLoading = false
      })
    },
    handleAllChecked(val) {
      this.allChecked = val
    },
    handleRowChecked(val) {
      const hasChecked = this.rowList.some(item => item.checked)
      const hasUnChecked = this.rowList.some(item => !item.checked)
      this.isHasSomeChecked = hasChecked && hasUnChecked
      this.checkedList = this.rowList.filter(item => item.checked)
      this.allChecked = hasChecked && !hasUnChecked
    },
    handlePaginationChange(val) {
      this.listQuery = {
        page: val.page,
        limit: val.limit
      }
      this.search()
    },
    handlePlayVideo(item) {
      this.currentVideo = {
        name: item.name,
        group: item.group,
        timeText: item.timeText,
        videoUrl: item.videoUrl,
        videoId: item.videoId
      }
      this.showVideoDialog = true
      this.videoLoading = true

      // 确保弹窗获得焦点以支持ESC键关闭
      this.$nextTick(() => {
        const overlay = document.querySelector('.video-dialog-overlay')
        if (overlay) {
          overlay.focus()
        }
      })
    },

    closeVideoDialog() {
      this.showVideoDialog = false
      this.videoLoading = false
      // 停止视频播放
      if (this.$refs.videoPlayer) {
        this.$refs.videoPlayer.pause()
        this.$refs.videoPlayer.currentTime = 0
      }
      // 清空当前视频信息
      this.currentVideo = {
        name: '',
        group: '',
        timeText: '',
        videoUrl: '',
        videoId: ''
      }
    },

    onVideoLoadStart() {
      this.videoLoading = true
    },

    onVideoCanPlay() {
      this.videoLoading = false
    },

    onVideoError(e) {
      this.videoLoading = false
    },

    async downloadVideos(item) {
      if (this.checkedList.length <= 0 && !item) {
        this.$message.error(this.$t('pleaseSelect'))
        return
      }

      const videoUrls = item ? [item.videoUrl] : this.checkedList.map(item => item.videoUrl)
      const videoNames = item ? [item.name] : this.checkedList.map(item => item.name)

      // 显示下载进度提示
      const loadingMessage = this.$message({
        message: this.$t('downloading') || '正在下载...',
        type: 'info',
        duration: 0
      })

      try {
        // 并发下载所有视频
        await Promise.all(videoUrls.map((url, index) => this.downloadSingleVideo(url, videoNames[index])))
        loadingMessage.close()
        this.$message.success(this.$t('downloadSuccess') || '下载成功')
      } catch (error) {
        loadingMessage.close()
        console.error('下载失败:', error)
        this.$message.error(this.$t('downloadFailed') || '下载失败')
      }
    },

    async downloadSingleVideo(url, fileName) {
      try {
        // 使用 fetch 获取视频文件
        const response = await fetch(url)
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }

        // 获取文件内容
        const blob = await response.blob()

        // 创建下载链接
        const downloadUrl = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = downloadUrl

        // 设置文件名，如果没有扩展名则添加 .mp4
        let downloadFileName = url.split('/').pop()
        if (!downloadFileName.includes('.')) {
          downloadFileName += '.mp4'
        }
        a.download = downloadFileName

        // 触发下载
        document.body.appendChild(a)
        a.click()

        // 清理资源
        document.body.removeChild(a)
        window.URL.revokeObjectURL(downloadUrl)
      } catch (error) {
        console.error('下载单个视频失败:', error)
        throw error
      }
    },

    deleteRows(item) {
      if (this.checkedList.length <= 0 && !item) {
        this.$message.error(this.$t('pleaseSelect'))

        return
      }

      this.dialogTitle = this.$t('delete')
      this.dialogTip = this.$t('sureToDelete')
      this.tipRequest = 'deleteVideos'
      this.tipParams = {
        video_id: item ? [item.videoId] : this.checkedList.map(item => item.videoId)
      }
      this.$refs.tipDialog.show('deleteRows')
        .then(res => {
          if (res) {
            deleteVideos(this.tipParams)
              .then(res => {
                if (res.code === 200) {
                  setTimeout(() => {
                    this.$refs.tipDialog.hideDialog()
                    this.$message.success(this.$t('deleteSuccess'))
                    this.search()
                  }, 500)
                }
              })
          }
        })
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.videoMgtBox {
  height: calc(100vh - 75px);
  background: var(--color-neutral-700);
  overflow-y: scroll;
}
.videoMgtLayout {
  max-width: 1260px;
  box-sizing: border-box;
  margin: auto;
  padding: 24px 20px 24px;
}
.operateBox {
  margin-bottom: 30px;

  .tipSubmitBtn {
    margin-left: 30px;
  }
  .tipCancelBtn {
    margin-left: 20px;
  }
  .el-button {
    padding: 0 31px;
    font-size: var(--font-size-small);
  }
}
.filterBox {
  margin-bottom: 30px;
}
.rowListBox {
  display: flex;
  flex-direction: column;
  gap: 2px;
  .emptyStateBox {
    background-color: var(--color-neutral-600);
    padding: 60px 20px;
    text-align: center;
    border-radius: 6px;
  }

  .loadingStateBox {
    background-color: var(--color-neutral-600);
    padding: 60px 20px;
    text-align: center;
    border-radius: 6px;

    .loadingContent {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      .loadingImage {
        width: 65px;
        height: 65px;
        margin-bottom: 16px;
      }

      .loadingText {
        margin: 0;
        font-size: 14px;
        color: var(--color-neutral-200);
      }
    }
  }
  .rowItem {
    display: flex;
    align-items: center;
    background-color: var(--color-neutral-600);
    padding: 0 40px 0 20px;
    min-height: 100px;
    .rowImageBox {
      width: 110px;
      height: 60px;
      margin-left: 20px;
      background-color: black;
      border-radius: 6px;
      overflow: hidden;
      flex-shrink: 0;
      position: relative;
      transition: all 0.2s ease;
      &:hover {
        opacity: 0.9;
        transform: scale(1.05);
        cursor: pointer;
      }
      .rowImage {
        width: 100%;
        height: 100%;
      }
      &:active {
        transform: scale(0.9);
        opacity: 0.7;
      }
      .rowPlayIcon {
        width: 30px;
        height: 30px;
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
      }
      .rowIcon {
        width: 40px;
        height: 40px;
        position: absolute;
        left: 10%;
        top: 20%;
        transform: translate(-50%, -50%);
      }
      .rowIcon2 {
        width: 75px;
        height: 75px;
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
      }
    }
    .rowInfo {
      margin-left: 24px;
      .rowDesc {
        margin-bottom: 28px;
        .rowName {
          margin-right: 67px;
          font-size: var(--font-size-regular);
          font-weight: bold;
        }
        .rowGroup {
          font-size: var(--font-size-regular);
        }
      }
      .rowTime {
        color: var(--color-neutral-200);
      }
    }
    .rowOperate {
      .rowOperateSplit {
        color: var(--color-neutral-400);
        margin-left: 20px;
        margin-right: 20px;
        user-select: none;
      }
      .download {
        color: #86c8ff;
        &:hover {
          opacity: 0.8;
        }
      }
      .delete {
        color: var(--color-negative-400);
        &:hover {
          opacity: 0.8;
        }
      }
    }
  }
}

.paginationBox {
  display: flex;
  justify-content: flex-end;
  background-color: var(--color-neutral-600);
  margin-top: 2px;
}

/* 视频播放弹窗样式 */
.video-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  backdrop-filter: blur(4px);
  animation: fadeIn 0.3s ease-out;
  outline: none;
}

.video-container {
  position: relative;
  background: #000;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.video-container video {
  width: 100%;
  height: auto;
  max-height: 60vh;
  outline: none;
}

.video-info {
  padding: 16px 0;
  background: var(--color-neutral-600);
  border-top: 1px solid var(--color-neutral-400);
}

.video-group,
.video-time {
  margin: 0;
  padding: 4px 0;
  font-size: 14px;
  color: var(--color-neutral-200);
}

.video-group {
  margin-bottom: 8px;
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .video-info {
    padding: 12px 20px;
  }

  .video-container {
    min-height: 250px;
  }
}
</style>
