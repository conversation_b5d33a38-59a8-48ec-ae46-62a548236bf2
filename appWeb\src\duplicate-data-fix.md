# 重复数据问题修复说明

## 问题描述

在实现自动重登机制后，设备组出现重复显示的问题。一个组会显示两个同样的数据，如：
- 全部分组
- PEC00
- PEC00 (重复)

## 问题原因分析

### 1. 重复调用数据加载方法

在autoRelogin mixin中，我们定义了两个回调方法：
- `onReloginSuccess()` - 重登成功后执行
- `onWebsocketReady()` - websocket准备就绪后执行

在某些场景下，这两个方法都会被调用，导致数据加载方法被执行两次：

```javascript
// deviceMgt页面
onReloginSuccess(response) {
  this.getGroup() // 第一次调用
},

onWebsocketReady() {
  this.getGroup() // 第二次调用 - 导致重复
}
```

### 2. 数据累加而非重置

在videoMgt页面的`getGroup()`方法中，没有重置groupList数组就直接push新数据：

```javascript
// 问题代码
getGroup() {
  getDeviceGroupList().then(res => {
    res.datas && res.datas.map(item => {
      this.groupList.push(item) // 直接push，没有清空原有数据
    })
  })
}
```

## 修复方案

### 1. 防止重复调用数据加载

在`onWebsocketReady()`方法中添加条件判断，避免重复加载：

```javascript
onWebsocketReady() {
  // 如果已经通过重登成功加载过数据，就不要重复加载
  if (!this.isReloginCompleted) {
    this.getGroup()
  }
}
```

**修复的页面**：
- `deviceMgt/index.vue`
- `userMgt/index.vue`
- `videoMgt/index.vue`
- `operationLogs/index.vue`

### 2. 重置数据数组

在videoMgt页面的`getGroup()`方法中，先重置groupList再添加新数据：

```javascript
getGroup() {
  // 重置groupList，防止重复数据
  this.groupList = [
    {
      value: "-1",
      label: this.$t("all"),
    },
  ]
  
  getDeviceGroupList().then(res => {
    if (res.code === 200) {
      res.datas && res.datas.map(item => {
        this.groupList.push({
          value: item.group_id,
          label: item.name
        })
      })
    }
  })
}
```

## 修复逻辑说明

### 场景1：正常登录
1. 用户正常登录进入页面
2. `onWebsocketReady()`被调用
3. 由于`isReloginCompleted`为false，正常加载数据

### 场景2：页面刷新（重登）
1. 页面刷新，触发自动重登
2. 重登成功后，`onReloginSuccess()`被调用，加载数据
3. `isReloginCompleted`被设置为true
4. 随后`onWebsocketReady()`被调用，但由于条件判断，不会重复加载数据

### 场景3：重登失败
1. 页面刷新，触发自动重登
2. 重登失败，`isReloginCompleted`保持为false
3. 如果websocket连接建立，`onWebsocketReady()`会正常加载数据

## 测试验证

### 1. 设备管理页面测试
1. 正常登录，访问设备管理页面
2. 检查设备组列表是否正常显示（无重复）
3. 刷新页面
4. 检查设备组列表是否仍然正常（无重复）

### 2. 视频管理页面测试
1. 正常登录，访问视频管理页面
2. 检查分组下拉列表是否正常显示（无重复）
3. 刷新页面
4. 检查分组下拉列表是否仍然正常（无重复）

### 3. 其他页面测试
对userMgt和operationLogs页面进行类似测试，确保数据不会重复加载。

## 预防措施

1. **状态管理**：利用mixin中的`isReloginCompleted`状态来控制数据加载
2. **数据重置**：在数据加载方法中先重置数组再添加新数据
3. **条件判断**：在可能重复调用的方法中添加条件判断

这些修复确保了自动重登机制不会导致数据重复显示，保持了用户界面的正确性和一致性。
