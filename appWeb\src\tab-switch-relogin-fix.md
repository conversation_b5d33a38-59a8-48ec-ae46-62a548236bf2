# Tab切换触发重登问题修复

## 问题描述

用户反馈：登录后在未切过其他tab的情况下，每切一个新tab都会触发一次刷新（重登）。

## 问题原因

### 原始设计问题

`sessionStorage.home`最初是专门为video.vue设计的，用来标识页面是否是刷新操作：

```javascript
// video.vue的逻辑
if (sessionStorage.home && sessionStorage.home === '1') {
  console.log('刷新')
  // 执行重登逻辑
} else {
  console.log('登录进来')
  // 正常初始化
}

// mounted时设置标志
mounted() {
  sessionStorage.home = '1'
}
```

### 问题产生过程

1. **用户登录**：访问第一个页面，`sessionStorage.home`为`undefined`，走正常逻辑
2. **页面mounted**：设置`sessionStorage.home = '1'`
3. **切换新tab**：新页面检测到`sessionStorage.home === '1'`，误认为是刷新操作
4. **触发重登**：新页面执行重登逻辑，导致不必要的重登

### 根本原因

所有页面共享了`sessionStorage.home`这个标志，但这个标志的语义是"页面是否被刷新"，而不是"是否有页面被加载过"。

## 修复方案

### 核心思路

使用更准确的方式检测页面刷新，而不是依赖共享的sessionStorage标志。

### 实现方案

#### 1. 新的刷新检测逻辑

```javascript
isPageRefresh() {
  // 方法1：使用performance.navigation API
  if (performance.navigation) {
    return performance.navigation.type === 1  // TYPE_RELOAD = 1
  }
  
  // 方法2：使用performance.getEntriesByType
  if (performance.getEntriesByType) {
    const navigationEntries = performance.getEntriesByType('navigation')
    if (navigationEntries.length > 0) {
      return navigationEntries[0].type === 'reload'
    }
  }
  
  // 方法3：备选方案 - 页面特定标识
  const currentRoute = this.$route.path
  const wasLoaded = sessionStorage.getItem(`page_loaded_${currentRoute}`)
  return wasLoaded === '1' && sessionStorage.username
}
```

#### 2. 页面特定的标识

```javascript
mounted() {
  // 为非video页面设置页面特定的标识
  if (this.$route.path !== '/video') {
    const currentRoute = this.$route.path
    sessionStorage.setItem(`page_loaded_${currentRoute}`, '1')
  }
}
```

#### 3. 保护video.vue的原有逻辑

video.vue继续使用`sessionStorage.home`，不受影响。

## 修复效果

### 修复前的行为

```
用户登录 → 访问页面A → mounted设置sessionStorage.home='1' 
→ 切换到页面B → 检测到sessionStorage.home='1' → 误认为刷新 → 触发重登
```

### 修复后的行为

```
用户登录 → 访问页面A → mounted设置page_loaded_/pageA='1'
→ 切换到页面B → 检测navigation.type !== 1 → 识别为正常导航 → 不触发重登
```

### 真正刷新时的行为

```
用户在页面A按F5刷新 → navigation.type === 1 → 识别为刷新 → 触发重登 ✓
```

## 测试验证

### 测试场景1：正常Tab切换

1. 登录系统
2. 访问设备管理页面
3. 切换到用户管理页面
4. 切换到系统设置页面

**预期结果**：不应该触发重登，Console不应该出现"刷新 - 开始自动重登"

### 测试场景2：真正的页面刷新

1. 登录系统
2. 访问任意管理页面
3. 按F5刷新页面

**预期结果**：应该触发重登，Console出现"刷新 - 开始自动重登"

### 测试场景3：video.vue页面

1. 登录系统
2. 访问video页面
3. 刷新video页面

**预期结果**：video.vue的重登逻辑正常工作，不受影响

## 技术细节

### Performance Navigation API

- `performance.navigation.type === 0`：正常导航（点击链接、地址栏输入等）
- `performance.navigation.type === 1`：页面刷新（F5、Ctrl+R等）
- `performance.navigation.type === 2`：前进/后退按钮

### Navigation Timing API（现代浏览器）

```javascript
const navigationEntries = performance.getEntriesByType('navigation')
navigationEntries[0].type // 'navigate', 'reload', 'back_forward', 'prerender'
```

### 兼容性考虑

提供了三层检测机制：
1. 现代API优先
2. 备选API
3. 基于sessionStorage的备选方案

## 总结

这个修复：

1. **解决了Tab切换误触发重登的问题**
2. **保持了真正刷新时的重登功能**
3. **保护了video.vue的原有逻辑**
4. **提供了更准确的刷新检测机制**

现在用户可以正常在不同管理页面间切换，不会再出现不必要的重登操作。
